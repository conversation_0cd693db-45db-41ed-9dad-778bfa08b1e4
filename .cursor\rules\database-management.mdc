---
description: 
globs: 
alwaysApply: false
---
---
description: "ODDLUCK项目数据库管理规范和指南。"
auto_attachments: ["**/database/**/*.py", "**/database/**/*.sql", "**/api/models.py"]
---

# ODDLUCK数据库管理指南

## 数据库结构
项目使用PostgreSQL数据库，主要数据模型包括：

| 表名 | 说明 | 主要字段 |
|------|------|---------|
| **continents** | 大洲信息 | continent_id, continent_name, continent_name_en |
| **countries** | 国家信息 | country_id, country_name, country_logo, continent_id |
| **leagues** | 联赛信息 | id, league_id, sub_league_id, league_type, league_short_name, current_season, total_rounds, current_round |
| **cups** | 杯赛信息 | id, cup_id, cup_short_name, match_type, current_season |
| **teams** | 球队信息 | id, team_id, team_name_simp, team_name_cant, team_name_en, team_logo |
| **matches** | 比赛信息 | id, match_id, league/cup, season, round, match_time, home_team_id, away_team_id, full_score, half_score |
| **odds** | 赔率信息 | id, match_id, odds_detail_id, bookmaker_id, home_win, draw, away_win, update_time |
| **spider_logs** | 爬虫日志 | task_id, spider_type, league, season, round, status, progress, message, log_content |

## 模型关系
- 大洲(Continent) ← 国家(Country) ← 联赛(League)/杯赛(Cup)
- 联赛(League)/杯赛(Cup) ← 比赛(Match)
- 球队(Team) ← 比赛(Match) (主队/客队)
- 比赛(Match) ← 赔率(Odds)

## 本地数据库设置
- 本地数据库配置脚本位于 [database/local_setup/](mdc:database/local_setup)
- 主数据库操作脚本为 [database/db.py](mdc:database/db.py)

## 云端数据库设置
- Neon数据库配置脚本位于 [database/neon_setup/](mdc:database/neon_setup)
- SSL配置修复脚本为 [database/fix_ssl_settings.py](mdc:database/fix_ssl_settings.py)

## 最佳实践
- 数据库连接信息应使用环境变量或.env文件配置，避免硬编码
- 爬虫对数据库的访问应通过连接池进行，减少连接开销
- 大批量数据更新应使用批处理方式进行，提高性能
- 数据库模式变更需同步更新两个项目的相关代码
- 使用Django ORM进行数据库操作，保持模型定义与数据库结构一致