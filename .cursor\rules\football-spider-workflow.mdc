---
description: 
globs: 
alwaysApply: false
---
---
description: "ODDLUCK项目足球数据爬虫的工作流程和命令参考。"
auto_attachments: ["**/football_spider/**/*.py", "**/football_spider/README.md"]
---

# 足球数据爬虫工作流

## 功能特点

- 支持抓取大洲、国家、联赛、球队的基本信息
- 支持抓取五大联赛（英超、德甲、西甲、意甲、法甲）的比赛数据
- 完整的数据层次结构：大洲 -> 国家 -> 联赛 -> 球队 -> 比赛
- 获取联赛基本信息（联赛ID、名称、赛季等）
- 获取球队信息（球队ID、名称、粤语名称等）
- 获取比赛信息（比赛时间、比分等）
- 获取主要博彩公司（Bet365、立博、威廉希尔）的欧赔数据
- **支持增量更新**，自动跳过已爬取且无变化的数据，提高效率
- **数据库连接池**，减少数据库连接开销，提高并发性能
- **批量处理**，减少数据库操作次数，大幅提升处理速度
- **IP代理轮换**，降低被目标网站限制的风险
- **定时爬取**，支持在比赛前1天、6小时、2小时和15分钟自动触发爬取任务

## 环境准备
- 进入虚拟环境: 
  ```bash
  cd football_spider
  .\spider_venv\Scripts\activate
  ```

## 核心组件
- **爬虫实现**: [football_spider/spiders/](mdc:football_spider/football_spider/spiders) 目录
- **数据处理管道**: [football_spider/pipelines.py](mdc:football_spider/football_spider/pipelines.py)
- **增量更新逻辑**: [football_spider/incremental_pipeline.py](mdc:football_spider/football_spider/incremental_pipeline.py)
- **数据库连接池**: [football_spider/db_pool.py](mdc:football_spider/football_spider/db_pool.py)

## 爬虫运行命令参考

### 1. 爬取基础数据

```bash
# 爬取大洲信息
scrapy crawl continent

# 爬取国家信息
scrapy crawl country
```

### 2. 爬取杯赛信息

```bash
# 爬取杯赛数据
scrapy crawl cup
# 指定赛季
# scrapy crawl cup -a season=2024-2025
```

### 3. 爬取联赛信息

```bash
# 爬取指定联赛的特定赛季信息
scrapy crawl league -a league=意甲 -a season=2024-2025

# 也可以使用联赛ID
scrapy crawl league -a league=36 -a season=2024-2025
```

### 4. 爬取球队信息

```bash
# 爬取指定联赛特定赛季下的所有球队信息
scrapy crawl team -a league=英超 -a season=2024-2025

# 也可以使用联赛ID
scrapy crawl team -a league=36 -a season=2024-2025
```

### 5. 爬取比赛信息

```bash
# 爬取英超2024-2025赛季所有比赛
scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="36"

# 爬取特定轮次（如第29轮）
scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="36" -a target_round="29"

# 爬取特定球队（如曼城）的比赛
scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="36" -a target_team_name="曼城"

# 爬取杯赛比赛
scrapy crawl match -a competition_type="cup" -a season="2024-2025" -a source_competition_id="103"

# 爬取杯赛特定阶段比赛
scrapy crawl match -a competition_type="cup" -a season="2024-2025" -a source_competition_id="103" -a target_round="联赛阶段"
```

### 6. 使用增量更新模式

```bash
# 使用增量更新模式爬取英超2024-2025赛季的比赛和赔率数据
scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="36" -a incremental=true
```

### 7. 定时爬取设置

```bash
# 1. 启动Django/Daphne后端 (包含定时任务调度器)
cd oddluck_dev
.\oddluck_venv\Scripts\activate
cd backend
python start_daphne.py 

# 2. 启动Scrapyd服务
cd ../../football_spider 
scrapyd  # 运行在 http://127.0.0.1:6800/

# 3. 部署爬虫到Scrapyd服务
cd football_spider 
scrapyd-deploy default

# 4. 手动触发单场比赛的爬取 (测试用)
cd ../oddluck_dev
.\oddluck_venv\Scripts\activate
cd backend
python manage.py scrape_match <match_id>
```

## 支持的联赛

| 联赛名称 | 联赛ID |
|---------|-------|
| 英超 | 36 |
| 意甲 | 34 |
| 西甲 | 31 |
| 德甲 | 8 |
| 法甲 | 11 |

## 性能优化

- **数据库连接池**：使用连接池管理数据库连接，避免频繁创建和销毁连接
- **批量处理**：对odds、leagues、teams、matches数据进行批量插入/更新
- **查询缓存**：对频繁查询的数据进行缓存，减少数据库查询次数
- **增量更新**：只处理有变化的数据，避免不必要的数据库操作
- **异步处理**：使用异步方式处理网络请求和数据处理，提高并发性能
- **IP代理轮换**：降低被目标网站限制的风险

## 配置说明

在 `settings.py` 文件中可以配置以下参数：

```python
# 数据库连接池配置
DB_POOL_MIN_CONNECTIONS = 1
DB_POOL_MAX_CONNECTIONS = 20

# 批处理配置
BATCH_PROCESSING_ENABLED = True
BATCH_SIZE_ODDS = 100
BATCH_SIZE_LEAGUES = 10
BATCH_SIZE_TEAMS = 20
BATCH_SIZE_MATCHES = 50

# 查询缓存配置
QUERY_CACHE_ENABLED = True
QUERY_CACHE_TTL = 300  # 缓存300秒
```
```