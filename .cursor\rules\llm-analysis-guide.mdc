---
description: 
globs: 
alwaysApply: false
---
---
description: "ODDLUCK项目LLM分析功能的配置和使用指南。"
auto_attachments: ["**/llm_analysis/**/*.py"]
---

# LLM分析功能指南

## 模型选择与特点
项目支持以下嵌入模型：
- **HuggingFace模型**: 
  - 模型名称: `BAAI/bge-large-zh-v1.5`
  - 特点: 支持离线使用，适合生产环境
  - 语言支持: 中文优化
- **Ollama模型**: 
  - 可选模型: `nomic-embed-text` 或 `bge-large-zh`
  - 特点: 通过Ollama API使用，适合开发环境
  - 部署方式: 本地轻量级部署

## 模型下载与配置
- HuggingFace模型下载脚本: [backend/llm_analysis/download_model.py](mdc:oddluck_dev/backend/llm_analysis/download_model.py)
- Ollama测试脚本: [backend/llm_analysis/test_ollama.py](mdc:oddluck_dev/backend/llm_analysis/test_ollama.py)

## 模型缓存设置
- 通过环境变量设置HF_HOME指定缓存路径
  ```bash
  # Windows
  set HF_HOME=D:\my_cache\huggingface
  ```
- 或在代码中设置环境变量
  ```python
  import os
  os.environ['HF_HOME'] = '/path/to/cache/huggingface'
  ```

## 重要注意事项
- 模型下载必须在oddluck_venv虚拟环境中进行
- 首次使用模型可能需要较长时间下载（约500MB-1GB）
- 确保有足够的磁盘空间存储模型文件
- 在生产环境中优先使用已缓存的模型减少加载时间