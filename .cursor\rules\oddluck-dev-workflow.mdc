---
description: 
globs: 
alwaysApply: false
---
---
description: "ODDLUCK项目网站开发的工作流程和相关命令。"
auto_attachments: ["**/oddluck_dev/**/*.py", "**/oddluck_dev/frontend/**/*.vue"]
---

# ODDLUCK网站开发工作流

## 环境准备
- 进入虚拟环境: 
  ```bash
  cd oddluck_dev
  .\oddluck_venv\Scripts\activate
  ```

## 后端开发 (Django)
- **核心文件**:
  - API实现: [api/views.py](mdc:oddluck_dev/backend/api/views.py)
  - 数据模型: [api/models.py](mdc:oddluck_dev/backend/api/models.py)
  - URL路由: [api/urls.py](mdc:oddluck_dev/backend/api/urls.py)
  
- **启动后端服务**:
  ```bash
  cd oddluck_dev
  .\oddluck_venv\Scripts\activate
  cd backend
  python manage.py runserver
  ```

## 前端开发 (Vue)
- **核心目录**:
  - 组件: [frontend/src/components/](mdc:oddluck_dev/frontend/src/components)
  - 页面: [frontend/src/views/](mdc:oddluck_dev/frontend/src/views)
  - 样式: [frontend/src/styles/](mdc:oddluck_dev/frontend/src/styles)
  
- **启动前端服务**:
  ```bash
  cd oddluck_dev/frontend
  npm run dev
  ```

## 重要注意事项
- 前端样式修改必须在styles目录下进行，不要在Vue文件中直接添加CSS
- API接口变更时需同步更新前端服务调用
- 数据模型变更需要执行迁移命令:
  ```bash
  python manage.py makemigrations
  python manage.py migrate
  ```