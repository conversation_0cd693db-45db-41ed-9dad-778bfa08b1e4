---
description: 
globs: 
alwaysApply: true
---
---
description: "ODDLUCK项目的目录结构和重要约定说明。"
auto_attachments: ["**/*.py", "**/*.vue", "**/*.js"]
---

# ODDLUCK 项目结构说明

## 核心目录结构
oddluck/
├── football_spider/ # 数据采集与爬虫
│ ├── football_spider/ # 爬虫实现
│ │ ├── spiders/ # 各类爬虫
│ │ ├── pipelines.py # 数据处理管道
│ │ └── ...
│ ├── spider_venv/ # 爬虫专用虚拟环境
│ └── scrapy.cfg # 爬虫配置入口
│
├── oddluck_dev/ # 网站开发
│ ├── frontend/ # 前端 (Vue)
│ │ ├── src/
│ │ │ ├── components/
│ │ │ ├── views/
│ │ │ └── styles/ # 集中样式管理
│ │ └── ...
│ ├── backend/ # 后端 (Django)
│ │ ├── api/
│ │ ├── manage.py
│ │ └── ...
│ └── oddluck_venv/ # 网站开发虚拟环境
│
└── database/ # 数据库相关
├── local_setup/ # 本地数据库配置
└── neon_setup/ # 云端数据库配置

## 重要约定
- 数据采集相关操作仅限于 football_spider 目录
- 网站开发相关操作仅限于 oddluck_dev 目录
- 前端样式全部集中在 styles 目录，禁止在 .vue 文件内添加 CSS
- 依赖安装必须在对应虚拟环境内进行

