---
description: 
globs: 
alwaysApply: true
---
---
description: "ODDLUCK项目的工作规范和重要约定。"
auto_attachments: ["**/*.py", "**/*.vue", "**/*.js", "**/*.ts"]
---

# ODDLUCK项目工作规范

## 目录与操作规范
- **数据采集**：所有数据获取相关操作必须在 `football_spider` 目录下进行
- **网站开发**：所有网站构建相关操作必须在 `oddluck_dev` 目录下进行

## 命令执行规范
- 执行命令时禁止使用 `&&` 连接符，必须分步执行每个命令
- 创建虚拟环境前必须先获得确认，不可自动创建

## 虚拟环境使用规范
- 进入 `oddluck_venv` 虚拟环境的标准流程：
  ```bash
  cd oddluck_dev
  .\oddluck_venv\Scripts\activate
  ```
- 所有依赖安装必须在对应虚拟环境内进行：
  - 网站开发依赖安装在 `oddluck_venv` 中
  - 爬虫依赖安装在 `spider_venv` 中

## 前端开发规范
- 严格禁止在 `.vue` 文件内添加 CSS 样式
- 所有样式必须集中在 `styles` 目录下管理
