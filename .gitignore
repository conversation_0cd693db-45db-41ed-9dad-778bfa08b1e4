# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv
oddluck_venv/
spider_venv/
llm_spider_venv/
*.exe

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Scrapy
.scrapy

# Logs
*.log
logs/

# Database
*.sqlite3
*.db

# Local development settings
local_settings.py

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.env.local
.env.*.local

# MongoDB
data/
mongodb/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Vue.js / Frontend
dist/
dist-ssr/
coverage/
*.local
.env.*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Django / Backend
/oddluck_dev/backend/staticfiles/
/oddluck_dev/backend/media/
/oddluck_dev/backend/static/images/logos/*
!/oddluck_dev/backend/static/images/logos/default/
/oddluck_dev/backend/db.sqlite3
/oddluck_dev/backend/.env

# Project specific
*.pyc
.DS_Store
Thumbs.db 