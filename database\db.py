import psycopg2
from psycopg2.extras import DictCursor
from contextlib import contextmanager
import logging
import os
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class Database:
    def __init__(self, host=os.getenv('DB_HOST', 'localhost'),
                 port=int(os.getenv('DB_PORT', '5432')),
                 dbname=os.getenv('DB_NAME', 'oddluck'),
                 user=os.getenv('DB_USER', 'postgres'),
                 password=os.getenv('DB_PASSWORD', '888'),
                 sslmode=os.getenv('PGSSLMODE', 'disable')):
        self.conn_params = {
            'host': host,
            'port': port,
            'dbname': dbname,
            'user': user,
            'password': password,
            'sslmode': sslmode
        }
        self._conn = None
    
    @property
    def conn(self):
        if self._conn is None:
            try:
                logger.info(f"正在连接数据库: {self.conn_params['host']}:{self.conn_params['port']}/{self.conn_params['dbname']} (SSL: {self.conn_params['sslmode']})")
                self._conn = psycopg2.connect(**self.conn_params)
            except Exception as e:
                logger.error(f"数据库连接失败: {str(e)}")
                raise
        return self._conn
    
    @contextmanager
    def cursor(self):
        """上下文管理器，用于获取数据库游标"""
        try:
            cursor = self.conn.cursor(cursor_factory=DictCursor)
            yield cursor
            self.conn.commit()
        except Exception as e:
            self.conn.rollback()
            logger.error(f"Database error: {str(e)}")
            raise
        finally:
            cursor.close()
    
    def close(self):
        """关闭数据库连接"""
        if self._conn is not None:
            self._conn.close()
            self._conn = None
    
    def upsert_league(self, league_data):
        """插入或更新联赛信息"""
        sql = """
            INSERT INTO leagues (
                league_id, sub_league_id, league_type, league_name, 
                league_name_en, season, logo, total_rounds, current_round
            ) VALUES (
                %(league_id)s, %(sub_league_id)s, %(league_type)s, %(league_name)s,
                %(league_name_en)s, %(season)s, %(logo)s, %(total_rounds)s, %(current_round)s
            )
            ON CONFLICT (league_id, sub_league_id, season) DO UPDATE SET
                league_name = EXCLUDED.league_name,
                league_name_en = EXCLUDED.league_name_en,
                logo = EXCLUDED.logo,
                total_rounds = EXCLUDED.total_rounds,
                current_round = EXCLUDED.current_round,
                updated_at = CURRENT_TIMESTAMP
            RETURNING id
        """
        with self.cursor() as cur:
            cur.execute(sql, league_data)
            return cur.fetchone()[0]
    
    def upsert_team(self, team_data):
        """插入或更新球队信息"""
        sql = """
            INSERT INTO teams (
                team_id, team_name, team_name_en, team_logo,
                league_id, sub_league_id, season
            ) VALUES (
                %(team_id)s, %(team_name)s, %(team_name_en)s, %(team_logo)s,
                %(league_id)s, %(sub_league_id)s, %(season)s
            )
            ON CONFLICT (team_id, league_id, sub_league_id) DO UPDATE SET
                team_name = EXCLUDED.team_name,
                team_name_en = EXCLUDED.team_name_en,
                team_logo = EXCLUDED.team_logo,
                season = EXCLUDED.season,
                updated_at = CURRENT_TIMESTAMP
            RETURNING id
        """
        with self.cursor() as cur:
            cur.execute(sql, team_data)
            return cur.fetchone()[0]
    
    def get_existing_leagues(self):
        """获取已存在的联赛信息"""
        sql = """
            SELECT league_id, sub_league_id, season
            FROM leagues
        """
        with self.cursor() as cur:
            cur.execute(sql)
            return {f"{row['league_id']}_{row['sub_league_id']}_{row['season']}"
                    for row in cur.fetchall()}
    
    def get_existing_teams(self):
        """获取已存在的球队信息"""
        sql = """
            SELECT team_id, league_id, sub_league_id
            FROM teams
        """
        with self.cursor() as cur:
            cur.execute(sql)
            return {f"{row['team_id']}_{row['league_id']}_{row['sub_league_id']}"
                    for row in cur.fetchall()} 