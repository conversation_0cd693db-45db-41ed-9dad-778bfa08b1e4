#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复PostgreSQL SSL连接配置问题
动态设置本地和云数据库的SSL模式
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def find_project_root():
    """查找项目根目录"""
    current_dir = Path(__file__).resolve().parent
    parent_dir = current_dir.parent
    
    # 检查当前目录是否为项目根目录
    if (parent_dir / "oddluck_dev").exists() and (parent_dir / "football_spider").exists():
        return parent_dir
    
    # 如果当前目录不是项目根目录，则返回父目录
    return parent_dir

def fix_ssl_settings():
    """修复SSL设置"""
    # 查找项目根目录
    project_root = find_project_root()
    logger.info(f"项目根目录: {project_root}")
    
    # 环境变量文件路径
    env_file = project_root / "database" / ".env"
    backend_env_file = project_root / "oddluck_dev" / ".env"
    
    if not env_file.exists():
        logger.error(f"环境变量文件不存在: {env_file}")
        return False
    
    # 加载环境变量
    load_dotenv(env_file)
    
    # 获取当前设置
    current_ssl_mode = os.getenv('SSL_MODE', 'require')
    local_ssl_mode = os.getenv('LOCAL_SSL_MODE', 'disable')
    
    logger.info(f"当前SSL模式: {current_ssl_mode}")
    logger.info(f"本地SSL模式: {local_ssl_mode}")
    
    # 修改文件内容
    try:
        # 读取当前内容
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 如果不存在LOCAL_SSL_MODE，添加它
        if 'LOCAL_SSL_MODE' not in content:
            content += f"\nLOCAL_SSL_MODE={local_ssl_mode}\n"
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"已添加LOCAL_SSL_MODE={local_ssl_mode}到{env_file}")
        
        # 复制到后端目录
        if backend_env_file.exists():
            with open(backend_env_file, 'r', encoding='utf-8') as f:
                backend_content = f.read()
            
            # 更新后端环境文件
            with open(backend_env_file, 'w', encoding='utf-8') as f:
                # 如果后端已有.env文件，确保它有正确的SSL设置
                if 'SSL_MODE' not in backend_content:
                    backend_content += f"\nSSL_MODE={current_ssl_mode}\n"
                if 'LOCAL_SSL_MODE' not in backend_content:
                    backend_content += f"\nLOCAL_SSL_MODE={local_ssl_mode}\n"
                f.write(backend_content)
                logger.info(f"已更新后端环境变量文件: {backend_env_file}")
        
        logger.info("环境变量已更新")
        return True
    except Exception as e:
        logger.error(f"更新环境变量时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    logger.info("开始修复SSL设置...")
    
    success = fix_ssl_settings()
    
    if success:
        logger.info("SSL设置修复成功!")
        logger.info("请记住:")
        logger.info("1. 本地连接使用SSL_MODE=disable")
        logger.info("2. 云数据库连接使用SSL_MODE=require")
        logger.info("3. 如果使用Django连接本地数据库，请确保设置了正确的环境变量")
    else:
        logger.error("SSL设置修复失败!")

if __name__ == "__main__":
    main() 