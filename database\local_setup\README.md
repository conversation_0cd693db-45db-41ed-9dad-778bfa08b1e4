# 重构数据库

## 创建一个符合oddluck_dev\backend\api\models.py的数据库
```bash
cd database/local_setup
.\create_db.ps1 oddluck postgres 你的实际密码
```

## 数据迁移
### 迁移django_apscheduler
```bash
python manage.py migrate django_apscheduler
```

### 迁移api
```bash
# 创建一个初始迁移，让Django知道当前数据库结构
cd oddluck_dev
.\oddluck_venv\Scripts\activate
cd ..\..\backend
python manage.py makemigrations api --empty

# 修改新生成的迁移文件-版本1，告诉Django您已经手动创建了这些表
from django.db import migrations

class Migration(migrations.Migration):
    initial = True
    
    dependencies = [
    ]
    
    operations = [
        migrations.RunSQL(
            "SELECT 1",  # 不执行任何实际操作的SQL
            None
        ),
    ]

# 修改新生成的迁移文件-版本2，告诉Django您已经手动创建了这些表
from django.db import migrations

class Migration(migrations.Migration):
    initial = True
    
    dependencies = [
            # 可能需要添加对其他应用(如auth)的依赖，如果模型中有外键指向它们
            # 例如: migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]
    
    operations = [
        # 告诉Django这些模型对应的表已经存在，不做任何操作
        migrations.SeparateDatabaseAndState(
            state_operations=[
                # 这里列出所有在models.py中定义的模型对应的CreateModel操作
                # Django makemigrations --empty 不会自动填充，但--fake需要知道状态
                # 可以先不加--empty运行一次makemigrations，复制CreateModel操作过来，然后删除生成的非空迁移文件，再创建空的
                # 或者，更简单的方式是直接用RunSQL占位
                    migrations.RunSQL("SELECT 1", "SELECT 1"), 
            ]
        )
    ]
    
    # 添加 managed = False 可能是另一种方式，但会阻止Django未来管理这些表
    # 或者直接使用下面的RunSQL也可以
    # operations = [ migrations.RunSQL("SELECT 1", "SELECT 1") ]


# 直接迁移或标记迁移为已应用
python manage.py makemigrations api
python manage.py migrate api
python manage.py migrate api --fake
```

### 迁移auth_api
```bash
python manage.py makemigrations auth_api
python manage.py migrate auth_api
```

### 迁移knowledge_api
```bash
# 创建一个初始迁移
cd oddluck_dev
.\oddluck_venv\Scripts\activate
cd ..\..\backend
python manage.py makemigrations knowledge_api --empty

# 修改新生成的迁移文件-版本1
from django.db import migrations

class Migration(migrations.Migration):
    initial = True
    
    dependencies = [
    ]
    
    operations = [
        migrations.RunSQL(
            "SELECT 1",  # 不执行任何实际操作的SQL
            None
        ),
    ]

# 修改新生成的迁移文件-版本2，告诉Django您已经手动创建了这些表
from django.db import migrations

class Migration(migrations.Migration):
    initial = True
    
    dependencies = [
            # 可能需要添加对其他应用(如auth)的依赖，如果模型中有外键指向它们
            # 例如: migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]
    
    operations = [
        # 告诉Django这些模型对应的表已经存在，不做任何操作
        migrations.SeparateDatabaseAndState(
            state_operations=[
                # 这里列出所有在models.py中定义的模型对应的CreateModel操作
                # Django makemigrations --empty 不会自动填充，但--fake需要知道状态
                # 可以先不加--empty运行一次makemigrations，复制CreateModel操作过来，然后删除生成的非空迁移文件，再创建空的
                # 或者，更简单的方式是直接用RunSQL占位
                    migrations.RunSQL("SELECT 1", "SELECT 1"), 
            ]
        )
    ]
    
    # 添加 managed = False 可能是另一种方式，但会阻止Django未来管理这些表
    # 或者直接使用下面的RunSQL也可以
    # operations = [ migrations.RunSQL("SELECT 1", "SELECT 1") ]

# 直接迁移或标记迁移为已应用
python manage.py makemigrations knowledge_api
python manage.py migrate knowledge_api
python manage.py migrate knowledge_api --fake
```

### 迁移llm_analysis
```bash
# 创建一个初始迁移
cd oddluck_dev
.\oddluck_venv\Scripts\activate
cd ..\..\backend
python manage.py makemigrations llm_analysis --empty

# 修改新生成的迁移文件-版本1
from django.db import migrations

class Migration(migrations.Migration):
    initial = True
    
    dependencies = [
    ]
    
    operations = [
        migrations.RunSQL(
            "SELECT 1",  # 不执行任何实际操作的SQL
            None
        ),
    ]

# 修改新生成的迁移文件-版本2，告诉Django您已经手动创建了这些表
from django.db import migrations

class Migration(migrations.Migration):
    initial = True
    
    dependencies = [
            # 可能需要添加对其他应用(如auth)的依赖，如果模型中有外键指向它们
            # 例如: migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]
    
    operations = [
        # 告诉Django这些模型对应的表已经存在，不做任何操作
        migrations.SeparateDatabaseAndState(
            state_operations=[
                # 这里列出所有在models.py中定义的模型对应的CreateModel操作
                # Django makemigrations --empty 不会自动填充，但--fake需要知道状态
                # 可以先不加--empty运行一次makemigrations，复制CreateModel操作过来，然后删除生成的非空迁移文件，再创建空的
                # 或者，更简单的方式是直接用RunSQL占位
                    migrations.RunSQL("SELECT 1", "SELECT 1"), 
            ]
        )
    ]
    
    # 添加 managed = False 可能是另一种方式，但会阻止Django未来管理这些表
    # 或者直接使用下面的RunSQL也可以
    # operations = [ migrations.RunSQL("SELECT 1", "SELECT 1") ]

# 直接迁移或标记迁移为已应用
python manage.py makemigrations llm_analysis
python manage.py migrate llm_analysis
python manage.py migrate llm_analysis --fake
```

### 迁移scheduler
```bash
python manage.py makemigrations scheduler
python manage.py migrate scheduler
```

### 数据库迁移所有应用
```bash
python manage.py migrate
```
## 重置自增序列id
```bash
# 在数据库sql编辑器执行命令
TRUNCATE TABLE leagues RESTART IDENTITY CASCADE;
```