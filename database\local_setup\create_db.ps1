# PowerShell script for creating and initializing PostgreSQL database
# Usage: .\create_db.ps1 [database_name] [username] [password] [host] [port]

# Default parameters
$dbName = if ($args[0]) { $args[0] } else { "oddluck" }
$username = if ($args[1]) { $args[1] } else { "postgres" }
$password = if ($args[2]) { $args[2] } else { "postgres" }
$dbHost = if ($args[3]) { $args[3] } else { "localhost" }  # 修改这里
$port = if ($args[4]) { $args[4] } else { "5432" }

# Color definitions
$GREEN = @{ ForegroundColor = "Green" }
$RED = @{ ForegroundColor = "Red" }
$YELLOW = @{ ForegroundColor = "Yellow" }

# Function: Check if command exists
function Check-Command {
    param($command)
    $exists = Get-Command $command -ErrorAction SilentlyContinue
    return $exists -ne $null
}

# Check if psql is installed
if (-not (Check-Command "psql")) {
    Write-Host "Error: psql command not found. Please make sure PostgreSQL is installed and added to PATH." @RED
    exit 1
}

# Print script information
Write-Host "==================================================" @GREEN
Write-Host "               PostgreSQL Database Setup          " @GREEN
Write-Host "==================================================" @GREEN
Write-Host "Database name: $dbName" @YELLOW
Write-Host "Username: $username" @YELLOW
Write-Host "Host: $dbHost" @YELLOW  # 修改这里
Write-Host "Port: $port" @YELLOW
Write-Host "==================================================" @GREEN

# Set environment variable for psql to use password
$env:PGPASSWORD = $password

try {
    # 1. Try to drop existing database
    Write-Host "Checking and dropping existing database..." @YELLOW
    psql -h $dbHost -p $port -U $username -d "postgres" -c "DROP DATABASE IF EXISTS $dbName WITH (FORCE);"  # 修改这里
    
    # 2. Create new database
    Write-Host "Creating new database: $dbName..." @YELLOW
    psql -h $dbHost -p $port -U $username -d "postgres" -c "CREATE DATABASE $dbName;"  # 修改这里
    
    # 3. Execute schema.sql to create table structure
    $schemaPath = Join-Path $PSScriptRoot "schema.sql"
    Write-Host "Executing schema.sql to create tables..." @YELLOW
    psql -h $dbHost -p $port -U $username -d $dbName -f $schemaPath  # 修改这里
    
    Write-Host "Database $dbName created and initialized successfully!" @GREEN
}
catch {
    Write-Host "Error creating database: $_" @RED
    exit 1
}
finally {
    # Clear environment variable
    Remove-Item Env:\PGPASSWORD
}