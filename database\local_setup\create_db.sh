#!/bin/bash

# Shell脚本用于创建和初始化PostgreSQL数据库
# 用法: ./create_db.sh [database_name] [username] [password] [host] [port]

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 默认参数
DB_NAME=${1:-"oddluck"}
USERNAME=${2:-"postgres"}
PASSWORD=${3:-"postgres"}
HOST=${4:-"localhost"}
PORT=${5:-"5432"}

# 检查psql是否安装
if ! command -v psql &> /dev/null; then
    echo -e "${RED}错误: psql命令未找到。请确保PostgreSQL已安装。${NC}"
    exit 1
fi

# 打印脚本信息
echo -e "${GREEN}=================================================="
echo -e "               PostgreSQL数据库创建脚本          "
echo -e "==================================================${NC}"
echo -e "${YELLOW}数据库名称: $DB_NAME"
echo -e "用户名: $USERNAME"
echo -e "主机: $HOST"
echo -e "端口: $PORT${NC}"
echo -e "${GREEN}==================================================${NC}"

# 设置环境变量以便psql使用密码
export PGPASSWORD=$PASSWORD

# 获取当前脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SCHEMA_PATH="$SCRIPT_DIR/schema.sql"

# 执行数据库操作
echo -e "${YELLOW}检查并删除已存在的数据库...${NC}"
psql -h $HOST -p $PORT -U $USERNAME -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME WITH (FORCE);"

if [ $? -ne 0 ]; then
    echo -e "${RED}删除数据库失败！${NC}"
    unset PGPASSWORD
    exit 1
fi

echo -e "${YELLOW}创建新数据库: $DB_NAME...${NC}"
psql -h $HOST -p $PORT -U $USERNAME -d postgres -c "CREATE DATABASE $DB_NAME;"

if [ $? -ne 0 ]; then
    echo -e "${RED}创建数据库失败！${NC}"
    unset PGPASSWORD
    exit 1
fi

echo -e "${YELLOW}执行schema.sql创建表结构...${NC}"
psql -h $HOST -p $PORT -U $USERNAME -d $DB_NAME -f "$SCHEMA_PATH"

if [ $? -ne 0 ]; then
    echo -e "${RED}创建表结构失败！${NC}"
    unset PGPASSWORD
    exit 1
fi

echo -e "${GREEN}数据库 $DB_NAME 创建并初始化成功！${NC}"

# 清除环境变量
unset PGPASSWORD 