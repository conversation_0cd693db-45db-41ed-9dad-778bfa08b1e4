#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建用户配置表的脚本
这个脚本会为每个Django用户创建一个对应的UserProfile记录
"""

import os
import django
import sys
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 设置Django环境
# 将路径指向oddluck_dev/backend目录
project_base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
backend_dir = os.path.join(project_base_dir, 'oddluck_dev', 'backend')
sys.path.append(backend_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# 导入必要的Django模块
from django.db import connection

# SQL语句
SQL = '''
-- 创建UserProfile表
CREATE TABLE IF NOT EXISTS auth_api_userprofile (
    id SERIAL PRIMARY KEY,
    avatar TEXT,
    bio TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    notification_match BOOLEAN NOT NULL DEFAULT TRUE,
    notification_odds BOOLEAN NOT NULL DEFAULT TRUE,
    notification_analysis BOOLEAN NOT NULL DEFAULT FALSE,
    user_id INTEGER NOT NULL UNIQUE,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth_user(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_auth_api_userprofile_user_id ON auth_api_userprofile(user_id);

-- 为已有用户创建档案记录
INSERT INTO auth_api_userprofile (user_id, notification_match, notification_odds, notification_analysis)
SELECT id, TRUE, TRUE, FALSE FROM auth_user
WHERE id NOT IN (SELECT user_id FROM auth_api_userprofile);

-- 创建更新触发器
CREATE OR REPLACE FUNCTION update_profile_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_auth_api_userprofile_updated_at ON auth_api_userprofile;
CREATE TRIGGER update_auth_api_userprofile_updated_at
    BEFORE UPDATE ON auth_api_userprofile
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
'''

def execute_sql():
    """执行SQL创建用户配置表"""
    try:
        logger.info("开始执行SQL...")
        with connection.cursor() as cursor:
            cursor.execute(SQL)
        logger.info("用户配置表创建成功！")
    except Exception as e:
        logger.error(f"执行SQL时出错: {str(e)}")
        return False
    return True

if __name__ == "__main__":
    execute_sql() 