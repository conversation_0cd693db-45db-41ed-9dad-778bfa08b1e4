-- Drop foreign key constraints first
ALTER TABLE IF EXISTS matches DROP CONSTRAINT IF EXISTS fk_matches_league;
ALTER TABLE IF EXISTS matches DROP CONSTRAINT IF EXISTS fk_matches_cup;
ALTER TABLE IF EXISTS matches DROP CONSTRAINT IF EXISTS fk_matches_home_team;
ALTER TABLE IF EXISTS matches DROP CONSTRAINT IF EXISTS fk_matches_away_team;
ALTER TABLE IF EXISTS odds DROP CONSTRAINT IF EXISTS fk_odds_match;
ALTER TABLE IF EXISTS teams DROP CONSTRAINT IF EXISTS teams_league_id_fkey;
ALTER TABLE IF EXISTS leagues DROP CONSTRAINT IF EXISTS fk_leagues_continent;
ALTER TABLE IF EXISTS leagues DROP CONSTRAINT IF EXISTS fk_leagues_country;
ALTER TABLE IF EXISTS cups DROP CONSTRAINT IF EXISTS fk_cups_continent;
ALTER TABLE IF EXISTS cups DROP CONSTRAINT IF EXISTS fk_cups_country;
ALTER TABLE IF EXISTS countries DROP CONSTRAINT IF EXISTS fk_countries_continent;
ALTER TABLE IF EXISTS spider_logs DROP CONSTRAINT IF EXISTS fk_spider_logs_matches;

-- Drop check constraints
ALTER TABLE IF EXISTS matches DROP CONSTRAINT IF EXISTS match_belongs_to_league_or_cup;

-- Drop triggers
DROP TRIGGER IF EXISTS update_matches_updated_at ON matches;
DROP TRIGGER IF EXISTS update_odds_updated_at ON odds;
DROP TRIGGER IF EXISTS update_teams_updated_at ON teams;
DROP TRIGGER IF EXISTS update_leagues_updated_at ON leagues;
DROP TRIGGER IF EXISTS update_cups_updated_at ON cups;
DROP TRIGGER IF EXISTS update_countries_updated_at ON countries;
DROP TRIGGER IF EXISTS update_continents_updated_at ON continents;
DROP TRIGGER IF EXISTS update_spider_logs_updated_at ON spider_logs;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Drop tables
DROP TABLE IF EXISTS odds CASCADE;
DROP TABLE IF EXISTS spider_logs CASCADE;
DROP TABLE IF EXISTS matches CASCADE;
DROP TABLE IF EXISTS teams CASCADE;
DROP TABLE IF EXISTS leagues CASCADE;
DROP TABLE IF EXISTS cups CASCADE;
DROP TABLE IF EXISTS countries CASCADE;
DROP TABLE IF EXISTS continents CASCADE;
DROP TABLE IF EXISTS odds_retry_queue CASCADE;

-- Create continents table
CREATE TABLE continents (
    continent_id INTEGER NOT NULL,
    continent_name VARCHAR(50) NOT NULL,
    continent_name_en VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (continent_id)
);

-- Create countries table
CREATE TABLE countries (
    country_id VARCHAR(50) NOT NULL,
    country_name VARCHAR(100) NOT NULL,
    country_logo VARCHAR(200),
    continent_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (country_id),
    FOREIGN KEY (continent_id) REFERENCES continents (continent_id)
);

-- Create leagues table
CREATE TABLE leagues (
    id SERIAL PRIMARY KEY,
    league_id VARCHAR(50) NOT NULL,
    sub_league_id VARCHAR(50),
    league_type VARCHAR(50) NOT NULL,
    league_short_name VARCHAR(100) NOT NULL,
    league_official_name_simp VARCHAR(200),
    league_official_name_cant VARCHAR(200),
    league_official_name_en VARCHAR(200),
    league_logo VARCHAR(200),
    available_seasons TEXT,
    current_season VARCHAR(50) NOT NULL,
    total_rounds INTEGER,
    current_round INTEGER,
    continent_id INTEGER,
    country_id VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (league_id, current_season, sub_league_id),
    FOREIGN KEY (continent_id) REFERENCES continents (continent_id),
    FOREIGN KEY (country_id) REFERENCES countries (country_id)
);

-- Create cups table
CREATE TABLE cups (
    id SERIAL PRIMARY KEY,
    cup_id VARCHAR(50) NOT NULL,
    cup_short_name VARCHAR(100) NOT NULL,
    cup_official_name_simp VARCHAR(200),
    cup_official_name_cant VARCHAR(200),
    cup_official_name_en VARCHAR(200),
    cup_logo VARCHAR(200),
    match_type VARCHAR(50) NOT NULL,
    current_season VARCHAR(50) NOT NULL,
    continent_id INTEGER,
    country_id VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (cup_id, current_season),
    FOREIGN KEY (continent_id) REFERENCES continents (continent_id),
    FOREIGN KEY (country_id) REFERENCES countries (country_id)
);

-- Create teams table
CREATE TABLE teams (
    id SERIAL PRIMARY KEY,
    team_id VARCHAR(50) NOT NULL UNIQUE,
    team_name_simp VARCHAR(100) NOT NULL,
    team_name_cant VARCHAR(100),
    team_name_en VARCHAR(100),
    team_logo VARCHAR(200),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create matches table
CREATE TABLE matches (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) NOT NULL UNIQUE,
    league_ref_id INTEGER,
    cup_ref_id INTEGER,
    sub_league_id VARCHAR(50),
    season VARCHAR(50) NOT NULL,
    round VARCHAR(50),
    match_time TIMESTAMP WITH TIME ZONE,
    home_team_id VARCHAR(50) NOT NULL,
    away_team_id VARCHAR(50) NOT NULL,
    full_score VARCHAR(50),
    half_score VARCHAR(50),
    scraped_1d BOOLEAN DEFAULT FALSE,
    scraped_6h BOOLEAN DEFAULT FALSE,
    scraped_2h BOOLEAN DEFAULT FALSE,
    scraped_15m BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_matches_league FOREIGN KEY (league_ref_id) REFERENCES leagues (id),
    CONSTRAINT fk_matches_cup FOREIGN KEY (cup_ref_id) REFERENCES cups (id),
    CONSTRAINT fk_matches_home_team FOREIGN KEY (home_team_id) REFERENCES teams (team_id),
    CONSTRAINT fk_matches_away_team FOREIGN KEY (away_team_id) REFERENCES teams (team_id),
    CONSTRAINT match_belongs_to_league_or_cup CHECK (
        (league_ref_id IS NOT NULL AND cup_ref_id IS NULL) OR 
        (league_ref_id IS NULL AND cup_ref_id IS NOT NULL)
    )
);

-- Create odds table
CREATE TABLE odds (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) NOT NULL,
    odds_detail_id VARCHAR(50) NOT NULL,
    bookmaker_id VARCHAR(50) NOT NULL,
    home_win NUMERIC(10, 2),
    draw NUMERIC(10, 2),
    away_win NUMERIC(10, 2),
    update_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (match_id, odds_detail_id, bookmaker_id, update_time),
    CONSTRAINT fk_odds_match FOREIGN KEY (match_id) REFERENCES matches (match_id)
);

-- Create spider_logs table
CREATE TABLE spider_logs (
    task_id VARCHAR(64) NOT NULL PRIMARY KEY,
    spider_type VARCHAR(20) NOT NULL,
    league VARCHAR(50),
    season VARCHAR(50),
    round VARCHAR(10),
    status VARCHAR(20) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    message VARCHAR(255),
    log_content TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create odds_retry_queue table
CREATE TABLE odds_retry_queue (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) NOT NULL UNIQUE,
    status VARCHAR(20) DEFAULT 'pending',
    attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches (match_id)
);

-- Create indexes
CREATE INDEX idx_continents_continent_id ON continents(continent_id);

CREATE INDEX idx_countries_country_id ON countries(country_id);
CREATE INDEX idx_countries_continent_id ON countries(continent_id);

CREATE INDEX idx_leagues_league_id ON leagues(league_id);
CREATE INDEX idx_leagues_sub_league_id ON leagues(sub_league_id);
CREATE INDEX idx_leagues_current_season ON leagues(current_season);
CREATE INDEX idx_leagues_continent_id ON leagues(continent_id);
CREATE INDEX idx_leagues_country_id ON leagues(country_id);

CREATE INDEX idx_cups_cup_id ON cups(cup_id);
CREATE INDEX idx_cups_current_season ON cups(current_season);
CREATE INDEX idx_cups_continent_id ON cups(continent_id);
CREATE INDEX idx_cups_country_id ON cups(country_id);

CREATE INDEX idx_teams_team_id ON teams(team_id);

CREATE INDEX idx_matches_match_id ON matches(match_id);
CREATE INDEX idx_matches_league_ref_id ON matches(league_ref_id);
CREATE INDEX idx_matches_cup_ref_id ON matches(cup_ref_id);
CREATE INDEX idx_matches_season ON matches(season);
CREATE INDEX idx_matches_round ON matches(round);
CREATE INDEX idx_matches_match_time ON matches(match_time);
CREATE INDEX idx_matches_home_team_id ON matches(home_team_id);
CREATE INDEX idx_matches_away_team_id ON matches(away_team_id);

CREATE INDEX idx_odds_match_id ON odds(match_id);
CREATE INDEX idx_odds_update_time ON odds(update_time);
CREATE INDEX idx_odds_bookmaker_id ON odds(bookmaker_id);

CREATE INDEX idx_spider_logs_spider_type ON spider_logs(spider_type);
CREATE INDEX idx_spider_logs_status ON spider_logs(status);
CREATE INDEX idx_spider_logs_started_at ON spider_logs(started_at DESC);

CREATE INDEX idx_odds_retry_queue_status ON odds_retry_queue(status);
CREATE INDEX idx_odds_retry_queue_match_id ON odds_retry_queue(match_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for all tables
CREATE TRIGGER update_continents_updated_at
    BEFORE UPDATE ON continents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_countries_updated_at
    BEFORE UPDATE ON countries
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leagues_updated_at
    BEFORE UPDATE ON leagues
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cups_updated_at
    BEFORE UPDATE ON cups
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at
    BEFORE UPDATE ON teams
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_matches_updated_at
    BEFORE UPDATE ON matches
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_odds_updated_at
    BEFORE UPDATE ON odds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_spider_logs_updated_at
    BEFORE UPDATE ON spider_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_odds_retry_queue_updated_at
    BEFORE UPDATE ON odds_retry_queue
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 