-- update_odds_schema.sql
-- 修改odds表update_time字段类型的SQL脚本

-- 1. 首先备份表结构以防万一（不备份数据）
CREATE TABLE IF NOT EXISTS odds_schema_backup AS 
SELECT * FROM odds WHERE 1=0;

-- 2. 修改update_time字段类型为timestamp with time zone
ALTER TABLE odds 
ALTER COLUMN update_time TYPE TIMESTAMP WITH TIME ZONE 
USING CASE
    -- 处理Unix时间戳（纯数字）
    WHEN update_time ~ '^\d+$' THEN 
        to_timestamp(update_time::bigint)
    -- 处理"MM-DD HH:MM"格式（如"03-16 21:30"）- 假设是当前年份
    WHEN update_time ~ '^[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$' THEN
        to_timestamp(
            extract(year from current_date)::text || '-' || 
            substring(update_time from 1 for 5) || ' ' || 
            substring(update_time from 7), 
            'YYYY-MM-DD HH24:MI'
        )
    -- 处理"YYYY-MM-DD HH:MM:SS"格式
    WHEN update_time ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' THEN
        update_time::timestamp with time zone
    -- 处理"YYYY-MM-DD HH:MM"格式
    WHEN update_time ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$' THEN
        (update_time || ':00')::timestamp with time zone
    -- 其他情况，尝试直接转换，如果失败则设为NULL
    ELSE
        NULL
END;

-- 2.2 修改赔率数值字段类型
ALTER TABLE odds 
ALTER COLUMN home_win TYPE NUMERIC(10,2),
ALTER COLUMN draw TYPE NUMERIC(10,2),
ALTER COLUMN away_win TYPE NUMERIC(10,2);

-- 2.3 设置默认值
ALTER TABLE odds 
ALTER COLUMN update_time SET DEFAULT CURRENT_TIMESTAMP;

-- 3. 删除并重建索引
DROP INDEX IF EXISTS idx_odds_match_id;
DROP INDEX IF EXISTS idx_odds_update_time;
DROP INDEX IF EXISTS idx_odds_bookmaker_id;

CREATE INDEX idx_odds_match_id ON odds(match_id);
CREATE INDEX idx_odds_update_time ON odds(update_time);
CREATE INDEX idx_odds_bookmaker_id ON odds(bookmaker_id);

-- 4. 更新外键约束
ALTER TABLE odds DROP CONSTRAINT IF EXISTS fk_odds_match;
ALTER TABLE odds ADD CONSTRAINT fk_odds_match 
FOREIGN KEY (match_id) REFERENCES matches(match_id);

-- 5. 检查修改结果
-- 以下语句用于验证修改结果
-- SELECT column_name, data_type, character_maximum_length
-- FROM information_schema.columns
-- WHERE table_name = 'odds'
-- ORDER BY ordinal_position; 