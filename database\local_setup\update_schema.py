#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新数据库schema的脚本
包括：
1. 更新odds表：将update_time从character varying修改为timestamp with time zone
2. 添加spider_logs表
3. 更新表的主键和外键关系
"""

import psycopg2
from psycopg2.extras import DictCursor
import logging
import sys
import os
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_db_connection(host=os.getenv('DB_HOST', 'localhost'), 
                     port=int(os.getenv('DB_PORT', '5432')), 
                     dbname=os.getenv('DB_NAME', 'oddluck'), 
                     user=os.getenv('DB_USER', 'postgres'), 
                     password=os.getenv('DB_PASSWORD', '')):
    """连接数据库"""
    try:
        logger.info(f"正在连接数据库... Host: {host}, Port: {port}, Database: {dbname}")
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password,
            connect_timeout=10,
            sslmode=os.getenv('PGSSLMODE', 'require')  # 添加SSL模式支持
        )
        logger.info("数据库连接成功")
        return conn
    except Exception as e:
        logger.error(f"连接数据库时出错: {str(e)}")
        return None

def check_table_schema(conn, table_name):
    """检查表结构"""
    with conn.cursor(cursor_factory=DictCursor) as cur:
        cur.execute(f"""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position;
        """)
        columns = cur.fetchall()
        
        if columns:
            logger.info(f"\n{'='*50}\n{table_name} 表结构:\n{'-'*50}")
        
        for col in columns:
            data_type = col['data_type']
            if col['character_maximum_length']:
                data_type += f"({col['character_maximum_length']})"
                
            logger.info(f"{col['column_name']:<20} {data_type}")
            
            logger.info('='*50)
            return True
        else:
            logger.warning(f"表 {table_name} 不存在")
            return False

def table_exists(conn, table_name):
    """检查表是否存在"""
    with conn.cursor() as cur:
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            );
        """, (table_name,))
        return cur.fetchone()[0]

def create_spider_logs_table(conn):
    """创建spider_logs表"""
    try:
        with conn.cursor() as cur:
            if not table_exists(conn, 'spider_logs'):
                logger.info("正在创建spider_logs表...")
                cur.execute("""
                    CREATE TABLE spider_logs (
                        task_id VARCHAR(64) NOT NULL,
                        spider_type VARCHAR(20) NOT NULL,
                        league VARCHAR(50),
                        season VARCHAR(50),
                        round VARCHAR(10),
                        status VARCHAR(20) DEFAULT 'pending',
                        progress INTEGER DEFAULT 0,
                        message VARCHAR(255),
                        log_content TEXT,
                        started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        completed_at TIMESTAMP WITH TIME ZONE,
                        PRIMARY KEY (task_id)
                    );
                    
                    CREATE INDEX idx_spider_logs_spider_type ON spider_logs(spider_type);
                    CREATE INDEX idx_spider_logs_status ON spider_logs(status);
                    CREATE INDEX idx_spider_logs_started_at ON spider_logs(started_at DESC);
                    
                    CREATE TRIGGER update_spider_logs_updated_at
                        BEFORE UPDATE ON spider_logs
                        FOR EACH ROW
                        EXECUTE FUNCTION update_updated_at_column();
                """)
                logger.info("spider_logs表创建成功")
                return True
            else:
                logger.info("spider_logs表已存在")
                return False
    except Exception as e:
        logger.error(f"创建spider_logs表时出错: {str(e)}")
        conn.rollback()
        return False

def update_odds_table(conn):
    """更新odds表结构"""
    try:
        with conn.cursor() as cur:
            logger.info("检查odds表中update_time字段类型...")
            cur.execute("""
                SELECT data_type 
                FROM information_schema.columns 
                WHERE table_name = 'odds' AND column_name = 'update_time';
            """)
            data_type = cur.fetchone()
            
            if data_type and data_type[0] != 'timestamp with time zone':
                logger.info(f"正在将odds表的update_time字段从 {data_type[0]} 更新为 timestamp with time zone...")
                
                # 执行更新字段类型的操作
                cur.execute("""
                    ALTER TABLE odds 
                    ALTER COLUMN update_time TYPE TIMESTAMP WITH TIME ZONE 
                    USING CASE
                        -- 处理Unix时间戳（纯数字）
                        WHEN update_time ~ '^\\d+$' THEN 
                            to_timestamp(update_time::bigint)
                        -- 处理"MM-DD HH:MM"格式（如"03-16 21:30"）- 假设是当前年份
                        WHEN update_time ~ '^[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$' THEN
                            to_timestamp(
                                extract(year from current_date)::text || '-' || 
                                substring(update_time from 1 for 5) || ' ' || 
                                substring(update_time from 7), 
                                'YYYY-MM-DD HH24:MI'
                            )
                        -- 处理"YYYY-MM-DD HH:MM:SS"格式
                        WHEN update_time ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' THEN
                            update_time::timestamp with time zone
                        -- 处理"YYYY-MM-DD HH:MM"格式
                        WHEN update_time ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$' THEN
                            (update_time || ':00')::timestamp with time zone
                        -- 其他情况，尝试直接转换，如果失败则设为NULL
                        ELSE
                            NULL
                    END;
                    
                    ALTER TABLE odds 
                    ALTER COLUMN update_time SET DEFAULT CURRENT_TIMESTAMP;
                """)
                
                # 检查是否需要修改数值类型字段
                cur.execute("""
                    SELECT data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'odds' AND column_name = 'home_win';
                """)
                home_win_type = cur.fetchone()
                
                if home_win_type and home_win_type[0] != 'numeric':
                    logger.info("更新odds表的赔率字段类型为numeric(10,2)...")
                    cur.execute("""
                        ALTER TABLE odds ALTER COLUMN home_win TYPE NUMERIC(10,2);
                        ALTER TABLE odds ALTER COLUMN draw TYPE NUMERIC(10,2);
                        ALTER TABLE odds ALTER COLUMN away_win TYPE NUMERIC(10,2);
                    """)
                
                logger.info("odds表更新成功")
                return True
            else:
                logger.info("odds表字段类型已符合要求，无需更新")
                return False
    except Exception as e:
        logger.error(f"更新odds表时出错: {str(e)}")
        conn.rollback()
        return False

def update_primary_keys(conn):
    """更新表的主键结构"""
    try:
        tables_to_update = {
            'leagues': {'primary_key': 'id', 'unique': [('league_id', 'current_season', 'sub_league_id')]},
            'teams': {'primary_key': 'id', 'unique': [('team_id', 'league_id', 'sub_league_id')]},
            'matches': {'primary_key': 'id', 'unique': ['match_id']}
        }
        
        with conn.cursor() as cur:
            for table, config in tables_to_update.items():
                # 检查表是否存在
                if not table_exists(conn, table):
                    logger.warning(f"表 {table} 不存在，跳过")
                    continue
                
                # 获取当前主键信息
                cur.execute(f"""
                    SELECT kcu.column_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    WHERE tc.table_name = '{table}'
                    AND tc.constraint_type = 'PRIMARY KEY';
                """)
                current_pk = [row[0] for row in cur.fetchall()]
                
                if not current_pk or current_pk[0] != config['primary_key']:
                    logger.info(f"更新 {table} 表的主键为 {config['primary_key']}...")
                    
                    # 如果存在主键，先删除它
                    if current_pk:
                        cur.execute(f"""
                            ALTER TABLE {table} DROP CONSTRAINT IF EXISTS {table}_pkey;
                        """)
                    
                    # 添加新主键
                    cur.execute(f"""
                        ALTER TABLE {table} ADD PRIMARY KEY ({config['primary_key']});
                    """)
                    
                    logger.info(f"{table} 表主键更新成功")
                
                # 更新唯一约束
                for unique_key in config.get('unique', []):
                    if isinstance(unique_key, tuple):
                        columns = ', '.join(unique_key)
                        constraint_name = f"{table}_{'_'.join(unique_key)}_key"
                    else:
                        columns = unique_key
                        constraint_name = f"{table}_{unique_key}_key"
                    
                    # 检查唯一约束是否存在
                    cur.execute(f"""
                        SELECT COUNT(*)
                        FROM information_schema.table_constraints
                        WHERE table_name = '{table}'
                        AND constraint_name = '{constraint_name}'
                        AND constraint_type = 'UNIQUE';
                    """)
                    
                    if cur.fetchone()[0] == 0:
                        logger.info(f"为 {table} 表添加唯一约束 {columns}...")
                        cur.execute(f"""
                            ALTER TABLE {table} ADD CONSTRAINT {constraint_name} UNIQUE ({columns});
                        """)
                        logger.info(f"{table} 表唯一约束添加成功")
            
            conn.commit()
            return True
    except Exception as e:
        logger.error(f"更新主键结构时出错: {str(e)}")
        conn.rollback()
        return False

def update_foreign_keys(conn):
    """更新表的外键关系"""
    try:
        foreign_keys = [
            ('teams', 'league_id', 'leagues', 'league_id'),
            ('matches', 'league_ref_id', 'leagues', 'id'),
            ('matches', 'home_team_id', 'teams', 'team_id'),
            ('matches', 'away_team_id', 'teams', 'team_id'),
            ('odds_retry_queue', 'match_id', 'matches', 'match_id')
        ]
        
        with conn.cursor() as cur:
            for table, column, ref_table, ref_column in foreign_keys:
                # 检查表是否存在
                if not table_exists(conn, table) or not table_exists(conn, ref_table):
                    logger.warning(f"表 {table} 或 {ref_table} 不存在，跳过")
                    continue
                
                # 检查外键是否已存在
                constraint_name = f"fk_{table}_{column}_{ref_table}_{ref_column}"
                cur.execute(f"""
                    SELECT COUNT(*)
                    FROM information_schema.table_constraints
                    WHERE table_name = '{table}'
                    AND constraint_name = '{constraint_name}'
                    AND constraint_type = 'FOREIGN KEY';
                """)
                
                if cur.fetchone()[0] == 0:
                    logger.info(f"为 {table}.{column} 添加外键约束引用 {ref_table}.{ref_column}...")
                    
                    # 删除可能存在的旧外键
                    cur.execute(f"""
                        SELECT constraint_name
                        FROM information_schema.table_constraints
                        WHERE table_name = '{table}'
                        AND constraint_type = 'FOREIGN KEY'
                        AND constraint_name LIKE '%{column}%';
                    """)
                    old_constraints = cur.fetchall()
                    
                    for old in old_constraints:
                        cur.execute(f"""
                            ALTER TABLE {table} DROP CONSTRAINT IF EXISTS {old[0]};
                        """)
                    
                    # 添加新外键
        cur.execute(f"""
                        ALTER TABLE {table} ADD CONSTRAINT {constraint_name}
                        FOREIGN KEY ({column}) REFERENCES {ref_table}({ref_column});
                    """)
                    
        logger.info(f"{table}.{column} 外键约束添加成功")
            
        conn.commit()
        return True
    except Exception as e:
        logger.error(f"更新外键关系时出错: {str(e)}")
        conn.rollback()
        return False

def add_missing_indexes(conn):
    """添加缺失的索引"""
    try:
        indexes = [
            ('matches', 'match_id'),
            ('matches', 'home_team_id'),
            ('matches', 'away_team_id'),
            ('spider_logs', 'spider_type'),
            ('spider_logs', 'status'),
            ('spider_logs', 'started_at DESC')
        ]
        
        with conn.cursor() as cur:
            for table, column in indexes:
                # 检查表是否存在
                if not table_exists(conn, table):
                    logger.warning(f"表 {table} 不存在，跳过")
                    continue
                
                # 提取纯列名（不包含排序方向）
                pure_column = column.split(' ')[0]
                
                # 检查索引是否已存在
                index_name = f"idx_{table}_{pure_column}"
                if 'DESC' in column or 'ASC' in column:
                    index_name += "_desc" if 'DESC' in column else "_asc"
                
                cur.execute(f"""
                    SELECT COUNT(*)
                    FROM pg_indexes
                    WHERE tablename = '{table}'
                    AND indexname = '{index_name}';
                """)
                
                if cur.fetchone()[0] == 0:
                    logger.info(f"为 {table}.{column} 添加索引...")
                    cur.execute(f"""
                        CREATE INDEX {index_name} ON {table}({column});
                    """)
                    logger.info(f"{table}.{column} 索引添加成功")
            
            conn.commit()
            return True
    except Exception as e:
        logger.error(f"添加索引时出错: {str(e)}")
        conn.rollback()
        return False

def execute_sql_file(conn, file_path):
    """执行SQL文件中的所有语句"""
    try:
        logger.info(f"正在执行SQL文件: {file_path}")
        
        # 读取SQL文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        # 分割SQL语句（简单实现，假设每条语句以分号结束）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        # 执行每条SQL语句
        with conn.cursor() as cur:
            for i, statement in enumerate(sql_statements, 1):
                try:
                    logger.info(f"执行第 {i} 条SQL语句...")
                    cur.execute(statement)
                    logger.info(f"第 {i} 条SQL语句执行成功")
                except Exception as e:
                    logger.error(f"执行第 {i} 条SQL语句时出错: {str(e)}")
                    logger.error(f"问题语句: {statement}")
                    raise
        
        # 提交事务
        conn.commit()
        logger.info("所有SQL语句执行成功并已提交")
        return True
    except Exception as e:
        # 回滚事务
        conn.rollback()
        logger.error(f"执行SQL文件时出错，已回滚: {str(e)}")
        return False

def main():
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 连接数据库
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        # 1. 检查并创建spider_logs表
        create_spider_logs_table(conn)
        
        # 2. 更新odds表结构
        update_odds_table(conn)
        
        # 3. 更新主键结构
        update_primary_keys(conn)
        
        # 4. 更新外键关系
        update_foreign_keys(conn)
        
        # 5. 添加缺失的索引
        add_missing_indexes(conn)
        
        # 6. 检查最终的表结构
        tables = ['continents', 'countries', 'leagues', 'teams', 'matches', 'odds', 'spider_logs']
        for table in tables:
            check_table_schema(conn, table)
        
        logger.info("数据库结构更新完成")
        
    finally:
        conn.close()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 