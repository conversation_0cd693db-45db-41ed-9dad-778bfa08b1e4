-- 创建leagues表
CREATE TABLE IF NOT EXISTS leagues (
    id SERIAL,
    league_id VARCHAR(50) NOT NULL,
    sub_league_id VARCHAR(50),
    league_type VARCHAR(50) NOT NULL,
    league_name VARCHAR(100) NOT NULL,
    league_name_en VARCHAR(100),
    season VARCHAR(50) NOT NULL,
    logo VARCHAR(200),
    total_rounds INTEGER,
    current_round INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (league_id, season, sub_league_id)
);

-- 创建teams表
CREATE TABLE IF NOT EXISTS teams (
    id SERIAL,
    team_id VARCHAR(50) NOT NULL,
    team_name VARCHAR(100) NOT NULL,
    team_name_en VARCHAR(100),
    team_logo VARCHAR(200),
    league_id VARCHAR(50) NOT NULL,
    sub_league_id VARCHAR(50),
    season VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (team_id, league_id, sub_league_id),
    FOREIGN KEY (league_id, season, sub_league_id) REFERENCES leagues (league_id, season, sub_league_id)
);

-- 创建matches表
CREATE TABLE IF NOT EXISTS matches (
    id SERIAL,
    match_id VARCHAR(50) NOT NULL,
    league_id VARCHAR(50) NOT NULL,
    sub_league_id VARCHAR(50),
    season VARCHAR(50) NOT NULL,
    round VARCHAR(50),
    match_time TIMESTAMP WITH TIME ZONE,
    home_team_id VARCHAR(50) NOT NULL,
    away_team_id VARCHAR(50) NOT NULL,
    full_score VARCHAR(50),
    half_score VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (match_id),
    FOREIGN KEY (league_id, season, sub_league_id) REFERENCES leagues (league_id, season, sub_league_id),
    FOREIGN KEY (home_team_id, league_id, sub_league_id) REFERENCES teams (team_id, league_id, sub_league_id),
    FOREIGN KEY (away_team_id, league_id, sub_league_id) REFERENCES teams (team_id, league_id, sub_league_id)
);

-- 创建odds表
CREATE TABLE IF NOT EXISTS odds (
    match_id VARCHAR(50) NOT NULL,
    odds_detail_id VARCHAR(50) NOT NULL,
    bookmaker_id VARCHAR(50) NOT NULL,
    home_win NUMERIC,
    draw NUMERIC,
    away_win NUMERIC,
    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (match_id, odds_detail_id, bookmaker_id, update_time),
    FOREIGN KEY (match_id) REFERENCES matches (match_id)
);

-- 创建odds_retry_queue表
CREATE TABLE IF NOT EXISTS odds_retry_queue (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) NOT NULL UNIQUE,
    status VARCHAR(20) DEFAULT 'pending',
    attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
); 