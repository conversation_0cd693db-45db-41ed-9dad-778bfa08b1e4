-- 创建触发器
CREATE TRIGGER update_leagues_updated_at
BEFORE UPDATE ON leagues
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at
BEFORE UPDATE ON teams
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_matches_updated_at
BEFORE UPDATE ON matches
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_odds_updated_at
BEFORE UPDATE ON odds
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_odds_retry_queue_updated_at
BEFORE UPDATE ON odds_retry_queue
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column(); 