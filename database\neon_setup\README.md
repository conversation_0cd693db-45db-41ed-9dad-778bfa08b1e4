# Neon数据库迁移指南

本指南将帮助您将本地PostgreSQL数据库中的数据迁移到Neon云数据库。

## 准备工作

1. 确保安装了PostgreSQL客户端工具
   - `pg_dump` - 用于导出数据
   - `psql` - 用于导入数据
   
2. 安装所需的Python包
   ```
   pip install psycopg2-binary python-dotenv
   ```

## 配置

1. 在`neon_setup`目录下创建`.env`文件（可复制`.env.example`）
2. 填写本地数据库和Neon数据库的连接信息

```
# 本地数据库连接信息
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=5432
LOCAL_DB_NAME=oddluck
LOCAL_DB_USER=postgres
LOCAL_DB_PASSWORD=your_local_password

# Neon数据库连接信息
DB_HOST=ep-cold-tooth-a54lbla0-pooler.us-east-2.aws.neon.tech
DB_PORT=5432
DB_NAME=oddluck
DB_USER=chodomadie_owner
DB_PASSWORD=your_neon_password
PGSSLMODE=require
```

## 使用方法

### 执行迁移

1. 进入`database`目录
2. 运行迁移脚本
   ```
   python neon_setup/migrate_data.py
   ```
   
脚本会按照以下顺序迁移表数据：
1. leagues
2. teams
3. matches
4. odds
5. odds_retry_queue

迁移过程会显示详细日志，包括导出和导入每个表的状态。

### 完整数据库迁移（替代方法）

如果您想一次性迁移整个数据库，可以使用以下命令：

1. 导出本地数据库数据（不包含结构）
   ```
   pg_dump -a --no-owner --no-privileges -h localhost -U postgres -d oddluck > full_export.sql
   ```

2. 导入数据到Neon
   ```
   psql "postgresql://user:<EMAIL>/oddluck?sslmode=require" -f full_export.sql
   ```

## 故障排除

### 连接问题
- 确保本地PostgreSQL服务正在运行
- 检查Neon数据库连接URL是否正确
- 验证您的IP是否有权访问Neon数据库

### 导入错误
- 确保目标Neon数据库已正确创建表结构
- 检查数据格式是否兼容
- 查看错误消息以获取更具体的问题

### SSL问题
- 确保在连接Neon时设置`PGSSLMODE=require` 