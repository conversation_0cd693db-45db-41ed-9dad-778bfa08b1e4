# Neon数据库迁移指南

本文档记录了将ODDLUCK项目从本地PostgreSQL数据库迁移到Neon云数据库的步骤和注意事项。

## 更新的文件

1. `oddluck_dev/backend/backend/settings.py`
   - 更新了Django数据库配置，添加了Neon数据库连接信息和SSL设置

2. `football_spider/football_spider/settings.py`
   - 更新了爬虫项目数据库配置，添加了环境变量支持
   - 添加了SSL模式配置
   - 减小连接池大小以适应云数据库

3. `football_spider/football_spider/db_pool.py`
   - 在数据库连接池中添加了SSL支持
   - 更新了连接参数名称

4. `football_spider/football_spider/incremental_pipeline.py`
   - 更新了增量更新管道以支持SSL连接
   - 修改了构造函数以接受SSL模式参数

5. `database/db.py`
   - 添加了环境变量支持
   - 添加了SSL连接支持
   - 改进了连接错误处理

6. `database/update_schema.py`
   - 添加了环境变量支持
   - 在数据库连接中添加了SSL支持

## 环境变量配置

需要在本地创建一个`.env`文件，包含以下配置：

```
# 数据库配置
DB_NAME=oddluck
DB_USER=chodomadie_owner
DB_PASSWORD=你的密码  # 请替换为实际密码
DB_HOST=ep-cold-tooth-a54lbla0-pooler.us-east-2.aws.neon.tech
DB_PORT=5432
PGSSLMODE=require  # Neon数据库需要SSL连接
```

## 注意事项

1. **密码处理**：
   - 配置文件中已移除硬编码的密码
   - 实际密码应通过环境变量提供，不要直接写入代码

2. **连接池大小**：
   - 从10减少到5，避免与Neon数据库创建过多连接
   - 云数据库通常有连接数限制，请根据实际情况调整

3. **SSL要求**：
   - Neon数据库要求SSL连接（sslmode=require）
   - 所有连接都已添加SSL配置

4. **错误处理**：
   - 增强了数据库连接错误处理
   - 添加了日志记录以便调试连接问题

## 迁移后的验证

迁移完成后，请执行以下测试确保连接正常：

1. 启动Django开发服务器：
   ```
   cd oddluck_dev
   python manage.py runserver
   ```

2. 尝试执行一次爬虫任务：
   ```
   cd football_spider
   scrapy crawl league -a league=英超 -a season=2024-2025
   ```

如有连接问题，请检查错误日志并确保环境变量正确设置。 