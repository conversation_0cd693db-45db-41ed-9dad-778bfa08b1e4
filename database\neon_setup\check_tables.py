#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库中的表结构
"""

import psycopg2
from psycopg2.extras import DictCursor
import logging
import sys
import os
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_db_connection():
    """连接数据库"""
    host = os.getenv('DB_HOST', 'ep-cold-tooth-a54lbla0-pooler.us-east-2.aws.neon.tech')
    port = int(os.getenv('DB_PORT', '5432'))
    dbname = os.getenv('DB_NAME', 'oddluck')
    user = os.getenv('DB_USER', 'chodomadie_owner')
    password = os.getenv('DB_PASSWORD', '')
    sslmode = os.getenv('PGSSLMODE', 'require')
    
    try:
        logger.info(f"正在连接数据库... Host: {host}, Port: {port}, Database: {dbname}, SSL: {sslmode}")
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password,
            connect_timeout=10,
            sslmode=sslmode
        )
        logger.info("数据库连接成功")
        return conn
    except Exception as e:
        logger.error(f"连接数据库时出错: {str(e)}")
        return None

def check_tables(conn):
    """检查数据库中的表结构"""
    with conn.cursor(cursor_factory=DictCursor) as cur:
        # 获取所有表
        cur.execute("""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        ORDER BY table_name
        """)
        tables = cur.fetchall()
        
        logger.info("=" * 50)
        logger.info(f"数据库中共有 {len(tables)} 个表")
        logger.info("=" * 50)
        
        # 依次检查每个表
        for table in tables:
            table_name = table['table_name']
            logger.info(f"\n表 {table_name} 的结构:")
            logger.info("-" * 50)
            
            # 获取表结构
            cur.execute(f"""
            SELECT column_name, data_type, character_maximum_length, is_nullable
            FROM information_schema.columns
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position
            """)
            columns = cur.fetchall()
            
            for col in columns:
                data_type = col['data_type']
                if col['character_maximum_length']:
                    data_type += f"({col['character_maximum_length']})"
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                logger.info(f"{col['column_name']:<30} {data_type:<30} {nullable}")
            
            # 获取表中的记录数
            cur.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cur.fetchone()[0]
            logger.info("-" * 50)
            logger.info(f"表 {table_name} 中有 {count} 条记录")
            logger.info("=" * 50)

def main():
    # 连接数据库
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        # 检查表结构
        check_tables(conn)
    finally:
        conn.close()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 