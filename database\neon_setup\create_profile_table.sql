-- 创建UserProfile表
CREATE TABLE IF NOT EXISTS auth_api_userprofile (
    id SERIAL PRIMARY KEY,
    avatar TEXT,
    bio TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    notification_match BOOLEAN NOT NULL DEFAULT TRUE,
    notification_odds BOOLEAN NOT NULL DEFAULT TRUE,
    notification_analysis BOOLEAN NOT NULL DEFAULT FALSE,
    user_id INTEGER NOT NULL UNIQUE,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth_user(id) ON DELETE CASCADE
);

-- 为已有用户创建档案记录
INSERT INTO auth_api_userprofile (user_id, notification_match, notification_odds, notification_analysis)
SELECT id, TRUE, TRUE, FALSE FROM auth_user
WHERE id NOT IN (SELECT user_id FROM auth_api_userprofile); 