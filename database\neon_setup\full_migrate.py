#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
将本地PostgreSQL数据库数据完整迁移到Neon云数据库
使用pg_dump导出数据，然后使用psql导入到Neon
"""

import os
import subprocess
import logging
import sys
import tempfile
import urllib.parse
import shutil
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_pg_tools():
    """检查pg_dump和psql工具是否可用"""
    pg_dump_path = shutil.which("pg_dump")
    psql_path = shutil.which("psql")
    
    if not pg_dump_path:
        logger.error("未找到pg_dump工具，请确保PostgreSQL客户端工具已安装")
        return False
    
    if not psql_path:
        logger.error("未找到psql工具，请确保PostgreSQL客户端工具已安装")
        return False
    
    logger.info(f"找到pg_dump: {pg_dump_path}")
    logger.info(f"找到psql: {psql_path}")
    return True

def get_db_connection_info():
    """获取数据库连接信息"""
    # 本地数据库连接信息
    local_host = os.getenv('LOCAL_DB_HOST', 'localhost')
    local_port = os.getenv('LOCAL_DB_PORT', '5432')
    local_dbname = os.getenv('LOCAL_DB_NAME', 'oddluck')
    local_user = os.getenv('LOCAL_DB_USER', 'postgres')
    local_password = os.getenv('LOCAL_DB_PASSWORD', '888')
    
    # Neon数据库连接信息
    neon_host = os.getenv('DB_HOST', 'ep-cold-tooth-a54lbla0-pooler.us-east-2.aws.neon.tech')
    neon_port = os.getenv('DB_PORT', '5432')
    neon_dbname = os.getenv('DB_NAME', 'oddluck')
    neon_user = os.getenv('DB_USER', 'chodomadie_owner')
    neon_password = os.getenv('DB_PASSWORD', '')
    
    # SSL配置
    ssl_mode = os.getenv('SSL_MODE', 'require')  # 默认require，可选：disable, require, verify-ca, verify-full
    ssl_root_cert = os.getenv('SSL_ROOT_CERT', '')  # 可选：根证书路径
    
    return {
        'local': {
            'host': local_host,
            'port': local_port,
            'dbname': local_dbname,
            'user': local_user,
            'password': local_password
        },
        'neon': {
            'host': neon_host,
            'port': neon_port,
            'dbname': neon_dbname,
            'user': neon_user,
            'password': neon_password,
            'ssl_mode': ssl_mode,
            'ssl_root_cert': ssl_root_cert
        }
    }

def full_database_export_import():
    """完整导出本地数据库并导入到Neon"""
    try:
        # 检查PostgreSQL工具是否可用
        if not check_pg_tools():
            return False
        
        # 获取数据库连接信息
        db_info = get_db_connection_info()
        local = db_info['local']
        neon = db_info['neon']
        
        # 创建临时文件
        temp_file = os.path.join(tempfile.gettempdir(), "full_export.sql")
        logger.info(f"开始完整数据库导出到 {temp_file}...")
        
        # 设置环境变量
        env = os.environ.copy()
        if local['password']:
            env['PGPASSWORD'] = local['password']
        
        # 构建导出命令
        export_cmd = [
            "pg_dump",
            "-h", local['host'],
            "-p", local['port'],
            "-U", local['user'],
            "-d", local['dbname'],
            "-a",                    # 只导出数据，不导出schema
            "--no-owner",            # 不包含所有者信息
            "--no-privileges",       # 不包含权限信息
            "-f", temp_file          # 输出文件
        ]
        
        # 执行导出
        logger.info(f"执行导出命令: {' '.join(export_cmd)}")
        export_result = subprocess.run(export_cmd, env=env, capture_output=True, text=True)
        
        if export_result.returncode != 0:
            logger.error(f"导出失败: {export_result.stderr}")
            return False
        
        if os.path.exists(temp_file):
            file_size = os.path.getsize(temp_file)
            if file_size == 0:
                logger.error("导出文件为空，请检查数据库连接和权限")
                return False
            logger.info(f"导出成功，文件大小: {file_size} 字节")
        else:
            logger.error(f"导出命令未能创建文件: {temp_file}")
            return False
        
        # 设置Neon环境变量
        env = os.environ.copy()
        if neon['password']:
            env['PGPASSWORD'] = neon['password']
        
        # SSL环境变量
        env['PGSSLMODE'] = neon['ssl_mode']
        if neon['ssl_root_cert'] and os.path.exists(neon['ssl_root_cert']):
            env['PGSSLROOTCERT'] = neon['ssl_root_cert']
        
        # 构建导入命令
        import_cmd = [
            "psql",
            "-h", neon['host'],
            "-p", neon['port'],
            "-U", neon['user'],
            "-d", neon['dbname'],
            "-a",                    # 打印所有输入
            "-f", temp_file          # 输入文件
        ]
        
        # 添加SSL选项
        if neon['ssl_mode'] != 'disable':
            import_cmd.extend([
                "--set=sslmode=" + neon['ssl_mode']
            ])
            if neon['ssl_root_cert'] and os.path.exists(neon['ssl_root_cert']):
                import_cmd.extend([
                    "--set=sslrootcert=" + neon['ssl_root_cert']
                ])
        
        # 执行导入
        logger.info(f"执行导入命令: {' '.join(import_cmd)}")
        import_result = subprocess.run(import_cmd, env=env, capture_output=True, text=True)
        
        if import_result.returncode != 0:
            logger.error(f"导入失败: {import_result.stderr}")
            # 如果是SSL相关错误，提供更详细的指导
            if "SSL" in import_result.stderr or "ssl" in import_result.stderr:
                logger.error("检测到SSL错误。请尝试以下解决方案：")
                logger.error("1. 确保在.env文件中设置了正确的SSL_MODE (require, verify-ca, verify-full)")
                logger.error("2. 如果使用verify-ca或verify-full模式，确保提供了有效的SSL_ROOT_CERT路径")
                logger.error("3. 也可以尝试将SSL_MODE设置为'require'以简化SSL配置")
            return False
        
        logger.info("完整数据库导入成功")
        
        # 删除临时文件
        try:
            os.remove(temp_file)
            logger.info(f"已删除临时文件 {temp_file}")
        except Exception as e:
            logger.warning(f"无法删除临时文件: {str(e)}")
        
        return True
    except Exception as e:
        logger.error(f"完整数据库导出导入时出错: {str(e)}")
        # 显示更多异常信息
        import traceback
        logger.error(traceback.format_exc())
        return False

def create_env_file_if_not_exists():
    """如果.env文件不存在，创建一个包含默认值的.env文件"""
    env_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    
    if not os.path.exists(env_file_path):
        logger.info("未找到.env文件，创建默认配置...")
        with open(env_file_path, 'w', encoding='utf-8') as f:
            f.write("""# 本地数据库配置
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=5432
LOCAL_DB_NAME=oddluck
LOCAL_DB_USER=postgres
LOCAL_DB_PASSWORD=888

# Neon云数据库配置
DB_HOST=ep-cold-tooth-a54lbla0-pooler.us-east-2.aws.neon.tech
DB_PORT=5432
DB_NAME=oddluck
DB_USER=chodomadie_owner
DB_PASSWORD=

# SSL配置
SSL_MODE=require
# SSL_ROOT_CERT=/path/to/root/cert.pem (如果需要)
""")
        logger.info(f"已创建默认.env文件: {env_file_path}")
        logger.info("请编辑此文件设置正确的数据库连接信息")
        return True
    return False

def main():
    logger.info("开始完整数据库迁移...")
    
    # 检查并创建.env文件
    if create_env_file_if_not_exists():
        logger.info("请编辑.env文件后重新运行此脚本")
        return
    
    success = full_database_export_import()
    
    if success:
        logger.info("数据库完整迁移成功!")
    else:
        logger.error("数据库完整迁移失败!")

if __name__ == "__main__":
    main() 