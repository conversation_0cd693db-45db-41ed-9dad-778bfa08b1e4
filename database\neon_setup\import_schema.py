#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
将schema.sql导入到Neon数据库的脚本
"""

import psycopg2
import logging
import sys
import os
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_db_connection():
    """连接数据库"""
    host = os.getenv('DB_HOST', 'ep-cold-tooth-a54lbla0-pooler.us-east-2.aws.neon.tech')
    port = int(os.getenv('DB_PORT', '5432'))
    dbname = os.getenv('DB_NAME', 'oddluck')
    user = os.getenv('DB_USER', 'chodomadie_owner')
    password = os.getenv('DB_PASSWORD', '')
    sslmode = os.getenv('PGSSLMODE', 'require')
    
    try:
        logger.info(f"正在连接数据库... Host: {host}, Port: {port}, Database: {dbname}, SSL: {sslmode}")
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password,
            connect_timeout=10,
            sslmode=sslmode
        )
        logger.info("数据库连接成功")
        return conn
    except Exception as e:
        logger.error(f"连接数据库时出错: {str(e)}")
        return None

def execute_sql_file(conn, file_path):
    """执行SQL文件中的所有语句"""
    try:
        logger.info(f"正在执行SQL文件: {file_path}")
        
        # 读取SQL文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        # 分割SQL语句（简单实现，假设每条语句以分号结束）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        # 执行每条SQL语句
        with conn.cursor() as cur:
            for i, statement in enumerate(sql_statements, 1):
                try:
                    logger.info(f"执行第 {i} 条SQL语句...")
                    cur.execute(statement)
                    logger.info(f"第 {i} 条SQL语句执行成功")
                except Exception as e:
                    logger.error(f"执行第 {i} 条SQL语句时出错: {str(e)}")
                    logger.error(f"问题语句: {statement}")
                    # 继续执行其他语句，不要在一个失败时中止
        
        # 提交事务
        conn.commit()
        logger.info("所有SQL语句执行成功并已提交")
        return True
    except Exception as e:
        # 回滚事务
        conn.rollback()
        logger.error(f"执行SQL文件时出错，已回滚: {str(e)}")
        return False

def main():
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    sql_files = [
        os.path.join(script_dir, '1_create_function.sql'),
        os.path.join(script_dir, '2_create_tables.sql'),
        os.path.join(script_dir, '3_create_indexes.sql'),
        os.path.join(script_dir, '4_create_triggers.sql'),
        os.path.join(script_dir, 'create_profile_table.sql'),
    ]
    
    # 连接数据库
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        # 依次执行各个SQL文件
        for sql_file in sql_files:
            if os.path.exists(sql_file):
                logger.info(f"开始执行 {os.path.basename(sql_file)}...")
                execute_sql_file(conn, sql_file)
            else:
                logger.warning(f"SQL文件不存在: {sql_file}")
        
    finally:
        conn.close()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 