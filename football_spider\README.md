# 足球数据爬虫

这是一个使用Scrapy+Playwright的足球数据爬虫项目，用于抓取足球比赛数据和赔率信息。目标网站：https://zq.titan007.com

## 功能特点

- 支持抓取大洲、国家、联赛、球队的基本信息
- 支持抓取五大联赛（英超、德甲、西甲、意甲、法甲）的比赛数据
- 完整的数据层次结构：大洲 -> 国家 -> 联赛 -> 球队 -> 比赛
- 获取联赛基本信息（联赛ID、名称、赛季等）
- 获取球队信息（球队ID、名称、粤语名称等）
- 获取比赛信息（比赛时间、比分等）
- 获取主要博彩公司（Bet365、立博、威廉希尔）的欧赔数据
- 数据存储到PostgreSQL，支持索引优化和数据压缩
- 支持通过联赛名称指定要爬取的联赛，无需记忆联赛ID
- 自动处理子联赛，无需手动指定子联赛ID
- **支持增量更新**，自动跳过已爬取且无变化的数据，提高效率
- **数据库连接池**，减少数据库连接开销，提高并发性能
- **批量处理**，减少数据库操作次数，大幅提升处理速度
- **IP代理轮换**，降低被目标网站限制的风险
- **定时爬取**，支持在比赛前1天、6小时、2小时和15分钟自动触发爬取任务，确保数据实时更新

## 性能优化

- **数据库连接池**：使用连接池管理数据库连接，避免频繁创建和销毁连接
- **批量处理**：对odds、leagues、teams、matches数据进行批量插入/更新
- **查询缓存**：对频繁查询的数据进行缓存，减少数据库查询次数
- **增量更新**：只处理有变化的数据，避免不必要的数据库操作
- **异步处理**：使用异步方式处理网络请求和数据处理，提高并发性能

## 环境要求

- Python 3.8+
- PostgreSQL 12+
- Node.js 14+ (Playwright依赖)

## 安装步骤

1. 克隆项目并安装依赖：
```bash
git clone <repository_url>
cd football_spider
pip install -r requirements.txt
```

2. 安装Playwright浏览器：
```bash
playwright install
```

3. 配置PostgreSQL连接：
编辑 `.env` 文件，设置PostgreSQL连接信息：
```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=oddluck
DB_USER=postgres
DB_PASSWORD=your_password
```

## 使用方法

在运行任何爬虫之前，请先激活Python虚拟环境。在 `football_spider` 目录下执行：

```bash
cd football_spider
.\spider_venv\Scripts\activate
```

然后，你可以进入 `football_spider/spiders` 目录运行特定的爬虫。

### 1. 爬取基础数据

**爬取大洲信息 (continent_spider.py):**

```bash
scrapy crawl continent
```

**爬取国家信息 (country_spider.py):**

```bash
scrapy crawl country
```

### 2. 爬取杯赛信息

**爬取杯赛数据 (cup_spider.py):**

```bash
# 示例：爬取所有杯赛的当前赛季信息 (具体参数需根据cup_spider的实现确定)
scrapy crawl cup
# scrapy crawl cup -a season=2024-2025 # 假设支持按赛季爬取
```
直接运行 `scrapy crawl cup` 通常会尝试爬取所有已知杯赛的默认或最新赛季数据。可以通过 `-a season=` 参数指定特定赛季。

### 3. 爬取联赛信息

**爬取联赛数据 (league_spider.py):**

```bash
# 爬取指定联赛的特定赛季信息，例如意甲2024-2025赛季：
scrapy crawl league -a league=意甲 -a season=2024-2025

# 也可以使用联赛ID，例如英超 (ID:36) 2024-2025赛季：
scrapy crawl league -a league=36 -a season=2024-2025
```
如果不提供 `-a league` 和 `-a season` 参数，爬虫可能会尝试爬取所有配置中支持的联赛的默认或最新赛季数据。具体行为请参考爬虫的实现。

### 4. 爬取球队信息

**爬取球队数据 (team_spider.py):**

```bash
# 爬取指定联赛特定赛季下的所有球队信息，例如英超2024-2025赛季的球队：
scrapy crawl team -a league=英超 -a season=2024-2025

# 也可以使用联赛ID：
scrapy crawl team -a league=36 -a season=2024-2025
```
如果不提供 `-a league` 和 `-a season` 参数，爬虫可能会尝试爬取所有配置中支持的联赛的默认或最新赛季下的所有球队信息。具体行为请参考爬虫的实现。

### 5. 爬取比赛信息

**爬取比赛数据 (match_spider.py):**

**基本命令格式:**

```bash
scrapy crawl match -a competition_type="TYPE" -a season="SEASON" -a source_competition_id="SOURCE_ID" [可选参数]
```

**参数说明:**

*   `competition_type`: 必填。赛事类型，可选值为 `"league"` (联赛) 或 `"cup"` (杯赛)。
*   `season`: 必填。赛季年份。例如，跨年赛季 "2024-2025"，单年杯赛 "2024"。请确保格式与目标JS文件URL中的一致。
*   `source_competition_id`: 必填。赛事在源数据中的ID。例如，英超为 `"36"`，欧冠杯为 `"103"`。

**可选参数:**

*   `-a target_round="轮次/阶段名称"`:
    *   对于联赛，指定要爬取的数字轮次，例如 `target_round="29"`。
    *   对于杯赛，指定要爬取的阶段中文名称，例如 `target_round="联赛阶段"` 或 `target_round="第一圈"`。名称需与目标JS文件内 `arrCupKind` 变量中定义的阶段名称一致。
    *   如果未提供此参数，将爬取该赛事在该赛季下的所有轮次/阶段。
*   `-a target_team_name="球队名称"`:
    *   指定只爬取包含该球队名称（简体中文或粤语）的比赛。例如 `target_team_name="曼城"`。
    *   如果同时提供了 `target_round`，则会在指定的轮次/阶段内筛选该球队的比赛。
*   `-a target_match_source_id="比赛源ID"`: (此功能尚在完善中，目前主要通过上述参数组合爬取)
    *   理论上用于直接爬取单个特定比赛的详细信息和赔率。如果使用此参数，通常不需要 `competition_type`, `season`, `source_competition_id`。

**示例:**

1.  **爬取整个联赛赛季的所有比赛:**
    例如，爬取英超 (源ID: 36) 2024-2025 赛季的所有比赛：
    ```bash
    scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="36"
    ```

2.  **爬取联赛特定轮次的比赛:**
    例如，爬取意甲 (源ID: 34) 2024-2025 赛季第 29 轮的比赛：
    ```bash
    scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="34" -a target_round="29"
    ```

3.  **爬取整个杯赛赛季的所有主要阶段比赛:**
    例如，爬取欧冠杯 (源ID: 103) 2024-2025 赛季的所有主要比赛阶段：
    ```bash
    scrapy crawl match -a competition_type="cup" -a season="2024-2025" -a source_competition_id="103"
    ```

4.  **爬取杯赛特定阶段的比赛:**
    例如，爬取欧冠杯 (源ID: 103) 2024-2025 赛季的 "联赛阶段" 的比赛：
    ```bash
    scrapy crawl match -a competition_type="cup" -a season="2024-2025" -a source_competition_id="103" -a target_round="联赛阶段"
    ```

5.  **爬取联赛特定轮次中特定球队的比赛:**
    例如，爬取英超 (源ID: 36) 2024-2025 赛季第 30 轮中 "曼城" 的比赛：
    ```bash
    scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="36" -a target_round="30" -a target_team_name="曼城"
    ```

6.  **爬取联赛整个赛季中特定球队的比赛:**
    例如，爬取英超 (源ID: 36) 2024-2025 赛季中 "曼城" 的所有比赛：
    ```bash
    scrapy crawl match -a competition_type="league" -a season="2024-2025" -a source_competition_id="36" -a target_team_name="曼城"
    ```

**重要提示:**

*   `source_competition_id` 和赛季 `season` 的正确性对能否成功请求到数据至关重要。这些ID和赛季格式通常可以从目标网站的JS文件URL中观察得到。
*   如果爬虫启动时无法确定 `competition_db_id` (即赛事在本地数据库中的ID)，可能会影响数据关联的准确性，请检查日志中的相关警告。

### 6. 爬取赔率信息

`match_spider.py` 在爬取比赛信息的同时通常会触发赔率的爬取。赔率数据是关联在比赛上的。
如果需要单独的赔率爬取或重试机制，可能有专门的爬虫。

**赔率重试 (odds_retry_spider.py):**

假设 `odds_retry_spider.py` 用于处理之前失败或需要更新的赔率。
```bash
# 运行赔率重试爬虫 (具体参数和行为需根据其实现确定)
scrapy crawl odds_retry
# scrapy crawl odds_retry -a match_id=xxxx # 可能支持指定比赛ID进行重试
```

### 7. 使用增量更新模式

对于支持增量更新的爬虫（主要是 `match_spider.py`），可以添加 `-a incremental=true` 参数：

```bash
# 使用增量更新模式爬取英超2024-2025赛季的比赛和赔率数据：
scrapy crawl match -a league=英超 -a season=2024-2025 -a incremental=true
```
- 对于已爬取的比赛，只有当比分或关键信息有变化时才会更新。
- 对于赔率数据，只会添加新的赔率时间点的数据。
- 详细说明请参见 [增量更新使用说明](./README_incremental_update_guide-1.md)

### 8. 关于定时爬取

系统支持定时自动触发爬取任务，确保数据实时更新。这通常涉及以下时间点：
- 比赛前1天
- 比赛前6小时
- 比赛前2小时
- 比赛前15分钟

配置和启动定时爬取功能需要以下步骤：

```bash
# 1. 启动Django/Daphne后端 (包含定时任务调度器)
cd oddluck_dev
# 根据你的后端配置选择启动 ASGI (Daphne) 或 WSGI (runserver)
python start_daphne.py 
# 或者
# python start_runserver.py

# 2. 启动Scrapyd服务 (用于接收和运行爬虫任务)
# 回到 football_spider 目录并激活虚拟环境（如果尚未激活）
cd ../football_spider 
# .\spider_venv\Scripts\activate # 如果已激活则忽略此步
cd .. # 返回到 oddluck 项目根目录
scrapyd  # 运行在 http://127.0.0.1:6800/

# 3. 部署爬虫到Scrapyd服务
# (确保在 football_spider 目录下并已激活虚拟环境)
cd football_spider 
# .\spider_venv\Scripts\activate # 如果已激活则忽略此步
# 清理旧的egg文件 (可选，但推荐)
# scrapyd-deploy # 部署默认项目
# 或者指定项目名称，如果 scrapy.cfg 中定义了多个项目
scrapyd-deploy default # 假设项目名为 default

# 4. 测试定时任务 (可选)
# 方法一：修改 oddluck_dev/backend/scheduler/apps.py 中的任务执行频率进行测试。
# 方法二：手动触发单场比赛的爬取 (需要激活 oddluck_dev 的虚拟环境):
cd ../oddluck_dev
.\oddluck_venv\Scripts\activate
cd backend
python manage.py scrape_match <match_id> # 将 <match_id> 替换为实际的比赛ID
```

## 配置说明

在 `settings.py` 文件中可以配置以下参数：

```python
# 数据库连接池配置
DB_POOL_MIN_CONNECTIONS = 1
DB_POOL_MAX_CONNECTIONS = 20

# 批处理配置
BATCH_PROCESSING_ENABLED = True
BATCH_SIZE_ODDS = 100
BATCH_SIZE_LEAGUES = 10
BATCH_SIZE_TEAMS = 20
BATCH_SIZE_MATCHES = 50

# 查询缓存配置
QUERY_CACHE_ENABLED = True
QUERY_CACHE_TTL = 300  # 缓存300秒
```

## 支持的联赛

| 联赛名称 | 联赛ID |
|---------|-------|
| 英超 | 36 |
| 意甲 | 34 |
| 西甲 | 31 |
| 德甲 | 8 |
| 法甲 | 11 |

## 数据结构

### 大洲数据 (continents表)
- continent_id: 大洲ID
- continent_name: 大洲名称
- continent_name_en: 大洲英文名称

### 国家数据 (countries表)
- country_id: 国家ID
- country_name: 国家名称
- country_logo: 国家logo URL
- continent_id: 所属大洲ID

### 杯赛数据 (cups表)
- cup_id: 杯赛ID (源数据ID，例如 '283')
- cup_short_name: 杯赛简称
- cup_official_name_simp: 杯赛官方名称（简体）
- cup_official_name_cant: 杯赛官方名称（繁体）
- cup_official_name_en: 杯赛官方名称（英文）
- cup_logo: 杯赛logo (本地相对路径)
- match_type: 赛事类型 (例如 '2,0')
- current_season: 当前赛季 (例如 '2024-2025')
- available_seasons: 可用赛季列表 (逗号分隔字符串，例如 '2024-2025,2025-2026')
- continent_id: 所属大洲ID (关联 continents.continent_id)
- country_id: 所属国家ID (关联 countries.country_id)

### 联赛数据 (leagues表)
- league_id: 联赛ID
- sub_league_id: 子联赛ID
- league_short_name: 联赛简称
- league_official_name_simp: 联赛官方名称（简体）
- league_official_name_cant: 联赛官方名称（繁体）
- league_official_name_en: 联赛官方名称（英文）
- league_type: 联赛类型
- league_logo: 联赛logo URL
- available_seasons: 可用赛季
- current_season: 当前赛季
- total_rounds: 总轮次
- current_round: 当前轮次
- continent_id: 所属大洲ID
- country_id: 所属国家ID

### 球队数据 (teams表)
- team_id: 球队ID
- team_name_simp: 球队名称（简体）
- team_name_cant: 球队名称（繁体）
- team_name_en: 球队名称（英文）
- team_logo: 球队logo URL
- league_id: 所属联赛ID
- sub_league_id: 所属子联赛ID
- season: 赛季

### 比赛数据 (matches表)
- match_id: 比赛ID
- league_id: 联赛ID
- sub_league_id: 子联赛ID
- season: 赛季
- round: 轮次
- match_time: 比赛时间
- home_team_id: 主队ID
- away_team_id: 客队ID
- full_score: 全场比分
- half_score: 半场比分
- scraped_1d: 是否已触发提前1天爬取
- scraped_6h: 是否已触发提前6小时爬取
- scraped_2h: 是否已触发提前2小时爬取
- scraped_15m: 是否已触发提前15分钟爬取

### 赔率数据 (odds表)
- match_id: 比赛ID
- odds_detail_id: 欧赔详细数据ID
- bookmaker_id: 博彩公司ID（Bet365:281, 立博:82, 威廉希尔:115）
- home_win: 主胜赔率
- draw: 平局赔率
- away_win: 客胜赔率
- update_time: 更新时间

## 优化记录

1. **数据库连接优化**
   - 实现连接池，减少连接创建/销毁开销
   - 添加连接超时和重试机制

2. **数据处理优化**
   - 实现批量处理，大幅减少数据库操作
   - 添加查询缓存，减少重复查询

3. **爬虫效率优化**
   - 增量更新策略，只处理有变化的数据
   - 简化IP代理轮换，降低被封风险
   - 优化请求频率，平衡速度和稳定性

4. **代码结构优化**
   - 重构Pipeline类，提高代码可维护性
   - 优化日志输出，方便调试和监控

5. **定时任务优化**
   - 实现基于Django调度器的定时爬取
   - 自动检测并触发不同时间点的爬取任务
   - 添加爬取状态标记，避免重复触发

6. **数据结构优化**
   - 完整的大洲-国家-联赛-球队层次结构
   - 丰富的多语言支持（简体中文、繁体中文、英文）
   - 规范的字段命名和数据关联

## 注意事项

1. 请确保PostgreSQL服务已启动
2. 爬虫使用了Playwright处理JavaScript渲染，首次运行需要下载浏览器
3. 建议使用代理池避免IP被封
4. 遵守网站的robots.txt规则和爬取频率限制
5. 数据库设计时已考虑大量数据处理的性能优化，包括：
   - 合理的主键设计
   - 适当的外键约束
   - 针对常用查询的索引优化
   - 时间戳记录和自动更新
   - 支持增量更新的查询优化
6. 定时爬取功能需要同时运行Django/Daphne后端和Scrapyd服务
 
