# 足球数据爬虫 - 增量更新功能使用说明

## 功能概述

增量更新功能通过在数据写入数据库前进行检查，避免重复爬取相同的数据。该功能可以：

1. 检测大洲数据（continents表）是否已存在，避免重复爬取
2. 检测国家数据（countries表）是否已存在，避免重复爬取
3. 检测联赛数据（leagues表）是否需要更新
4. 检测球队数据（teams表）是否需要更新
5. 检测比赛数据（matches表）是否已存在，仅更新有变化的字段（如比分）
6. 检测赔率数据（odds表）是否已存在，避免重复爬取
7. 提供详细的增量更新统计信息

## 使用方法

增量更新功能已经集成到爬虫中，**无需修改任何现有代码或命令**。只需正常运行爬虫即可自动启用增量更新功能。

### 示例命令

```bash
# 爬取英超2024-2025赛季第30轮比赛，自动跳过已爬取且无变化的数据
scrapy crawl match -a league=英超 -a season=2024-2025 -a round=30
```

## 工作原理

1. **大洲数据（continents表）**：
   - 通过continent_id检查大洲是否存在
   - 如存在且无变化，则跳过

2. **国家数据（countries表）**：
   - 通过country_id检查国家是否存在
   - 如存在且无变化，则跳过

3. **联赛数据（leagues表）和球队数据（teams表）**：
   - 检查是否存在，并比较关键字段是否有变化
   - 只在需要更新时才处理数据

4. **比赛数据（matches表）**：
   - 通过match_id检查比赛是否存在
   - 如存在，比较full_score（全场比分）和half_score（半场比分）等字段
   - 只有当这些关键字段有变化时才更新数据

5. **赔率数据（odds表）**：
   - 通过match_id + odds_detail_id + bookmaker_id + update_time的组合键检查
   - 如完全匹配则跳过，不再重复爬取
   - 对于新的赔率时间点（新的update_time），会作为新记录插入

## 统计信息

在爬虫完成后，控制台日志中会输出详细的增量更新统计信息，包括：

- 新增数据的数量
- 更新的数据数量
- 因无变化而跳过的数据数量

示例输出：
```
==================================================
增量更新Pipeline 统计信息
--------------------------------------------------
大洲数据: 新增 0, 更新 0, 无变化 6
国家数据: 新增 0, 更新 0, 无变化 30
联赛数据: 新增 0, 更新 1, 无变化 0
球队数据: 新增 0, 更新 0, 无变化 40
比赛数据: 新增 0, 更新 2, 无变化 8
赔率数据: 新增 12, 无变化 48
==================================================
```

## 常见问题

1. **问**: 如何临时禁用增量更新功能，强制爬取所有数据？
   **答**: 在settings.py中注释掉IncrementalUpdatePipeline的配置行即可。

2. **问**: 增量更新会影响爬虫性能吗？
   **答**: 会有轻微影响，因为需要在处理每个数据项前查询数据库。但总体上会提高效率，因为减少了大量重复写入操作。

3. **问**: 如何确认增量更新正常工作？
   **答**: 查看日志输出中的统计信息，确认有"无变化"的数据被正确跳过。 