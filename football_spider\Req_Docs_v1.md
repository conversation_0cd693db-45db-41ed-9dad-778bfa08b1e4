# 任务
构建一个爬虫，使用scrapy+playwright，对网站 https://zq.titan007.com/info/index_cn.htm 进行爬取，目标是欧洲足球五大联赛（英超、德甲、西甲、意甲、法甲）相关的比赛数据。

# 概况
该网站大多以js形式返回数据，例如英超2024-2025赛季的数据就是通过 https://zq.titan007.com/jsData/matchResult/2024-2025/s36.js?version=2025031422 这个url返回比赛数据，其中2024-2025为赛季，36为英超的id，version为返回数据的版本号，以时间(YYYYMMDDhh格式)的形式呈现。以此为突破口，获取赛事相关数据。

# 获取数据
## 获取联赛信息
联赛信息（var arrLeague），第0个代表联赛id，第1个代表联赛全称，第4个代表赛季，第6个代表联赛logo，第7个代表总轮次，第8个代表当前轮次，其他联赛如此类推。

## 获取球队信息
球队信息（var arrTeam），第0个代表球队id，第1个代表球队名称（国语翻译，不需要获取），第2个代表球队名称（粤语翻译，获取这个），第5个代表球队logo，其他球队如此类推。

## 获取当前轮次的赛程及赛事信息
当前轮次的赛程及赛事信息，jh["R_1"]代表第1轮，第0个代表赛事id，第3个代表开赛时间，第6个代表全场比分。

## 获取博彩机构id
36*（Bet365）id为281，立*（立博）id为82，威廉希*（威廉希尔）id为115，其他博彩机构都不要。

## 获取欧赔详细数据

### 快速方法
通过 https://1x2d.titan007.com/2590898.js?r=007133864382755709740 这个url获取每场赛事的每间博彩机构的欧赔详细数据，其中2590898代表赛事id，r不知道代表什么，帮我破解。这个url也是返回js数据，主要关注var game和var gameDetail。
**关于r这个参数**
007好像是不变的。
1338643这个好像变化也不太大，但是隔了一天后再观察，就变成了1338650。
82755709740这个变化剧烈，每请求一次就会变动，例如：
30462409749
30918279297
31275726038
31608176501
这是四次请求的数字变动。
推断：
r 参数由三部分组成，格式为：固定前缀 + 日期编码 + 高精度时间戳
例如：007133864382755709740
分解为：007（固定前缀） + 1338643（日期编码） + 82755709740（时间戳）
末尾部分（高精度时间戳）：82755709740，其生成逻辑基于当前时间的高精度时间戳（毫秒或微秒级），可能混合随机数或加密哈希，样本末尾部分长度固定为 11位，与 Date.now() 生成的 13位时间戳截取后 11 位一致。
**建议**
1. 逆向 JS 逻辑：直接分析 https://1x2d.titan007.com/{match_id}.js 的代码，确认参数生成的具体算法。
2. 时间戳对齐：将末尾部分与当前时间戳对比，确认是否为截取或哈希后的结果。

**关于var game**
以Array(“博彩机构id|欧赔详细数据id|博彩机构英文名称|初主胜|初和|初客胜|初主胜率|初和率|初客胜率|初返还率|即主胜|即和|即客胜|即主胜率|即和率|即客胜率|即返还率|凯利指数主|凯利指数和|凯利指数客”)的形式呈现。不要凯利指数。不要胜率。不要返还率。

**关于var gameDetail**
以Array(“欧赔详细数据id^终赔胜|终赔平|终赔负|变化时间|终赔凯利指数胜|终赔凯利指数平|终赔凯利指数负;即赔胜|即赔平|即赔负|变化时间|即赔凯利指数胜|即赔凯利指数平|即赔凯利指数负;......;初赔胜|初赔平|初赔负|变化时间|初赔凯利指数胜|初赔凯利指数平|初赔凯利指数负;”)的形式呈现。不要凯利指数。不要胜率。不要返还率。

### 使用playwright
通过 https://1x2.titan007.com/OddsHistory.aspx?id=141813566&sid=2591181&cid=281&l=0 这个url获取每场赛事的每间博彩机构的欧赔详细数据，其中id=141813566为每间博彩机构对该场赛事的欧赔详细数据的id，sid=2591181为match_id，cid=281为博彩机构id（281为BET365的id），l=0为固定值。
**关于id**
odds_id是动态变化的，不能直接获取。
**解决办法**
1. 访问 https://1x2.titan007.com/oddslist/{match_id}.htm
2. 在页面中找到对应博彩公司（Bet365、William Hill、Ladbrokes）的行
3. 从赔率单元格的链接中提取 odds_id
4. 使用提取的参数访问 https://1x2.titan007.com/OddsHistory.aspx?id={odds_id}&sid={match_id}&cid={bookmaker_id}&l=0
5. 解析表格中的所有赔率历史数据
6. 保存每一行的赔率数据（主胜、和、客胜）和更新时间

# 永久化存储
使用PostgreSQL储存以上获得的所有数据，要求索引优化和数据压缩，做好了处理大量足球数据准备。