# 任务
构建一个爬虫，使用scrapy+playwright，对网站 https://zq.titan007.com/info/index_cn.htm 进行爬取，目标是欧洲足球五大联赛（英超、德甲、西甲、意甲、法甲）相关的比赛数据。

# 概况
该网站大多以js形式返回数据，例如英超2024-2025赛季的数据就是通过 https://zq.titan007.com/jsData/matchResult/2024-2025/s36.js?version=2025031422 这个url返回比赛数据，其中2024-2025为赛季，36为英超的id，version为返回数据的版本号，以时间(YYYYMMDDhh格式)的形式呈现。以此为突破口，获取赛事相关数据。

# 获取数据
## 获取联赛信息
联赛信息（var arrLeague），第0个代表联赛id，第1个代表联赛全称，第4个代表赛季，第6个代表联赛logo，第7个代表总轮次，第8个代表当前轮次，其他联赛如此类推。

## 获取球队信息
球队信息（var arrTeam），第0个代表球队id，第1个代表球队名称（国语翻译，不需要获取），第2个代表球队名称（粤语翻译，获取这个），第5个代表球队logo，其他球队如此类推。

## 获取当前轮次的赛程及赛事信息
当前轮次的赛程及赛事信息，jh["R_1"]代表第1轮，第0个代表赛事id，第3个代表开赛时间，第6个代表全场比分。

## 获取博彩机构id
36*（Bet365）id为281，立*（立博）id为82，威廉希*（威廉希尔）id为115，其他博彩机构都不要。

## 获取欧赔详细数据
### 使用selenium
1. 访问 https://1x2.titan007.com/oddslist/{match_id}.htm
2. 自动捕获加载的 JS 文件中包含 match_id 的请求 URL
3. 使用获取到的 URL 来抓取赔率数据（查找var game和ar gameDetail，获取相关数据）
4. 保存到数据库
**var game**
- var game=Array(“博彩机构id|欧赔详细数据id|博彩机构英文名称|初主胜|初和|初客胜|初主胜率|初和率|初客胜率|初返还率|即主胜|即和|即客胜|即主胜率|即和率|即客胜率|即返还率|凯利指数主|凯利指数和|凯利指数客”)。
- 首先找到博彩机构id，36*（英国）（即Bet365）id为281，立*（英国）（即立博）id为82，威*（英国）（即威廉希尔）id为115，其他博彩机构都不要。
- 然后根据博彩机构的id，找到欧赔详细数据id。
- 除了欧赔详细数据id，其他数据都不要。
**var gameDetail**
- var gameDetail=Array(“欧赔详细数据id^终赔胜|终赔平|终赔负|变化时间|终赔凯利指数胜|终赔凯利指数平|终赔凯利指数负|赛季;即赔胜|即赔平|即赔负|变化时间|即赔凯利指数胜|即赔凯利指数平|即赔凯利指数负|赛季;......;初赔胜|初赔平|初赔负|变化时间|初赔凯利指数胜|初赔凯利指数平|初赔凯利指数负|赛季;”)。
- 根据var game获得的欧赔详细数据id，获取该id的数据，获取终赔胜|终赔平|终赔负|变化时间，即赔胜|即赔平|即赔负|变化时间，初赔胜|初赔平|初赔负|变化时间。

# 永久化存储
使用PostgreSQL储存以上获得的所有数据，要求索引优化和数据压缩，做好了处理大量足球数据准备。