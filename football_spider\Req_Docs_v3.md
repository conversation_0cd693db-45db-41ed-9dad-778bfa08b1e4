# 任务
构建爬虫，对指定url进行爬取：
**url-1**
https://zq.titan007.com/jsData/infoHeader.js
**url-2（需要拼接）**
version = time.strftime("%Y%m%d%H", time.localtime())
无sub_league_id的情况：https://zq.titan007.com/jsData/matchResult/{season}/s{league_id}.js?version={version}，例如 https://zq.titan007.com/jsData/matchResult/2024-2025/s36.js?version=2025031422
有sub_league_id的情况：https://zq.titan007.com/jsData/matchResult/{season}/s{league_id}_{sub_league_id}.js?version={version}，例如 https://zq.titan007.com/jsData/matchResult/2024-2025/s34_2948.js?version=2025031422
**url-3（需要拼接）**
https://zq.titan007.com/jsData/matchResult/{season}/c{cup_id}.js?version={version}，例如https://zq.titan007.com/jsData/matchResult/2024-2025/c103.js?version=2025031422

## 构建continent_spider，新建一个名为continent_spider的文件（存储：数据库新建continent表）
**url-1**
大洲ID（continent_id）：第3个，如0代表国际，1代表欧洲，2代表美洲，3代表亚洲，4代表大洋洲，5代表非洲

## 构建country_spider，新建一个名为country_spider的文件（存储：数据库新建country表）
**url-1**
国家ID（country_id）：第0个，如InfoID_1
国家名称（country_name）：第1个，如英格兰
国家LOGO（country_logo）：第2个，如images/164326923816.png

## 重构league_spider，新建一个名为league_spider的文件（存储：数据库新建leagues表）
主联赛ID（league_id）：位于url-1，第4个的第0个，如36（要结合联赛类型判断，1,0代表主联赛）
子联赛ID（sub_league_id）：创建一个配置表，硬编码，如'意甲': 2948（要结合联赛类型判断，1,1代表子联赛）
联赛简称（league_short_name）：位于url-1，第4个的第1个，如英超
联赛全称（league_official_name_simp）：位于url-2（var arrLeague），第0个的第1个，如英格兰超级联赛
联赛全称（league_official_name_cant）：位于url-2（var arrLeague），第0个的第2个，如英格蘭超級聯賽
联赛全称（league_official_name_en）：位于url-2（var arrLeague），第0个的第3个，如English Premier League
联赛LOGO（league_logo）：位于url-2（var arrLeague），第0个的第6个，如league_match/images/164577482086.png
赛事类型（match_type）：位于url-1，第4个的第0个的第2第3个，如1,0代表主联赛，1,1代表子联赛，当联赛类型为子联赛，必定有sub_league_id
适用赛季（available_seasons）：位于url-1，第4个的第4个及之后，如2024-2025,2023-2024及之后
当前赛季（current_season）：位于url-2（var arrLeague），第0个的第4个，如2024-2025
总轮次（total_rounds）：位于url-2（var arrLeague），第0个的第7个，如38
当前轮次（current_round）：位于url-2（var arrLeague），第0个的第8个，如36

## 构建cup_spider，新建一个名为cup_spider的文件（存储：数据库新建cups表）
杯赛ID（cup_id）：位于url-1，第4个的第0个，如116（要结合联赛类型判断，2,0代表杯赛）
杯赛简称（cup_short_name）：位于url-1，例如第4个的第0个的第1个如捷克杯，例如第4个的第4个的第1个如英足总杯
杯赛全称（cup_official_name_simp）：位于url-3（var arrCup），第0个的第1个，如欧洲联赛冠军杯赛
杯赛全称（cup_official_name_cant）：位于url-3（var arrCup），第0个的第2个，如歐洲聯賽冠軍盃賽
杯赛全称（cup_official_name_en）：位于url-3（var arrCup），第0个的第3个，如UEFA Champions League
杯赛LOGO（cup_logo）：位于url-3（var arrCup），第0个的第8个，如league_match/images/166675006178.png
赛事类型（match_type）：位于url-1，第4个的第0个的第2第3个，如2,0代表杯赛
当前赛季（current_season）：位于url-3（var arrCup），第0个的第7个，如2024-2025

## 重构league_spider，新建一个名为team_spider的文件（存储：数据库新建teams表）
**url-2**
球队ID（team_id）：位于var arrTeam，第0个的第1个的第0个，如19
球队名称（team_name_simp）：位于var arrTeam，第0个的第1个的第1个，如阿森纳
球队名称（team_name_cant）：位于var arrTeam，第0个的第1个的第2个，如阿仙奴
球队名称（team_name_en）：位于var arrTeam，第0个的第1个的第3个，如Arsenal
球队LOGO（team_logo）：位于var arrTeam，第0个的第1个的第5个，如images/164577419697.png

## 重构match_spider
鉴于现在matches表新增了杯赛元素，而从联赛js文件获取match_id的方法不同于从杯赛js文件获取match_id的方法。
从联赛js文件路径获取，联赛js文件通常是类似 https://zq.titan007.com/jsData/matchResult/{赛季}/s{联赛id}.js 的格式，对于每一轮中的每一场比赛数据 (通常是一个数组，例如 match_info)，该数组的第一个元素 (match_info[0]) 就是这场比赛的源ID。
从杯赛js文件路径获取，杯赛js文件通常是类似https://zq.titan007.com/jsData/matchResult/{赛季}/c{杯赛id}.js 的格式，而js文件的内容跟联赛js文件（例如s36.js）的内容完全不一样，例如c103js，jh["..."]是杯赛类型（杯赛类型从var arrCupKind获取），例如jh["G25366A"]代表联赛阶段，那么联赛阶段的其中一场比赛的match_id就是2660290，例如jh["G26547"]代表决赛，那么决赛阶段的match_id就是2774466，再例如jh["G26547"]代表资格赛1，那么资格赛1阶段的其中一场比赛的match_id就是2592169，如此类推。

### 通用处理：
1.round 字段的获取：
首先，从JS内容中解析 var arrCupKind = [...]。
创建一个映射 cup_stages_map，键为杯赛阶段的源ID（stage_info[0]，例如 25352），值为阶段的中文名称（stage_info[2]，例如 '联赛阶段'）。
在处理 jh["G<ID>"] 下的比赛数据时，从 <ID>（例如从 "G25352A" 中提取 25352）中获取阶段的数字ID，然后从 cup_stages_map 中查找到对应的阶段名称，并将其作为 matches.round 字段的值。
2.cup_id 字段的填充：
从JS的比赛数据中提取的 cup_id（例如 match_info[1] 或 leg_data[1]，其值为 103）是杯赛的源ID (source_cup_id)。
在创建 MatchItem 时，实际存入 matches.cup_id (外键, int4类型) 的值应该是爬虫初始化时传入的 self.competition_db_id（即对应 cups 表中的主键 id）。您提取的源 cup_id (例如103) 可用于 MatchItem 中的一个临时字段或用于验证。

### 对于单场比赛列表的阶段 (例如 jh["G25352A"] - 联赛阶段):
该阶段的数据 jh["G<ID>"] 是一个比赛信息数组的列表。
遍历此列表中的每一个 match_info 数组：
match_id (存入 matches.match_id): match_info[0]
source_cup_id (源杯赛ID, 用于验证或临时存储): match_info[1]
match_time (存入 matches.match_time): match_info[3]
home_team_id (存入 matches.home_team_id): match_info[4]
away_team_id (存入 matches.away_team_id): match_info[5]
full_score (存入 matches.full_score): match_info[6]
half_score (存入 matches.half_score): match_info[7]
round: 根据上述方法从 arrCupKind 获取。
season: 从爬虫的 self.season 获取。
cup_id (存入 matches.cup_id): 使用 self.competition_db_id。

### 对于系列赛为主的阶段 (例如 jh["G24969"] - 资格赛轮次, 或 jh["G26116"] - 淘汰赛):
该阶段的数据 jh["G<ID>"] 是一个系列赛信息数组的列表。
遍历此列表中的每一个 series_info 数组：
第一回合比赛数据 (leg1_match_data = series_info[4]):
如果 leg1_match_data 存在且有效：
match_id: leg1_match_data[0]
source_cup_id: leg1_match_data[1]
match_time: leg1_match_data[3]
home_team_id: leg1_match_data[4]
away_team_id: leg1_match_data[5]
full_score: leg1_match_data[6]
half_score: leg1_match_data[7]
round: 根据上述方法从 arrCupKind 获取。
season: 从爬虫的 self.season 获取。
cup_id: 使用 self.competition_db_id。
第二回合比赛数据 (leg2_match_data = series_info[5]):
如果 leg2_match_data 存在且有效，则按与第一回合类似的方式提取字段。
我会确保在后续的开发中遵循这些精确的字段提取规则和 round 字段的派生逻辑，以确保数据正确地适应 matches 表的结构。

## 说明
1、数据库中的continent表、country表、leagues表、cups表、teams表之间要有机联结。
2、要求索引优化和数据压缩，做好了处理大量足球数据准备。