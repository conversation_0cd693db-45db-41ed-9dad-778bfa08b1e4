#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库连接池模块：优化数据库连接，提高性能和稳定性
"""

import os
import psycopg2
import logging
import time
import random
from psycopg2.pool import ThreadedConnectionPool
from psycopg2.extras import DictCursor
from contextlib import contextmanager
from scrapy.utils.project import get_project_settings

logger = logging.getLogger(__name__)

# 全局连接池变量
pool = None
# 全局最小连接数和最大连接数变量，从Scrapy settings获取
MIN_CONNECTIONS = 1
MAX_CONNECTIONS = 5
CONNECT_TIMEOUT = 15 # 默认连接超时时间

class DatabasePool:
    """数据库连接池管理类"""
    
    def __init__(self, minconn, maxconn, **kwargs):
        """初始化数据库连接池"""
        try:
            self.pool = ThreadedConnectionPool(
                minconn, maxconn, 
                host=kwargs.get('host', 'localhost'),
                port=kwargs.get('port', 5432),
                dbname=kwargs.get('dbname', 'oddluck'),
                user=kwargs.get('user', 'postgres'),
                password=kwargs.get('password', '888'),
                connect_timeout=kwargs.get('connect_timeout', 15),
                sslmode=kwargs.get('sslmode', 'disable')  # 本地数据库设为disable
            )
            self.closed = False
            logger.info(f"成功创建数据库连接池 (最小连接: {minconn}, 最大连接: {maxconn})")
        except Exception as e:
            logger.error(f"创建数据库连接池时出错: {str(e)}")
            self.pool = None
            self.closed = True
            raise

    @contextmanager
    def get_cursor(self, cursor_factory=DictCursor):
        """获取数据库游标，使用上下文管理器模式"""
        conn = None
        max_retry = 3
        retry_count = 0
        backoff = 1  # 初始退避时间1秒
        
        while retry_count < max_retry:
            try:
                conn = self.pool.getconn()
                cur = conn.cursor(cursor_factory=cursor_factory)
                yield cur
                break
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.error(f"获取数据库连接失败 (尝试 {retry_count}/{max_retry}): {str(e)}")
                
                if conn:
                    self.pool.putconn(conn, close=True)  # 关闭并返回连接
                    conn = None
                
                if retry_count < max_retry:
                    # 指数退避策略
                    sleep_time = backoff * (1 + random.random())
                    logger.info(f"将在 {sleep_time:.2f} 秒后重试...")
                    time.sleep(sleep_time)
                    backoff *= 2  # 指数增长
                else:
                    logger.error("达到最大重试次数，放弃重试")
                    raise
            except Exception as e:
                logger.error(f"获取数据库游标时出错: {str(e)}")
                if conn:
                    self.pool.putconn(conn, close=True)
                    conn = None
                raise
            finally:
                if conn:
                    self.pool.putconn(conn)
    
    def commit(self):
        """提交事务"""
        conn = None
        try:
            conn = self.pool.getconn()
            conn.commit()
        except Exception as e:
            logger.error(f"提交事务时出错: {str(e)}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def rollback(self):
        """回滚事务"""
        conn = None
        try:
            conn = self.pool.getconn()
            conn.rollback()
        except Exception as e:
            logger.error(f"回滚事务时出错: {str(e)}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)

    def close(self):
        """关闭连接池"""
        if self.pool and not self.closed:
            self.pool.closeall()
            self.closed = True
            logger.info("数据库连接池已关闭")
    
    def __del__(self):
        """析构函数：确保连接池正确关闭"""
        self.close()

    def getconn(self):
        """直接从内部连接池获取连接。"""
        if self.closed or not self.pool:
            raise Exception("Database pool is closed or not initialized.")
        # 这里可以添加重试逻辑，或者直接抛出 psycopg2 的异常
        try:
            return self.pool.getconn()
        except Exception as e:
            logger.error(f"Failed to get connection from internal pool: {e}")
            raise # 重新抛出异常
            
    def putconn(self, conn, close=False):
        """将连接放回内部连接池。"""
        if self.closed or not self.pool:
            logger.warning("Attempted to put connection back into a closed or uninitialized pool.")
            return
        try:
            self.pool.putconn(conn, close=close)
        except Exception as e:
            logger.error(f"Failed to put connection back into internal pool: {e}")
            # 根据需要处理异常，例如记录或尝试强制关闭连接

def init_db_pool(settings_obj):
    """
    初始化数据库连接池。
    这个函数应该在爬虫启动时被调用，例如在Spider的__init__或pipeline的from_crawler中。
    """
    global pool, MIN_CONNECTIONS, MAX_CONNECTIONS, CONNECT_TIMEOUT
    if pool is None:
        try:
            MIN_CONNECTIONS = settings_obj.getint('DB_MIN_CONNECTIONS', 1)
            MAX_CONNECTIONS = settings_obj.getint('DB_MAX_CONNECTIONS', 5)
            CONNECT_TIMEOUT = settings_obj.getint('DB_CONNECT_TIMEOUT', 15)

            host = settings_obj.get('POSTGRESQL_HOST', 'localhost')
            port = settings_obj.getint('POSTGRESQL_PORT', 5432)
            dbname = settings_obj.get('POSTGRESQL_DATABASE', 'oddluck')
            user = settings_obj.get('POSTGRESQL_USER', 'postgres')
            password = settings_obj.get('POSTGRESQL_PASSWORD') # 从settings获取密码
            sslmode = settings_obj.get('POSTGRESQL_SSLMODE', 'disable')

            if password is None:
                logger.warning("警告：数据库密码 (POSTGRESQL_PASSWORD) 未在 Scrapy settings 中配置! 将尝试环境变量 DB_PASSWORD。")
                password = os.environ.get('DB_PASSWORD') # 尝试从环境变量获取
                if password is None:
                    logger.error("错误：环境变量 DB_PASSWORD 也未设置。无法连接数据库。")
                    # 在这里可以决定是抛出异常还是使用一个明确无效的密码来导致连接失败
                    # raise ValueError("数据库密码未配置")
                    password = "INVALID_PASSWORD_CONFIG" # 或者一个明确的值，让连接尝试失败

            logger.info(f"Initializing database pool with: host={host}, port={port}, dbname={dbname}, user={user}, minconn={MIN_CONNECTIONS}, maxconn={MAX_CONNECTIONS}, connect_timeout={CONNECT_TIMEOUT}, sslmode={sslmode}")
            
            # 使用psycopg2的连接池
            pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=MIN_CONNECTIONS,
                maxconn=MAX_CONNECTIONS,
                host=host,
                port=port,
                dbname=dbname,
                user=user,
                password=password,
                sslmode=sslmode,
                connect_timeout=CONNECT_TIMEOUT # 设置连接超时
            )
            if pool:
                logger.info(f"数据库连接池初始化成功。Min connections: {MIN_CONNECTIONS}, Max connections: {MAX_CONNECTIONS}")
            else:
                logger.error("数据库连接池初始化失败。")
        except Exception as e:
            logger.error(f"初始化数据库连接池失败: {e}", exc_info=True)
            pool = None # 确保在失败时pool仍然是None
    return pool

def get_db_pool():
    """获取已经初始化的数据库连接池"""
    global pool
    if not pool:
        logger.error("数据库连接池未初始化！")
        raise Exception("数据库连接池未初始化")
    return pool 

class DbPool:
    """
    数据库连接池类，提供更简洁的接口，用于新的pipeline
    """
    def __init__(self, settings):
        self.logger = logging.getLogger('DbPool')
        # self.pools = {} # 这个pools属性似乎没有被使用，如果确实不需要，可以考虑移除
        
        # 从 Scrapy settings 获取数据库连接信息
        host = settings.get('POSTGRESQL_HOST', 'localhost')
        port = int(settings.get('POSTGRESQL_PORT', 5432))
        dbname = settings.get('POSTGRESQL_DATABASE', 'oddluck')
        user = settings.get('POSTGRESQL_USER', 'postgres')
        password_from_settings = settings.get('POSTGRESQL_PASSWORD')

        if password_from_settings is None:
            self.logger.warning("警告 (DbPool)：settings 中的 POSTGRESQL_PASSWORD 为 None! 将尝试环境变量 DB_PASSWORD。")
            password_from_settings = os.environ.get('DB_PASSWORD')
            if password_from_settings is None:
                self.logger.error("错误 (DbPool)：环境变量 DB_PASSWORD 也为 None!")
                # 在生产环境中，这里可能应该抛出异常或使用一个明确会失败的密码
                # For now, to prevent None in db_config which psycopg2 dislikes for password
                password_from_settings = "FALLBACK_PASSWORD_DBPOOL_INIT_ERROR" 
        elif password_from_settings == '':
            self.logger.warning("警告 (DbPool)：settings 中的 POSTGRESQL_PASSWORD 为空字符串!")
            # psycopg2 might treat empty string as no password, similar to None.
            # Consider if this should also be a fallback or error in production.

        final_password_for_db_config = password_from_settings
        
        sslmode = settings.get('POSTGRESQL_SSLMODE', 'disable') # 从settings获取sslmode
        connect_timeout = int(settings.get('DB_CONNECT_TIMEOUT', 15)) # 从settings获取连接超时

        self.db_config = {
            'host': host,
            'port': port,
            'dbname': dbname,
            'user': user,
            'password': final_password_for_db_config, # 使用最终确定的密码
            'sslmode': sslmode,
            'connect_timeout': connect_timeout
        }
        
        self.logger.info(f"DbPool 数据库连接配置: host={host}, port={port}, dbname={dbname}, user={user}, sslmode={sslmode}, connect_timeout={connect_timeout}")
        
        self.pool = None
        self._connection = None 
        self.use_pool = True 

        if self.use_pool:
            try:
                self.pool = psycopg2.pool.ThreadedConnectionPool(
                    minconn=settings.getint('DB_MIN_CONNECTIONS', 1),
                    maxconn=settings.getint('DB_MAX_CONNECTIONS', 5),
                    **self.db_config
                )
                if self.pool:
                    self.logger.info(f"DbPool内部连接池初始化成功。Min connections: {settings.getint('DB_MIN_CONNECTIONS', 1)}, Max connections: {settings.getint('DB_MAX_CONNECTIONS', 5)}")
                else:
                    self.logger.error("DbPool内部连接池初始化失败。")
            except Exception as e:
                self.logger.error(f"DbPool内部连接池初始化失败: {e}", exc_info=True)
                self.pool = None

    def getconn(self):
        """获取一个数据库连接"""
        if self.use_pool and self.pool:
            try:
                conn = self.pool.getconn()
                return conn
            except Exception as e:
                self.logger.error(f"从连接池获取连接失败: {e}")
                raise
        else:
            # 如果不使用连接池或连接池初始化失败，则直接创建新连接
            try:
                conn = psycopg2.connect(**self.db_config)
                return conn
            except Exception as e:
                self.logger.error(f"创建数据库连接失败: {e}")
                raise

    def putconn(self, conn, close=False):
        """将连接归还到连接池"""
        if conn is None:
            return
            
        if self.use_pool and self.pool:
            try:
                self.pool.putconn(conn, close=close)
            except Exception as e:
                self.logger.error(f"归还连接到连接池失败: {e}")
                # 如果归还失败，尝试直接关闭连接
                try:
                    conn.close()
                except:
                    pass
        else:
            # 如果没有使用连接池，直接关闭连接
            try:
                conn.close()
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {e}")

    @contextmanager
    def get_connection(self):
        """获取一个数据库连接"""
        conn = None
        max_retry = 3
        retry_count = 0
        backoff = 1  # 初始退避时间1秒
        
        while retry_count < max_retry:
            try:
                conn = psycopg2.connect(**self.db_config)
                yield conn
                return
            except Exception as e:
                retry_count += 1
                self.logger.error(f"获取数据库连接失败 (尝试 {retry_count}/{max_retry}): {str(e)}")
                
                if conn:
                    conn.close()
                    conn = None
                
                if retry_count < max_retry:
                    # 指数退避策略
                    sleep_time = backoff * (1 + random.random())
                    self.logger.info(f"将在 {sleep_time:.2f} 秒后重试...")
                    time.sleep(sleep_time)
                    backoff *= 2  # 指数增长
                else:
                    self.logger.error("达到最大重试次数，无法获取数据库连接")
                    # Re-raise the last exception to notify the caller (pipeline)
                    raise e
            except Exception as e:
                # Handle potential errors during initial connect or yield itself
                retry_count = max_retry # Ensure loop terminates if error happens outside connect retry
                self.logger.error(f"获取数据库连接或在yield期间发生错误: {str(e)}", exc_info=True)
                if conn:
                    conn.close()
                    conn = None
                raise e # Re-raise the exception
            finally:
                # The connection should be closed by the caller within the 'with' block
                # No action needed here for connection return as we are not using a pool in this version
                pass
    
    def close_all(self):
        """关闭所有连接池（如果存在）"""
        # DbPool 类管理一个单一的连接池实例，存储在 self.pool
        # 因此，只需要关闭 self.pool 即可。
        if hasattr(self, 'pool') and self.pool:
            try:
                # 假设 self.pool 是 ThreadedConnectionPool 实例
                self.pool.closeall() # ThreadedConnectionPool 使用 closeall
                self.logger.info("DbPool内部连接池已正确关闭。")
                self.pool = None  # 关闭后置为None，防止重复关闭
            except AttributeError as ae:
                # 如果 self.pool 不是预期的 ThreadedConnectionPool 类型，可能没有 closeall 方法
                self.logger.error(f"尝试关闭DbPool内部连接池时发生AttributeError (可能是类型不匹配): {ae}")
            except Exception as e:
                self.logger.error(f"关闭DbPool内部连接池时发生未知错误: {e}")
        else:
            self.logger.info("DbPool内部连接池未初始化或已关闭，无需操作。")

        # 移除对不存在的 self.pools 的引用
        # for key, pool in self.pools.items():
        #     try:
        #         pool.closeall()
        #         self.logger.info(f"DbPool: 连接池 {key} 已关闭。")
        #     except Exception as e:
        #         self.logger.error(f"关闭连接池 {key} 时出错: {e}")
        # if hasattr(self, 'pools'): # 确保在尝试清空前属性存在
        #    self.pools.clear() 