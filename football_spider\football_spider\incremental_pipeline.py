import logging
from itemadapter import ItemAdapter
from .items import LeagueItem, TeamItem, MatchItem, ContinentItem, CountryItem, CupItem
from datetime import datetime
import uuid
import psycopg2
from psycopg2.extras import DictCursor
import time
import re
import dateutil.parser
import os
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

logger = logging.getLogger(__name__)

class IncrementalUpdatePipeline:
    """
    增量更新Pipeline
    在数据写入数据库前检查是否已存在相同数据，避免重复爬取
    对于已存在的数据，检查关键字段是否有更新，只更新变化的部分
    """
    
    def __init__(self, settings):
        self.settings = settings
        # 从settings对象获取数据库连接参数
        self.host = settings.get('POSTGRESQL_HOST', 'localhost')
        self.port = int(settings.get('POSTGRESQL_PORT', 5432))
        self.dbname = settings.get('POSTGRESQL_DATABASE', 'oddluck')
        self.user = settings.get('POSTGRESQL_USER', 'postgres')
        self.password = settings.get('POSTGRESQL_PASSWORD') # 直接从settings获取
        if self.password is None:
            logger.warning("警告：数据库密码 (POSTGRESQL_PASSWORD) 未在 Scrapy settings 中配置! 将尝试环境变量 DB_PASSWORD。")
            self.password = os.environ.get('DB_PASSWORD')
            if self.password is None:
                logger.error("错误：数据库密码也未在环境变量 DB_PASSWORD 中找到! 请检查配置。")
                # 可以选择抛出异常或使用一个明确会失败的密码
                self.password = "FALLBACK_PASSWORD_INCREMENTAL_PIPELINE" 

        self.sslmode = settings.get('POSTGRESQL_SSLMODE', 'disable')
        self.conn = None
        self.cur = None
        
        # 统计信息
        self.stats = {
            'league': {'new': 0, 'updated': 0, 'unchanged': 0},
            'team': {'new': 0, 'updated': 0, 'unchanged': 0},
            'match': {'new': 0, 'updated': 0, 'unchanged': 0},
            'odds': {'new': 0, 'unchanged': 0},  # odds通常只有新增，不更新
            'continent': {'new': 0, 'updated': 0, 'unchanged': 0},
            'country': {'new': 0, 'updated': 0, 'unchanged': 0},
            'cup': {'new': 0, 'updated': 0, 'unchanged': 0}
        }
        
        # 添加match_id映射表，用于跟踪和记录match_id的替换
        self.match_id_mapping = {}  # 原始match_id -> 数据库match_id
        
    @classmethod
    def from_crawler(cls, crawler):
        # 简化：直接传递crawler.settings给构造函数
        return cls(settings=crawler.settings)

    def open_spider(self, spider):
        """爬虫启动时连接数据库"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                logger.info(f"增量更新Pipeline正在连接数据库... Host: {self.host}, Port: {self.port}, Database: {self.dbname}, SSL: {self.sslmode}")
                self.conn = psycopg2.connect(
                    host=self.host,
                    port=self.port,
                    dbname=self.dbname,
                    user=self.user,
                    password=self.password,
                    connect_timeout=10,  # 设置连接超时
                    sslmode=self.sslmode  # 添加SSL模式
                )
                self.cur = self.conn.cursor(cursor_factory=DictCursor)
                
                # 测试数据库连接是否正常
                self.cur.execute('SELECT version();')
                version = self.cur.fetchone()
                logger.info(f"增量更新Pipeline数据库连接成功，PostgreSQL版本: {version[0]}")
                
                # 检查数据表是否存在
                tables_to_check = ['leagues', 'teams', 'matches', 'odds', 'continents', 'countries', 'cups', 'odds_retry_queue', 'spider_logs']
                for table in tables_to_check:
                    self.cur.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' 
                            AND table_name = '{table}'
                        );
                    """)
                    table_exists = self.cur.fetchone()[0]
                    if table_exists:
                        logger.info(f"✓ 表 {table} 存在")
                        
                        # 检查表中记录数量
                        self.cur.execute(f"SELECT COUNT(*) FROM {table};")
                        count = self.cur.fetchone()[0]
                        logger.info(f"  - {table} 表中有 {count} 条记录")
                    else:
                        logger.error(f"✗ 表 {table} 不存在！")
                
                # 连接成功，跳出循环
                break
                
            except Exception as e:
                retry_count += 1
                logger.error(f"增量更新Pipeline连接数据库时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
                
                if retry_count < max_retries:
                    retry_wait = 2 ** retry_count  # 指数退避策略
                    logger.info(f"将在 {retry_wait} 秒后重试连接...")
                    time.sleep(retry_wait)
                else:
                    logger.error(f"增量更新Pipeline在 {max_retries} 次尝试后仍无法连接数据库，放弃")
                    raise

    def close_spider(self, spider):
        """爬虫关闭时关闭数据库连接并输出统计信息"""
        if self.cur:
            self.cur.close()
        if self.conn:
            self.conn.close()
            
        # 输出match_id映射信息
        if self.match_id_mapping:
            logger.info("=" * 50)
            logger.info(f"Match ID 映射表 (共 {len(self.match_id_mapping)} 条)")
            logger.info("-" * 50)
            for original_id, mapped_id in self.match_id_mapping.items():
                logger.info(f"原始ID: {original_id} -> 数据库ID: {mapped_id}")
            logger.info("=" * 50)
        
        # 输出统计信息
        stats_info = [
            "=" * 50,
            "增量更新Pipeline 统计信息",
            "-" * 50,
            f"联赛数据: 新增 {self.stats['league']['new']}, 更新 {self.stats['league']['updated']}, 无变化 {self.stats['league']['unchanged']}",
            f"球队数据: 新增 {self.stats['team']['new']}, 更新 {self.stats['team']['updated']}, 无变化 {self.stats['team']['unchanged']}",
            f"比赛数据: 新增 {self.stats['match']['new']}, 更新 {self.stats['match']['updated']}, 无变化 {self.stats['match']['unchanged']}",
            f"赔率数据: 新增 {self.stats['odds']['new']}, 无变化 {self.stats['odds']['unchanged']}",
            f"大洲数据: 新增 {self.stats['continent']['new']}, 更新 {self.stats['continent']['updated']}, 无变化 {self.stats['continent']['unchanged']}",
            f"国家数据: 新增 {self.stats['country']['new']}, 更新 {self.stats['country']['updated']}, 无变化 {self.stats['country']['unchanged']}",
            f"杯赛数据: 新增 {self.stats['cup']['new']}, 更新 {self.stats['cup']['updated']}, 无变化 {self.stats['cup']['unchanged']}",
            "=" * 50
        ]
        
        # 将统计信息记录到日志中
        for line in stats_info:
            logger.info(line)
            
        # 如果爬虫有task_id，将统计信息添加到爬虫日志中
        if hasattr(spider, 'task_id') and spider.task_id and hasattr(spider, 'update_task_status'):
            try:
                # 构建一个简洁的统计摘要
                stats_summary = f"增量更新统计: 比赛(新增:{self.stats['match']['new']}/更新:{self.stats['match']['updated']}/无变化:{self.stats['match']['unchanged']}), 赔率(新增:{self.stats['odds']['new']}/无变化:{self.stats['odds']['unchanged']})"
                
                # 如果有ID映射，添加到摘要中
                id_mapping_info = ""
                if self.match_id_mapping:
                    id_mapping_info = f", 检测到{len(self.match_id_mapping)}个match_id映射关系"
                
                # 更新爬虫状态，添加统计信息
                spider.update_task_status(
                    progress=spider.processed_match_count * 100 // max(spider.total_match_count, 1),
                    message=f"增量更新完成，{stats_summary}{id_mapping_info}"
                )
            except Exception as e:
                logger.error(f"更新爬虫任务统计信息失败: {str(e)}")

    def process_item(self, item, spider):
        """处理数据项，根据不同类型进行增量更新处理"""
        try:
            if isinstance(item, LeagueItem):
                return self._process_league(item, spider)
            elif isinstance(item, TeamItem):
                return self._process_team(item, spider)
            elif isinstance(item, MatchItem):
                return self._process_match(item, spider)
            elif isinstance(item, ContinentItem):
                return self._process_continent(item, spider)
            elif isinstance(item, CountryItem):
                return self._process_country(item, spider)
            elif isinstance(item, CupItem):
                return self._process_cup(item, spider)
            elif isinstance(item, dict) and 'match_id' in item and 'update_time' in item:
                # 对于赔率数据，先检查match_id是否存在于数据库中
                try:
                    trace_id = str(uuid.uuid4())[:8]
                    original_match_id = item['match_id']
                    
                    # 查询数据库验证match_id是否存在
                    query = "SELECT 1 FROM matches WHERE match_id = %s LIMIT 1"
                    self.cur.execute(query, (original_match_id,))
                    match_exists = self.cur.fetchone()
                    
                    if not match_exists:
                        logger.warning(f"[{trace_id}] 赔率数据引用的match_id {original_match_id} 在数据库中不存在，尝试查找正确的match_id")
                        
                        # 尝试通过odds_detail_id查找可能的match_id
                        if 'odds_detail_id' in item:
                            query = """
                                SELECT DISTINCT match_id 
                                FROM odds 
                                WHERE odds_detail_id = %s 
                                ORDER BY created_at DESC 
                                LIMIT 1
                            """
                            self.cur.execute(query, (item['odds_detail_id'],))
                            existing_odds = self.cur.fetchone()
                            
                            if existing_odds:
                                logger.warning(f"[{trace_id}] 通过odds_detail_id {item['odds_detail_id']} 找到可能的match_id: {existing_odds[0]}")
                                item['match_id'] = existing_odds[0]
                    
                    # 如果仍未找到合适的match_id，使用其它条件尝试找到类似的比赛
                    # 但这里不进行复杂匹配，留给_process_odds处理
                    
                # 无论如何都处理赔率数据
                except Exception as e:
                    logger.error(f"检查赔率关联的match_id时出错: {str(e)}")
                
                # 进行正常的赔率数据处理
                return self._process_odds(item, spider)
            else:
                return item
        except Exception as e:
            logger.error(f"增量更新Pipeline处理数据时出错: {str(e)}")
            return item

    def _process_league(self, item, spider):
        """处理联赛数据的增量更新"""
        adapter = ItemAdapter(item)
        try:
            # 检查数据库中是否存在该联赛记录
            query = """
                SELECT league_id, sub_league_id, league_type, league_short_name, league_official_name_en, 
                       current_season, league_logo, total_rounds, current_round, continent_id, country_id
                FROM leagues
                WHERE league_id = %s AND current_season = %s AND sub_league_id IS NOT DISTINCT FROM %s
            """
            # 使用 adapter.get 确保安全访问 item 字段
            league_id_val = adapter.get('league_id')
            current_season_val = adapter.get('current_season')
            sub_league_id_val = adapter.get('sub_league_id')

            self.cur.execute(query, (league_id_val, current_season_val, sub_league_id_val))
            existing_league = self.cur.fetchone() # existing_league is a DictRow
            
            if not existing_league:
                logger.info(f"联赛数据不存在，新增: {adapter.get('league_short_name')} (ID: {league_id_val}, 赛季: {current_season_val})")
                self.stats['league']['new'] += 1
                return item
            
            need_update = False
            update_details = []

            # 定义要比较的字段映射: (ItemFieldKey, DB_Column_Key_in_existing_league)
            # 假设数据库列名与 ItemAdapter 返回的键名大部分一致，除了特别指出的
            # 注意: existing_league 是 DictRow, 可以直接用列名作为键
            fields_map = {
                'league_type': 'league_type',
                'league_short_name': 'league_short_name',
                'league_official_name_en': 'league_official_name_en',
                'league_logo': 'league_logo',
                'total_rounds': 'total_rounds',
                'current_round': 'current_round',
                'available_seasons': 'available_seasons', # 假设数据库也有此列，并且类型兼容比较
                'continent_id': 'continent_id',
                'country_id': 'country_id'
            }

            for item_key, db_key in fields_map.items():
                item_value = adapter.get(item_key)
                db_value = existing_league.get(db_key)

                # 类型规范化: total_rounds 和 current_round 确保为 int 或 None
                if item_key in ['total_rounds', 'current_round']:
                    if item_value is not None:
                        try:
                            item_value = int(item_value)
                        except (ValueError, TypeError):
                            item_value = None #无法转换则视为None
                    if db_value is not None:
                        try:
                            db_value = int(db_value)
                        except (ValueError, TypeError):
                            db_value = None #无法转换则视为None
                
                # 特殊处理 continent_id 和 country_id, 它们在数据库中是 INTEGER/VARCHAR, item中可能是字符串
                if item_key in ['continent_id', 'country_id'] and item_value is not None and db_value is not None:
                    if str(item_value) != str(db_value):
                        need_update = True
                        update_details.append(f"{item_key}: {db_value} -> {item_value}")
                elif item_value != db_value:
                    # 对于其他字段，如果 item_value 不是 None (即爬虫提供了这个值)
                    # 并且它与数据库中的值不同，则标记更新。
                    # 如果 item_value 是 None 但 db_value 不是 None，也视为需要更新 (清除数据)。
                    if item_value is not None or (item_value is None and db_value is not None):
                        need_update = True
                        update_details.append(f"{item_key}: {db_value} -> {item_value}")
            
            if need_update:
                self.stats['league']['updated'] += 1
                logger.info(f"联赛 {adapter.get('league_short_name')} (ID: {league_id_val}) 需要更新. 变动: {'; '.join(update_details)}")
                return item
            else:
                self.stats['league']['unchanged'] += 1
                logger.info(f"联赛数据无变化，跳过: {adapter.get('league_short_name')} (ID: {league_id_val}, 赛季: {current_season_val})")
                return None
                
        except Exception as e:
            logger.error(f"处理联赛增量更新时出错: {str(e)}")
            # 出错时继续处理，确保数据不丢失
            return item

    def _process_team(self, item, spider):
        """处理球队数据的增量更新"""
        try:
            # 检查数据库中是否存在该球队记录
            query = """
                SELECT team_id, team_name, team_name_en, team_logo, league_id, sub_league_id, season
                FROM teams
                WHERE team_id = %s AND league_id = %s AND sub_league_id IS NOT DISTINCT FROM %s
            """
            self.cur.execute(query, (item['team_id'], item['league_id'], item.get('sub_league_id')))
            existing_team = self.cur.fetchone()
            
            if not existing_team:
                # 不存在，标记为新数据，继续处理
                logger.info(f"球队数据不存在，新增: {item['team_name']} (ID: {item['team_id']})")
                self.stats['team']['new'] += 1
                return item
            
            # 存在，检查是否有字段需要更新
            need_update = False
            fields_to_check = ['team_name', 'team_name_en', 'team_logo', 'season']
            
            for field in fields_to_check:
                if field in item and item[field] != existing_team[field]:
                    # 如果字段值不同，需要更新
                    need_update = True
                    logger.info(f"球队 {item['team_name']} (ID: {item['team_id']}) 字段 {field} 需要更新: {existing_team[field]} -> {item[field]}")
            
            if need_update:
                self.stats['team']['updated'] += 1
                return item
            else:
                # 所有字段都相同，不需要更新
                self.stats['team']['unchanged'] += 1
                logger.info(f"球队数据无变化，跳过: {item['team_name']} (ID: {item['team_id']})")
                # 返回None，阻止后续Pipeline处理
                return None
                
        except Exception as e:
            logger.error(f"处理球队增量更新时出错: {str(e)}")
            # 出错时继续处理，确保数据不丢失
            return item

    def _process_match(self, item, spider):
        """处理比赛数据的增量更新"""
        # 生成一个跟踪ID，用于跟踪整个处理过程的日志
        trace_id = str(uuid.uuid4())[:8]
        original_match_id = item['match_id']  # 保存原始match_id
        
        try:
            # 详细记录要处理的比赛信息
            logger.warning(f"[{trace_id}] 处理比赛数据: match_id={item['match_id']}, round={item['round']}, "
                        f"league_id={item['league_id']}, season={item['season']}, "
                        f"home_team={item['home_team_id']}, away_team={item['away_team_id']}")
            
            # 方法1：尝试直接通过match_id查找，这是最精确的查找方式
            query = """
                SELECT match_id, league_id, sub_league_id, season, round, 
                      match_time, home_team_id, away_team_id, full_score, half_score,
                      created_at, updated_at
                FROM matches
                WHERE match_id = %s
            """
            self.cur.execute(query, (item['match_id'],))
            exact_match = self.cur.fetchone()
            
            if exact_match:
                logger.warning(f"[{trace_id}] 【方法1】通过match_id找到精确匹配记录: match_id={exact_match[0]}")
                existing_match = exact_match
            else:
                # 检查match_id_mapping中是否已有映射
                if original_match_id in self.match_id_mapping:
                    mapped_match_id = self.match_id_mapping[original_match_id]
                    logger.warning(f"[{trace_id}] 在映射表中找到ID映射: {original_match_id} -> {mapped_match_id}")
                    
                    # 使用映射的ID查询
                    self.cur.execute(query, (mapped_match_id,))
                    mapped_match = self.cur.fetchone()
                    
                    if mapped_match:
                        logger.warning(f"[{trace_id}] 使用映射ID {mapped_match_id} 找到匹配记录")
                        existing_match = mapped_match
                        # 更新item的match_id
                        item['match_id'] = mapped_match_id
                    else:
                        logger.warning(f"[{trace_id}] 使用映射ID {mapped_match_id} 未找到记录，继续尝试其他匹配方法")
                        # 继续尝试其他匹配方法
                        existing_match = None
                else:
                    logger.warning(f"[{trace_id}] 【方法1】通过match_id未找到精确匹配记录，尝试其他条件查询")
                    existing_match = None
                
                # 如果仍未找到匹配，继续其他方法
                if not existing_match:
                    # 方法2：通过联赛、赛季、轮次和主客队ID查询，这是最全面的匹配条件
                    query = """
                        SELECT match_id, league_id, sub_league_id, season, round, 
                              match_time, home_team_id, away_team_id, full_score, half_score,
                              created_at, updated_at
                        FROM matches
                        WHERE league_id = %s AND season = %s AND round = %s 
                        AND home_team_id = %s AND away_team_id = %s
                    """
                    self.cur.execute(query, (
                        item['league_id'],
                        item['season'],
                        item['round'],
                        item['home_team_id'],
                        item['away_team_id']
                    ))
                    similar_matches = self.cur.fetchall()
                    
                    if similar_matches:
                        logger.warning(f"[{trace_id}] 【方法2】通过联赛、赛季、轮次、球队找到 {len(similar_matches)} 个相似记录")
                        
                        # 如果只有一条记录，直接使用
                        if len(similar_matches) == 1:
                            existing_match = similar_matches[0]
                            logger.warning(f"[{trace_id}] 【方法2】找到唯一匹配的相似记录: match_id={existing_match[0]}")
                        else:
                            # 如果有多条记录，尝试通过比赛时间找到最匹配的一条
                            logger.warning(f"[{trace_id}] 【方法2】找到多条相似记录，尝试通过比赛时间匹配")
                            closest_match = None
                            min_diff = float('inf')
                            
                            for match in similar_matches:
                                if match[5] and item['match_time']:  # match_time index is 5
                                    try:
                                        # 尝试解析时间并比较
                                        time_formats = [
                                            '%Y-%m-%d %H:%M:%S',
                                            '%Y-%m-%d %H:%M',
                                            '%Y-%m-%d',
                                        ]
                                        
                                        db_time = None
                                        item_time = None
                                        
                                        # 尝试解析数据库时间
                                        for fmt in time_formats:
                                            try:
                                                db_time = datetime.strptime(str(match[5]), fmt)
                                                break
                                            except ValueError:
                                                continue
                                        
                                        # 尝试解析爬取时间
                                        for fmt in time_formats:
                                            try:
                                                item_time = datetime.strptime(str(item['match_time']), fmt)
                                                break
                                            except ValueError:
                                                continue
                                        
                                        if db_time and item_time:
                                            diff = abs((db_time - item_time).total_seconds())
                                            if diff < min_diff:
                                                min_diff = diff
                                                closest_match = match
                                    except Exception as e:
                                        logger.warning(f"[{trace_id}] 比较时间出错: {str(e)}")
                            
                            if closest_match:
                                existing_match = closest_match
                                logger.warning(f"[{trace_id}] 【方法2】选择最接近的时间匹配记录: match_id={existing_match[0]}, 时间差: {min_diff}秒")
                            else:
                                # 如果无法通过时间匹配，使用第一条记录
                                existing_match = similar_matches[0]
                                logger.warning(f"[{trace_id}] 【方法2】无法通过时间匹配，使用第一条记录: match_id={existing_match[0]}")
                    else:
                        # 方法3：放宽条件，通过联赛、赛季、轮次和至少一支球队ID查询
                        query = """
                            SELECT match_id, league_id, sub_league_id, season, round, 
                                  match_time, home_team_id, away_team_id, full_score, half_score,
                                  created_at, updated_at
                            FROM matches
                            WHERE league_id = %s AND season = %s AND round = %s 
                            AND (home_team_id = %s OR away_team_id = %s)
                        """
                        self.cur.execute(query, (
                            item['league_id'],
                            item['season'],
                            item['round'],
                            item['home_team_id'],
                            item['away_team_id']
                        ))
                        partial_matches = self.cur.fetchall()
                        
                        if partial_matches:
                            logger.warning(f"[{trace_id}] 【方法3】通过联赛、赛季、轮次和至少一支球队找到 {len(partial_matches)} 个可能的记录")
                            
                            # 检查是否有球队对换的记录（主客场对换）
                            for match in partial_matches:
                                if (match[6] == item['away_team_id'] and match[7] == item['home_team_id']):
                                    logger.warning(f"[{trace_id}] 【方法3】发现主客场对换的比赛: match_id={match[0]}, "
                                                f"数据库主队={match[6]}, 客队={match[7]}; "
                                                f"爬取主队={item['home_team_id']}, 客队={item['away_team_id']}")
                                    existing_match = match
                                    break
                            else:
                                # 如果没有主客场对换的记录，使用第一条记录
                                existing_match = partial_matches[0]
                                logger.warning(f"[{trace_id}] 【方法3】使用相似记录: match_id={existing_match[0]}")
                        else:
                            # 方法4：只通过联赛、赛季和轮次查询，忽略球队ID，查找可能的记录
                            query = """
                                SELECT match_id, league_id, sub_league_id, season, round, 
                                      match_time, home_team_id, away_team_id, full_score, half_score,
                                      created_at, updated_at
                                FROM matches
                                WHERE league_id = %s AND season = %s AND round = %s
                            """
                            self.cur.execute(query, (
                                item['league_id'],
                                item['season'],
                                item['round']
                            ))
                            round_matches = self.cur.fetchall()
                            
                            if round_matches:
                                logger.warning(f"[{trace_id}] 【方法4】该轮次已有 {len(round_matches)} 场比赛记录，检查是否有类似的比赛")
                                # 打印所有该轮次比赛以供调试
                                for match in round_matches:
                                    logger.warning(f"[{trace_id}] 轮次比赛记录: match_id={match[0]}, "
                                                f"home_team={match[6]}, away_team={match[7]}, time={match[5]}")
                                
                                # 没有找到明确匹配的记录，标记为新数据
                                logger.warning(f"[{trace_id}] 【方法4】该轮次有比赛，但未找到匹配的记录，将作为新数据添加")
                                existing_match = None
                            else:
                                # 该轮次没有比赛记录
                                logger.warning(f"[{trace_id}] 【方法4】该轮次没有比赛记录，将作为新数据添加")
                                existing_match = None
            
            # 如果仍然没有找到匹配的记录，标记为新数据
            if not existing_match:
                logger.info(f"[{trace_id}] 确认比赛数据不存在，新增: 比赛ID {item['match_id']} (轮次: {item['round']})")
                self.stats['match']['new'] += 1
                return item
            
            # 如果找到了匹配的记录，应用match_id替换逻辑并记录详细信息
            if existing_match:
                # 如果找到匹配的记录但match_id不同，更新item的match_id
                if existing_match[0] != original_match_id:
                    logger.warning(f"[{trace_id}] 找到匹配的比赛但match_id不同，将使用数据库ID: "
                                 f"爬取ID={original_match_id} -> 数据库ID={existing_match[0]}")
                    # 保存ID映射关系
                    self.match_id_mapping[original_match_id] = existing_match[0]
                    # 更新item的match_id
                    item['match_id'] = existing_match[0]
                    
                    # 记录更详细的信息
                    logger.warning(f"[{trace_id}] ID映射已保存至映射表，当前映射表大小: {len(self.match_id_mapping)}")
                
                # 记录当前找到的数据库记录，用于调试
                logger.warning(f"[{trace_id}] 找到数据库中的比赛记录: match_id={existing_match[0]}, league_id={existing_match[1]}, "
                             f"sub_league_id={existing_match[2]}, season={existing_match[3]}, round={existing_match[4]}, "
                             f"match_time={existing_match[5]}, home_team_id={existing_match[6]}, away_team_id={existing_match[7]}, "
                             f"full_score={existing_match[8]}, half_score={existing_match[9]}")
            
            # 存在，检查是否有字段需要更新
            need_update = False
            fields_to_check = ['full_score', 'half_score', 'match_time']
            update_details = []
            
            for field in fields_to_check:
                field_idx = {'full_score': 8, 'half_score': 9, 'match_time': 5}.get(field)
                
                # 针对match_time字段使用特殊的比较逻辑
                if field == 'match_time':
                    # 检查时间是否实质相同（忽略小的差异）
                    if field in item and not self.is_same_time(item[field], existing_match[field_idx]):
                        need_update = True
                        update_details.append(f"{field}: {existing_match[field_idx]} -> {item[field]}")
                        logger.info(f"[{trace_id}] 比赛ID {item['match_id']} 字段 {field} 需要更新: "
                                  f"{existing_match[field_idx]} -> {item[field]}")
                # 其他字段使用常规比较
                elif field in item and item[field] != existing_match[field_idx]:
                    need_update = True
                    update_details.append(f"{field}: {existing_match[field_idx]} -> {item[field]}")
                    logger.info(f"[{trace_id}] 比赛ID {item['match_id']} 字段 {field} 需要更新: "
                              f"{existing_match[field_idx]} -> {item[field]}")
            
            # 额外检查轮次、球队ID等是否匹配，确保是同一场比赛
            if item['round'] != existing_match[4]:
                logger.warning(f"[{trace_id}] 比赛ID {item['match_id']} 轮次不匹配: "
                            f"数据库={existing_match[4]}, 爬取={item['round']}")
            
            if item['home_team_id'] != existing_match[6] or item['away_team_id'] != existing_match[7]:
                logger.warning(f"[{trace_id}] 比赛ID {item['match_id']} 球队不匹配: "
                            f"数据库主队={existing_match[6]}, 客队={existing_match[7]}; "
                            f"爬取主队={item['home_team_id']}, 客队={item['away_team_id']}")
            
            if need_update:
                self.stats['match']['updated'] += 1
                logger.info(f"[{trace_id}] 比赛数据需要更新: 比赛ID {item['match_id']} (轮次: {item['round']}), 更新字段: {', '.join(update_details)}")
                return item
            else:
                # 所有字段都相同，不需要更新
                self.stats['match']['unchanged'] += 1
                logger.info(f"[{trace_id}] 比赛数据无变化，跳过: 比赛ID {item['match_id']} (轮次: {item['round']})")
                # 返回None，阻止后续Pipeline处理
                return None
                
        except Exception as e:
            logger.error(f"[{trace_id}] 处理比赛增量更新时出错: {str(e)}")
            import traceback
            logger.error(f"[{trace_id}] 错误详情: {traceback.format_exc()}")
            # 出错时继续处理，确保数据不丢失
            return item

    def _process_odds(self, item, spider):
        """处理赔率数据的增量更新 - 优化：跳过存在性检查，直接传递给下游Pipeline"""
        trace_id = str(uuid.uuid4())[:8]  # 为赔率处理添加跟踪ID
        try:
            # 仅进行必要的时间格式化
            try:
                if 'update_time' in item and isinstance(item['update_time'], str):
                    original_time = item['update_time']
                    # 情况1: Unix时间戳（纯数字字符串）
                    if original_time.isdigit():
                        timestamp = int(original_time)
                        dt_object = datetime.fromtimestamp(timestamp)
                        item['update_time'] = dt_object
                        logger.debug(f"[{trace_id}] Unix时间戳转换: {original_time} -> {item['update_time']}")
                    # 情况2: "MM-DD HH:MM"格式
                    elif re.match(r'^[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$', original_time):
                        current_year = datetime.now().year
                        try:
                            time_str = f"{current_year}-{original_time[:5]} {original_time[6:]}"
                            item['update_time'] = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                            logger.debug(f"[{trace_id}] 短日期格式转换: {original_time} -> {item['update_time']}")
                        except ValueError as e:
                            logger.warning(f"[{trace_id}] 短日期格式转换失败: {str(e)}")
                    # 情况3: 标准格式
                    elif re.match(r'^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}(:[0-9]{2})?$', original_time):
                        try:
                            if len(original_time) == 16:  # 没有秒
                                item['update_time'] = datetime.strptime(original_time, '%Y-%m-%d %H:%M')
                            else:
                                item['update_time'] = datetime.strptime(original_time, '%Y-%m-%d %H:%M:%S')
                            logger.debug(f"[{trace_id}] 标准格式转换: {original_time} -> {item['update_time']}")
                        except ValueError as e:
                            logger.warning(f"[{trace_id}] 标准格式转换失败: {str(e)}")
                    else:
                         # 尝试使用 dateutil.parser 作为最后的解析手段
                         try:
                             dt = dateutil.parser.parse(original_time)
                             item['update_time'] = dt.replace(tzinfo=None) # 移除时区信息
                             logger.debug(f"[{trace_id}] dateutil解析转换: {original_time} -> {item['update_time']}")
                         except Exception as parse_err:
                              logger.warning(f"[{trace_id}] 未知时间格式，保留原值: {original_time}, 解析错误: {parse_err}")
            except Exception as e:
                logger.warning(f"[{trace_id}] 标准化update_time出错: {str(e)}")
            
            # 不再执行数据库查询检查，直接返回item
            logger.info(f"[{trace_id}] 跳过赔率存在性检查，直接传递: 比赛ID={item['match_id']}, 博彩公司ID={item['bookmaker_id']}")
            # 更新统计（注意：现在所有赔率都会被视为'new'，因为检查被跳过）
            self.stats['odds']['new'] += 1 
            return item
            
        except Exception as e:
            logger.error(f"[{trace_id}] 处理赔率数据时出错（优化版）: {str(e)}")
            import traceback
            logger.error(f"[{trace_id}] 赔率处理错误详情: {traceback.format_exc()}")
            # 出错时仍然尝试返回item，让下游处理
            return item

    def is_same_time(self, time1, time2, max_diff_seconds=60):
        """
        比较两个时间是否相同，允许最大差异为max_diff_seconds秒
        """
        dt1 = self._parse_datetime(time1)
        dt2 = self._parse_datetime(time2)
        
        # 如果有一个无法解析，则认为不同
        if dt1 is None or dt2 is None:
            logger.warning(f"时间比较错误: 时间1 = {time1}({dt1}), 时间2 = {time2}({dt2})")
            return False
        
        try:
            diff = abs((dt1 - dt2).total_seconds())
            logger.debug(f"时间差异: {diff}秒, 时间1 = {dt1}, 时间2 = {dt2}")
            return diff <= max_diff_seconds
        except Exception as e:
            logger.error(f"比较时间时出错: {e}, 时间1 = {time1}({dt1}), 时间2 = {time2}({dt2})")
            return False

    def _parse_datetime(self, time_value):
        """
        将各种可能的时间格式解析为datetime对象
        :param time_value: 时间值（字符串或datetime）
        :return: datetime对象或None（如果解析失败）
        """
        if time_value is None:
            return None
            
        # 如果已经是datetime对象，处理时区
        if isinstance(time_value, datetime):
            # 如果有时区信息，转换为UTC时间
            if time_value.tzinfo is not None:
                return time_value.replace(tzinfo=None)
            return time_value
            
        # 如果是字符串，尝试解析
        if isinstance(time_value, str):
            # 尝试多种格式
            formats = [
                '%Y-%m-%d %H:%M:%S',     # 2025-03-22 16:41:13
                '%Y-%m-%d %H:%M',        # 2025-03-22 16:41
                '%Y-%m-%d',              # 2025-03-22
            ]
            
            # 先尝试标准格式
            for fmt in formats:
                try:
                    return datetime.strptime(time_value, fmt)
                except ValueError:
                    continue
            
            # 处理带时区的格式
            try:
                # 尝试ISO格式（包含时区）
                dt = dateutil.parser.parse(time_value)
                # 移除时区信息，以便与其他时间比较
                return dt.replace(tzinfo=None)
            except:
                pass
                
            # 处理短日期格式（MM-DD HH:MM）
            if re.match(r'^[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$', time_value):
                try:
                    # 添加当前年份
                    current_year = datetime.now().year
                    time_with_year = f"{current_year}-{time_value[:5]} {time_value[6:]}"
                    return datetime.strptime(time_with_year, '%Y-%m-%d %H:%M')
                except:
                    pass
                    
            # 如果是纯数字，尝试作为Unix时间戳处理
            if re.match(r'^\d+$', time_value):
                try:
                    # 使用UTC时间，避免时区问题
                    from datetime import timezone
                    dt = datetime.fromtimestamp(int(time_value), tz=timezone.utc)
                    # 移除时区信息
                    return dt.replace(tzinfo=None)
                except:
                    pass
            
            # 记录无法解析的时间
            logger.warning(f"无法解析的时间格式: {time_value}")
            return None
        
        # 其他类型，尝试转为字符串后解析
        return self._parse_datetime(str(time_value))

    def _process_continent(self, item, spider):
        """处理大洲数据的增量更新"""
        try:
            # 检查数据库中是否存在该大洲记录
            query = """
                SELECT continent_id, continent_name, continent_name_en
                FROM continents
                WHERE continent_id = %s OR continent_name = %s
            """
            # Scrapy Item中字段可能不存在，使用.get获取
            continent_id_item = item.get('continent_id')
            continent_name_item = item.get('continent_name')

            # 将 continent_id 转换为字符串以便查询
            continent_id_query_param = str(continent_id_item) if continent_id_item is not None else None

            if continent_id_query_param is not None:
                self.cur.execute(query, (continent_id_query_param, continent_name_item))
            elif continent_name_item is not None: # 如果continent_id不存在，确保continent_name存在才执行
                self.cur.execute(query, (None, continent_name_item))
            else: # 如果两者都不存在，无法查询
                logger.warning(f"大洲数据缺少 continent_id 和 continent_name，无法处理: {item}")
                return item # 或者根据业务逻辑返回None

            existing_continent = self.cur.fetchone()

            if not existing_continent:
                # 不存在，标记为新数据，继续处理
                logger.info(f"大洲数据不存在，新增: {item.get('continent_name')} (ID: {item.get('continent_id')})")
                self.stats['continent']['new'] += 1
                return item

            # 存在，检查是否有字段需要更新
            need_update = False
            fields_to_check = ['continent_name', 'continent_name_en']
            update_details = []

            for field in fields_to_check:
                # 从 existing_continent (元组或字典) 中获取值
                if isinstance(existing_continent, dict):
                    existing_value = existing_continent.get(field)
                else: # 假定是元组，需要知道字段顺序
                    if field == 'continent_name':
                        existing_value = existing_continent[1]
                    elif field == 'continent_name_en':
                        existing_value = existing_continent[2]
                    else:
                        existing_value = None

                if item.get(field) is not None and item.get(field) != existing_value:
                    need_update = True
                    update_details.append(f"{field}: {existing_value} -> {item.get(field)}")
                    logger.info(f"大洲 {item.get('continent_name')} (ID: {item.get('continent_id')}) 字段 {field} 需要更新: {existing_value} -> {item.get(field)}")

            if need_update:
                self.stats['continent']['updated'] += 1
                logger.info(f"大洲数据需要更新: {item.get('continent_name')} (ID: {item.get('continent_id')}), 更新字段: {', '.join(update_details)}")
                return item
            else:
                self.stats['continent']['unchanged'] += 1
                logger.info(f"大洲数据无变化，跳过: {item.get('continent_name')} (ID: {item.get('continent_id')})")
                return None

        except Exception as e:
            logger.error(f"处理大洲增量更新时出错: {str(e)}")
            return item

    def _process_country(self, item, spider):
        """处理国家数据的增量更新"""
        try:
            query = """
                SELECT country_id, country_name, country_logo, continent_id
                FROM countries
                WHERE country_id = %s OR country_name = %s
            """
            country_id_item = item.get('country_id')
            country_name_item = item.get('country_name')

            # 将 country_id 转换为字符串以便查询
            country_id_query_param = str(country_id_item) if country_id_item is not None else None

            if country_id_query_param is not None:
                self.cur.execute(query, (country_id_query_param, country_name_item))
            elif country_name_item is not None:
                self.cur.execute(query, (None, country_name_item))
            else:
                logger.warning(f"国家数据缺少 country_id 和 country_name，无法处理: {item}")
                return item

            existing_country = self.cur.fetchone()

            if not existing_country:
                logger.info(f"国家数据不存在，新增: {item.get('country_name')} (ID: {item.get('country_id')})")
                self.stats['country']['new'] += 1
                return item

            need_update = False
            fields_to_check = ['country_name', 'country_logo', 'continent_id']
            update_details = []

            for field_idx, field in enumerate(fields_to_check):
                existing_value = existing_country[field_idx+1] if not isinstance(existing_country, dict) else existing_country.get(field)
                item_value = item.get(field)
                if item_value is not None and item_value != existing_value:
                    need_update = True
                    update_details.append(f"{field}: {existing_value} -> {item_value}")
                    logger.info(f"国家 {item.get('country_name')} (ID: {item.get('country_id')}) 字段 {field} 需要更新: {existing_value} -> {item_value}")
            
            # 特别处理 continent_id，因为数据库中可能是 int 而 item 中可能是 str
            existing_continent_id = existing_country[3] if not isinstance(existing_country, dict) else existing_country.get('continent_id')
            item_continent_id = item.get('continent_id')
            if item_continent_id is not None:
                try:
                    if int(item_continent_id) != existing_continent_id:
                        if not any(f.startswith('continent_id:') for f in update_details): # 避免重复添加
                            need_update = True
                            update_details.append(f"continent_id: {existing_continent_id} -> {item_continent_id}")
                            logger.info(f"国家 {item.get('country_name')} (ID: {item.get('country_id')}) 字段 continent_id 需要更新: {existing_continent_id} -> {item_continent_id}")
                except ValueError:
                    logger.warning(f"国家 {item.get('country_name')} 的 continent_id ('{item_continent_id}') 无法转换为整数进行比较。")


            if need_update:
                self.stats['country']['updated'] += 1
                logger.info(f"国家数据需要更新: {item.get('country_name')} (ID: {item.get('country_id')}), 更新字段: {', '.join(update_details)}")
                return item
            else:
                self.stats['country']['unchanged'] += 1
                logger.info(f"国家数据无变化，跳过: {item.get('country_name')} (ID: {item.get('country_id')})")
                return None

        except Exception as e:
            logger.error(f"处理国家增量更新时出错: {str(e)}")
            return item

    def _process_cup(self, item, spider):
        """处理杯赛数据的增量更新"""
        try:
            query = """
                SELECT cup_id, cup_short_name, cup_official_name_simp, cup_official_name_cant, 
                       cup_official_name_en, cup_logo, match_type, current_season, 
                       available_seasons, continent_id, country_id
                FROM cups
                WHERE cup_id = %s OR cup_official_name_simp = %s 
            """ # 假设 cup_id 或 cup_official_name_simp 可以唯一确定一个杯赛
            
            cup_id_item = item.get('cup_id')
            cup_name_simp_item = item.get('cup_official_name_simp')

            # 将 cup_id 转换为字符串以便查询
            cup_id_query_param = str(cup_id_item) if cup_id_item is not None else None

            if cup_id_query_param is not None:
                self.cur.execute(query, (cup_id_query_param, cup_name_simp_item))
            elif cup_name_simp_item is not None:
                 self.cur.execute(query, (None, cup_name_simp_item))
            else:
                logger.warning(f"杯赛数据缺少 cup_id 和 cup_official_name_simp，无法处理: {item}")
                return item

            existing_cup = self.cur.fetchone()

            if not existing_cup:
                logger.info(f"杯赛数据不存在，新增: {item.get('cup_official_name_simp')} (ID: {item.get('cup_id')})")
                self.stats['cup']['new'] += 1
                return item

            need_update = False
            # 字段顺序必须与查询中的SELECT顺序一致 (不包括cup_id)
            fields_to_check = [
                'cup_short_name', 'cup_official_name_simp', 'cup_official_name_cant',
                'cup_official_name_en', 'cup_logo', 'match_type', 'current_season',
                'available_seasons', 'continent_id', 'country_id'
            ]
            update_details = []

            for field_idx, field in enumerate(fields_to_check):
                # existing_cup[0] is cup_id, so data fields start from index 1
                existing_value = existing_cup[field_idx+1] if not isinstance(existing_cup, dict) else existing_cup.get(field)
                item_value = item.get(field)

                if item_value is not None and item_value != existing_value:
                    # 特殊处理 available_seasons，它可能是列表或逗号分隔的字符串
                    if field == 'available_seasons':
                        # 规范化比较：都转换成排序后的列表
                        item_seasons = sorted([str(s).strip() for s in (item_value.split(',') if isinstance(item_value, str) else item_value or [])])
                        existing_seasons_val = existing_value or ""
                        existing_seasons = sorted([str(s).strip() for s in (existing_seasons_val.split(',') if isinstance(existing_seasons_val, str) else existing_seasons_val or [])])
                        if item_seasons != existing_seasons:
                            need_update = True
                            update_details.append(f"{field}: {existing_seasons} -> {item_seasons}")
                            logger.info(f"杯赛 {item.get('cup_official_name_simp')} (ID: {item.get('cup_id')}) 字段 {field} 需要更新.")
                    elif field in ['continent_id', 'country_id']:
                         # 处理ID字段可能存在的类型不匹配（str vs int）
                        try:
                            if item_value is not None and existing_value is not None and int(item_value) != int(existing_value):
                                need_update = True
                                update_details.append(f"{field}: {existing_value} -> {item_value}")
                                logger.info(f"杯赛 {item.get('cup_official_name_simp')} (ID: {item.get('cup_id')}) 字段 {field} 需要更新: {existing_value} -> {item_value}")
                        except ValueError:
                             if str(item_value) != str(existing_value): # 如果不能转int，则按字符串比较
                                need_update = True
                                update_details.append(f"{field}: {existing_value} -> {item_value}")
                                logger.info(f"杯赛 {item.get('cup_official_name_simp')} (ID: {item.get('cup_id')}) 字段 {field} (作为字符串比较) 需要更新: {existing_value} -> {item_value}")
                    else:
                        need_update = True
                        update_details.append(f"{field}: {existing_value} -> {item_value}")
                        logger.info(f"杯赛 {item.get('cup_official_name_simp')} (ID: {item.get('cup_id')}) 字段 {field} 需要更新: {existing_value} -> {item_value}")


            if need_update:
                self.stats['cup']['updated'] += 1
                logger.info(f"杯赛数据需要更新: {item.get('cup_official_name_simp')} (ID: {item.get('cup_id')}), 更新字段: {', '.join(update_details)}")
                return item
            else:
                self.stats['cup']['unchanged'] += 1
                logger.info(f"杯赛数据无变化，跳过: {item.get('cup_official_name_simp')} (ID: {item.get('cup_id')})")
                return None

        except Exception as e:
            logger.error(f"处理杯赛增量更新时出错: {str(e)}")
            return item 