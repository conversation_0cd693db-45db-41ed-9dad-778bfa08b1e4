import scrapy

class ContinentItem(scrapy.Item):
    """大洲信息"""
    continent_id = scrapy.Field()  # 大洲ID
    continent_name = scrapy.Field()  # 大洲名称
    continent_name_en = scrapy.Field()  # 大洲英文名称
    created_at = scrapy.Field()  # 创建时间
    updated_at = scrapy.Field()  # 更新时间

class CountryItem(scrapy.Item):
    """国家信息"""
    country_id = scrapy.Field()  # 国家ID
    country_name = scrapy.Field()  # 国家名称
    country_logo = scrapy.Field()  # 国家logo 本地URL路径
    continent_id = scrapy.Field()  # 所属大洲ID
    image_urls = scrapy.Field()    # Logo的完整URL列表，供ImagesPipeline使用
    created_at = scrapy.Field()  # 创建时间
    updated_at = scrapy.Field()  # 更新时间

class LeagueItem(scrapy.Item):
    """联赛数据模型"""
    league_id = scrapy.Field()  # 联赛ID
    sub_league_id = scrapy.Field()  # 子联赛ID
    league_type = scrapy.Field()  # 联赛类型
    league_short_name = scrapy.Field()  # 联赛简称
    league_official_name_simp = scrapy.Field()  # 联赛官方名称（简体）
    league_official_name_cant = scrapy.Field()  # 联赛官方名称（繁体）
    league_official_name_en = scrapy.Field()  # 联赛官方名称（英文）
    league_logo = scrapy.Field()  # 联赛logo
    league_logo_path = scrapy.Field()  # 联赛logo原始路径 (用于下载或记录)
    available_seasons = scrapy.Field()  # 可用赛季
    current_season = scrapy.Field()  # 当前赛季
    total_rounds = scrapy.Field()  # 总轮次
    current_round = scrapy.Field()  # 当前轮次
    continent_id = scrapy.Field()  # 大洲ID
    country_id = scrapy.Field()  # 国家ID
    task_id = scrapy.Field() # 任务ID，用于关联爬虫任务
    created_at = scrapy.Field()  # 创建时间
    updated_at = scrapy.Field()  # 更新时间

class CupItem(scrapy.Item):
    """杯赛数据模型"""
    cup_id = scrapy.Field()  # 杯赛ID (来自源数据的ID，例如 '283')
    cup_short_name = scrapy.Field()  # 杯赛简称
    cup_official_name_simp = scrapy.Field()  # 杯赛官方名称（简体）
    cup_official_name_cant = scrapy.Field()  # 杯赛官方名称（繁体）
    cup_official_name_en = scrapy.Field()  # 杯赛官方名称（英文）
    cup_logo = scrapy.Field()  # 杯赛logo (本地相对路径)
    match_type = scrapy.Field()  # 赛事类型 (例如 '2,0')
    current_season = scrapy.Field()  # 当前赛季
    available_seasons = scrapy.Field() # 可用赛季列表，逗号分隔字符串
    continent_id = scrapy.Field()  # 大洲ID
    country_id = scrapy.Field()  # 国家ID
    # 杯赛可能还有其他特有字段，例如：
    # current_stage = scrapy.Field() # 当前阶段 (例如 '小组赛', '淘汰赛', '决赛')
    # total_stages = scrapy.Field() # 总阶段数
    created_at = scrapy.Field()  # 创建时间
    updated_at = scrapy.Field()  # 更新时间

class TeamItem(scrapy.Item):
    """球队数据模型 (独立于联赛/赛季)"""
    team_id = scrapy.Field()  # 球队ID (源数据中的唯一ID, 例如 '530')
    team_name_simp = scrapy.Field()  # 球队名称（简体）
    team_name_cant = scrapy.Field()  # 球队名称（繁体）
    team_name_en = scrapy.Field()  # 球队名称（英文）
    team_logo = scrapy.Field()  # 球队logo (本地相对路径)
    country_id = scrapy.Field() # 新增：国家ID，关联到 countries 表
    # 新增字段以匹配 parse 方法中的赋值
    league_id = scrapy.Field()      # 球队所属的主联赛ID (用于关联或信息记录)
    sub_league_id = scrapy.Field()  # 球队所属的子联赛ID (可选)
    season = scrapy.Field()         # 该球队信息所属的赛季 (例如 '2024-2025')
    created_at = scrapy.Field()  # 创建时间
    updated_at = scrapy.Field()  # 更新时间

class MatchItem(scrapy.Item):
    """比赛数据模型"""
    # 唯一标识符
    match_id = scrapy.Field()  # 源数据中的比赛ID (例如 3948861)

    # 关联外键 (指向 leagues.id 或 cups.id)
    league_ref_id = scrapy.Field() # 关联的联赛在 leagues 表中的ID
    cup_ref_id = scrapy.Field()    # 关联的杯赛在 cups 表中的ID（原cup_id）

    # 比赛基本信息
    sub_league_id_source = scrapy.Field() # 源数据中的子联赛ID (例如 'NBL')
    season = scrapy.Field()  # 赛季 (例如 '2023-2024' 或 '2024')
    round = scrapy.Field()  # 轮次/阶段 (例如 '常规赛第10轮', '半决赛')
    match_time = scrapy.Field()  # 比赛时间 (UTC)

    # 新增：指向 teams 表的数据库外键ID
    home_team_id = scrapy.Field() # 主队在 teams 表中的数据库ID
    away_team_id = scrapy.Field() # 客队在 teams 表中的数据库ID

    home_team_id_source = scrapy.Field()  # 主队ID (源数据中的球队ID)
    away_team_id_source = scrapy.Field()  # 客队ID (源数据中的球队ID)
    home_team_name_source = scrapy.Field() # 主队名称 (源数据中的名称，用于日志或显示)
    away_team_name_source = scrapy.Field() # 客队名称 (源数据中的名称)
    full_score = scrapy.Field()  # 全场比分 (例如 '2-1')
    half_score = scrapy.Field()  # 半场比分 (例如 '1-0')
    
    # 爬虫状态字段
    scraped_1d = scrapy.Field()  # 是否已触发提前1天爬取
    scraped_6h = scrapy.Field()  # 是否已触发提前6小时爬取
    scraped_2h = scrapy.Field()  # 是否已触发提前2小时爬取
    scraped_15m = scrapy.Field()  # 是否已触发提前15分钟爬取
    
    created_at = scrapy.Field()  # 创建时间
    updated_at = scrapy.Field()  # 更新时间

class OddsItem(scrapy.Item):
    """赔率数据模型"""
    match_id = scrapy.Field()  # 比赛ID
    odds_detail_id = scrapy.Field()  # 赔率详情ID
    bookmaker_id = scrapy.Field()  # 博彩公司ID
    home_win = scrapy.Field()  # 主胜赔率
    draw = scrapy.Field()  # 平局赔率
    away_win = scrapy.Field()  # 客胜赔率
    update_time = scrapy.Field()  # 更新时间
    created_at = scrapy.Field()  # 创建时间
    updated_at = scrapy.Field()  # 更新时间 