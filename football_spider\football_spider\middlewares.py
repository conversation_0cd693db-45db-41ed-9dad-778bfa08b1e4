import logging
import random
from scrapy import signals
from scrapy.downloadermiddlewares.retry import RetryMiddleware
from scrapy.utils.response import response_status_message
from .proxy_rotator import get_rotator

logger = logging.getLogger(__name__)

class ProxyMiddleware:
    """IP代理中间件，实现代理IP的自动轮换"""
    
    def __init__(self, settings):
        self.rotator = get_rotator()
        # 从配置中获取代理列表
        proxy_list = settings.get('PROXY_LIST', [])
        if proxy_list:
            self.rotator.add_proxies(proxy_list)
            
        # 是否启用代理
        self.enabled = settings.getbool('PROXY_ENABLED', False)
        if self.enabled:
            logger.info(f"启用代理中间件，当前代理数量: {len(self.rotator.proxy_list)}")
        else:
            logger.info("代理中间件已禁用")
            
    @classmethod
    def from_crawler(cls, crawler):
        middleware = cls(crawler.settings)
        crawler.signals.connect(middleware.spider_opened, signal=signals.spider_opened)
        return middleware
        
    def spider_opened(self, spider):
        """当爬虫启动时执行"""
        # 检查爬虫是否有自己的代理列表
        if hasattr(spider, 'proxy_list') and spider.proxy_list:
            logger.info(f"从爬虫 {spider.name} 中添加 {len(spider.proxy_list)} 个代理")
            self.rotator.add_proxies(spider.proxy_list)
            
    def process_request(self, request, spider):
        """处理请求，为请求添加代理"""
        # 如果请求已经有代理或代理功能被禁用，或爬虫本身禁用了代理，则跳过
        if 'proxy' in request.meta or not self.enabled or getattr(spider, 'proxy_enabled', True) is False:
            return None
            
        # 获取一个代理
        proxy = self.rotator.get_proxy()
        if proxy:
            logger.debug(f"为请求 {request.url} 分配代理: {proxy}")
            request.meta['proxy'] = proxy
            # 记录原始代理，用于后续报告成功或失败
            request.meta['original_proxy'] = proxy
        return None
        
    def process_response(self, request, response, spider):
        """处理响应，检查代理是否有效"""
        proxy = request.meta.get('original_proxy')
        
        # 如果请求使用了代理，检查响应状态
        if proxy:
            # 2xx 状态码视为成功
            if 200 <= response.status < 300:
                self.rotator.report_success(proxy)
            else:
                # 4xx 和 5xx 状态码视为失败
                if response.status >= 400:
                    logger.warning(f"代理 {proxy} 请求失败，状态码: {response.status}")
                    self.rotator.report_failure(proxy)
                    
        return response
        
    def process_exception(self, request, exception, spider):
        """处理异常，报告代理失败"""
        proxy = request.meta.get('original_proxy')
        if proxy:
            logger.warning(f"代理 {proxy} 请求异常: {type(exception).__name__}")
            self.rotator.report_failure(proxy)
        return None
        
class CustomRetryMiddleware(RetryMiddleware):
    """自定义重试中间件，结合代理轮换功能"""
    
    def __init__(self, settings):
        super().__init__(settings)
        self.rotator = get_rotator()
        self.max_retry_times = settings.getint('RETRY_TIMES', 2)
        self.retry_http_codes = set(int(x) for x in settings.getlist('RETRY_HTTP_CODES', []))
        
    def process_response(self, request, response, spider):
        """处理响应，检查是否需要重试"""
        if request.meta.get('dont_retry', False):
            return response
            
        if response.status in self.retry_http_codes:
            # 当状态码需要重试时，检查当前使用的代理
            current_proxy = request.meta.get('proxy')
            if current_proxy:
                # 报告当前代理失败
                self.rotator.report_failure(current_proxy)
                
            # 获取新代理
            new_proxy = self.rotator.get_proxy()
            if new_proxy:
                request.meta['proxy'] = new_proxy
                request.meta['original_proxy'] = new_proxy
                logger.info(f"为重试请求 {request.url} 分配新代理: {new_proxy}")
                
            reason = response_status_message(response.status)
            return self._retry(request, reason, spider) or response
            
        return response
        
    def process_exception(self, request, exception, spider):
        """处理异常，检查是否需要重试"""
        if request.meta.get('dont_retry', False):
            return None
            
        # 检查当前使用的代理
        current_proxy = request.meta.get('proxy')
        if current_proxy:
            # 报告当前代理失败
            self.rotator.report_failure(current_proxy)
            
        # 获取新代理
        new_proxy = self.rotator.get_proxy()
        if new_proxy:
            request.meta['proxy'] = new_proxy
            request.meta['original_proxy'] = new_proxy
            logger.info(f"为重试请求 {request.url} 分配新代理: {new_proxy}")
            
        # 调用父类方法处理重试
        return super().process_exception(request, exception, spider)
        
class RandomUserAgentMiddleware:
    """随机User-Agent中间件，每次请求随机使用不同的User-Agent"""
    
    def __init__(self, settings):
        self.user_agents = settings.getlist('USER_AGENT_LIST', [
            # 桌面端浏览器
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            
            # 移动端浏览器
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 11; SM-G998U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
        ])
        
    @classmethod
    def from_crawler(cls, crawler):
        return cls(crawler.settings)
        
    def process_request(self, request, spider):
        """为每个请求随机选择一个User-Agent"""
        # 检查爬虫是否有自己的User-Agent
        if hasattr(spider, 'user_agent'):
            request.headers['User-Agent'] = spider.user_agent
        else:
            request.headers['User-Agent'] = random.choice(self.user_agents)
        return None 