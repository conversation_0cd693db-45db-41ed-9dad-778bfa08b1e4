import os
from dotenv import load_dotenv
from itemadapter import ItemAdapter
from .items import LeagueItem, TeamItem, MatchItem, OddsItem, ContinentItem, CountryItem, CupItem
import logging
import psycopg2
from psycopg2.extras import DictCursor, execute_values
from .db_pool import get_db_pool, init_db_pool, DbPool
# from psycopg2 import sql # Import sql module for safe SQL composition
import psycopg2.sql as sql_builder
import requests # <-- Add requests import
import traceback # 导入 traceback 用于打印详细错误
from scrapy.exceptions import DropItem
from scrapy.pipelines.images import ImagesPipeline
from scrapy import Request
import pytz
from datetime import datetime

load_dotenv()

# Use Scrapy's logger for pipeline messages
logger = logging.getLogger(__name__)
# Django backend notification API URL
NOTIFY_URL = 'http://localhost:8000/api/notify-match-update/' 

# --- 新增或确认辅助函数 ---
def _send_notification(match_id, update_type='odds_updated'):
    """发送 HTTP POST 请求到 Django 后端以触发 SSE 通知。"""
    payload = {'match_id': str(match_id), 'update_type': update_type}
    try:
        response = requests.post(NOTIFY_URL, json=payload, timeout=5) # 添加超时
        response.raise_for_status() # 检查 HTTP 错误 (4xx or 5xx)
        logger.info(f"成功发送比赛更新通知到后端: match_id={match_id}, type={update_type}, status={response.status_code}")
    except requests.exceptions.RequestException as e:
        logger.error(f"发送比赛更新通知失败: match_id={match_id}, type={update_type}, URL={NOTIFY_URL}, Error: {e}")
        # 可以选择打印更详细的 traceback
        # logger.error(traceback.format_exc())
    except Exception as e:
        logger.error(f"发送通知时发生意外错误: match_id={match_id}, Error: {e}")
        # logger.error(traceback.format_exc())

class BasePipeline:
    """基础Pipeline类，提供基本的数据库操作功能"""
    
    def __init__(self, settings):
        self.settings = settings
        self.db_pool = DbPool(settings)  # 使用数据库连接池，并传递settings
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @classmethod
    def from_crawler(cls, crawler):
        # Scrapy调用此方法来创建pipeline实例
        return cls(settings=crawler.settings)
        
    def process_item(self, item, spider):
        return item
        
    def close_spider(self, spider):
        """关闭爬虫时关闭数据库连接"""
        self.db_pool.close_all()

class ContinentPipeline(BasePipeline):
    """处理大洲数据的pipeline"""
    
    def process_item(self, item, spider):
        if item is None:
            logger.debug(f"ContinentPipeline: Received None item, skipping.")
            return None
        if spider.name != 'continent':
            return item
            
        conn = self.db_pool.getconn()    
        try:
            with conn.cursor() as cur:
                # 检查表是否存在，如果不存在则创建
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS continents (
                        id SERIAL PRIMARY KEY,
                        continent_id INTEGER UNIQUE NOT NULL,
                        continent_name VARCHAR(100) NOT NULL,
                        continent_name_en VARCHAR(100),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                    
                # 使用UPSERT操作插入或更新大洲数据
                query = sql_builder.SQL("""
                    INSERT INTO continents (
                        continent_id, continent_name, continent_name_en, 
                        created_at, updated_at
                    ) VALUES (
                        %(continent_id)s, %(continent_name)s, %(continent_name_en)s,
                        %(created_at)s, %(updated_at)s
                    )
                    ON CONFLICT (continent_id) DO UPDATE SET
                        continent_name = EXCLUDED.continent_name,
                        continent_name_en = EXCLUDED.continent_name_en,
                        updated_at = EXCLUDED.updated_at
                """)
                
                # 执行SQL
                cur.execute(query, dict(item))
                conn.commit()
                
                self.logger.info(f"大洲信息已保存: continent_id={item['continent_id']}, name={item['continent_name']}")
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"保存大洲信息出错: {str(e)}")
        finally:
            if conn:
                self.db_pool.putconn(conn)
        return item

class CountryPipeline(BasePipeline):
    """处理国家数据的pipeline"""
    
    def process_item(self, item, spider):
        if item is None:
            logger.debug(f"CountryPipeline: Received None item, skipping.")
            return None
        if spider.name != 'country':
            return item
        
        conn = self.db_pool.getconn()
        try:
            with conn.cursor() as cur:
                # 检查表是否存在，如果不存在则创建
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS countries (
                        id SERIAL PRIMARY KEY,
                        country_id INTEGER UNIQUE NOT NULL,
                        country_name VARCHAR(100) NOT NULL,
                        country_logo VARCHAR(200),
                        continent_id INTEGER REFERENCES continents(continent_id),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                
                # 使用UPSERT操作插入或更新国家数据
                query = sql_builder.SQL("""
                    INSERT INTO countries (
                        country_id, country_name, country_logo, continent_id,
                        created_at, updated_at
                    ) VALUES (
                        %(country_id)s, %(country_name)s, %(country_logo)s, %(continent_id)s,
                        %(created_at)s, %(updated_at)s
                    )
                    ON CONFLICT (country_id) DO UPDATE SET
                        country_name = EXCLUDED.country_name,
                        country_logo = EXCLUDED.country_logo,
                        continent_id = EXCLUDED.continent_id,
                        updated_at = EXCLUDED.updated_at
                """)
                
                # 执行SQL
                cur.execute(query, dict(item))
                conn.commit()
                
                self.logger.info(f"国家信息已保存: country_id={item['country_id']}, name={item['country_name']}")
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"保存国家信息出错: {str(e)}")
        finally:
            if conn:
                self.db_pool.putconn(conn)
        return item

class CupPipeline(BasePipeline):
    """处理杯赛数据的pipeline"""
    
    def process_item(self, item, spider):
        if item is None:
            logger.debug(f"CupPipeline: Received None item, skipping.")
            return None
        if not isinstance(item, CupItem): # Check if the item is a CupItem
            return item
        if spider.name != 'cup': # Ensure this pipeline only processes items from cup_spider
            return item
            
        adapter = ItemAdapter(item)
        conn = self.db_pool.getconn()
        try:
            with conn.cursor() as cur:
                # The cups table structure should match your models.py and schema.sql
                # CREATE TABLE IF NOT EXISTS is good for robustness
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS cups (
                        id SERIAL PRIMARY KEY,
                        cup_id VARCHAR(50) NOT NULL,
                        cup_short_name VARCHAR(100) NOT NULL,
                        cup_official_name_simp VARCHAR(200),
                        cup_official_name_cant VARCHAR(200),
                        cup_official_name_en VARCHAR(200),
                        cup_logo VARCHAR(255), 
                        match_type VARCHAR(50) NOT NULL,
                        current_season VARCHAR(50) NOT NULL,
                        available_seasons TEXT,
                        continent_id INTEGER REFERENCES continents(continent_id) ON DELETE SET NULL,
                        country_id VARCHAR(50) REFERENCES countries(country_id) ON DELETE SET NULL,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(cup_id, current_season)
                    )
                """)
                conn.commit() # Commit CREATE TABLE IF NOT EXISTS separately
                
                # UPSERT operation for cup data
                query = sql_builder.SQL("""
                    INSERT INTO cups (
                        cup_id, cup_short_name, cup_official_name_simp, cup_official_name_cant,
                        cup_official_name_en, cup_logo, match_type, current_season, 
                        available_seasons, continent_id, country_id, created_at, updated_at
                    ) VALUES (
                        %(cup_id)s, %(cup_short_name)s, %(cup_official_name_simp)s, %(cup_official_name_cant)s,
                        %(cup_official_name_en)s, %(cup_logo)s, %(match_type)s, %(current_season)s,
                        %(available_seasons)s, %(continent_id)s, %(country_id)s, 
                        %(created_at)s, %(updated_at)s
                    )
                    ON CONFLICT (cup_id, current_season) DO UPDATE SET
                        cup_short_name = EXCLUDED.cup_short_name,
                        cup_official_name_simp = EXCLUDED.cup_official_name_simp,
                        cup_official_name_cant = EXCLUDED.cup_official_name_cant,
                        cup_official_name_en = EXCLUDED.cup_official_name_en,
                        cup_logo = EXCLUDED.cup_logo,
                        match_type = EXCLUDED.match_type,
                        available_seasons = EXCLUDED.available_seasons,
                        continent_id = EXCLUDED.continent_id,
                        country_id = EXCLUDED.country_id,
                        updated_at = EXCLUDED.updated_at
                """)
                
                # Prepare data dict from item
                data = {
                    'cup_id': adapter.get('cup_id'),
                    'cup_short_name': adapter.get('cup_short_name'),
                    'cup_official_name_simp': adapter.get('cup_official_name_simp'),
                    'cup_official_name_cant': adapter.get('cup_official_name_cant'),
                    'cup_official_name_en': adapter.get('cup_official_name_en'),
                    'cup_logo': adapter.get('cup_logo'),
                    'match_type': adapter.get('match_type'),
                    'current_season': adapter.get('current_season'),
                    'available_seasons': ','.join(adapter.get('available_seasons')) if adapter.get('available_seasons') else None,
                    'continent_id': adapter.get('continent_id'),
                    'country_id': adapter.get('country_id'),
                    'created_at': adapter.get('created_at', datetime.now(pytz.utc)), # Default to now if not present
                    'updated_at': adapter.get('updated_at', datetime.now(pytz.utc))  # Default to now if not present
                }

                cur.execute(query, data)
                conn.commit()
                
                self.logger.info(f"杯赛信息已保存: cup_id={adapter.get('cup_id')}, season={adapter.get('current_season')}")
        except psycopg2.Error as db_err:
            conn.rollback() # Rollback on database error
            self.logger.error(f"保存杯赛信息时发生数据库错误: {db_err} for item: {adapter.asdict()}")
            # Optionally re-raise or drop item
            # raise DropItem(f"Database error processing cup: {adapter.get('cup_id')}")
        except Exception as e:
            if conn:
                conn.rollback() # Rollback on other errors too
            self.logger.error(f"保存杯赛信息时发生未知错误: {str(e)} for item: {adapter.asdict()}")
            # raise DropItem(f"Unknown error processing cup: {adapter.get('cup_id')}")
        finally:
            if conn:
                self.db_pool.putconn(conn)
        return item

class LeaguePipeline(BasePipeline):
    """处理联赛数据的pipeline"""
    
    def process_item(self, item, spider):
        if item is None:
            logger.debug(f"LeaguePipeline: Received None item, skipping.")
            return None
        if not isinstance(item, LeagueItem): # Check if the item is a LeagueItem
            return item
        if spider.name != 'league':
            return item
        
        conn = self.db_pool.getconn()
        try:
            with conn.cursor() as cur:
                # 检查表是否存在，如果不存在则创建
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS leagues (
                        id SERIAL PRIMARY KEY,
                        league_id VARCHAR(50) NOT NULL,
                        sub_league_id VARCHAR(50),
                        league_type VARCHAR(50) NOT NULL,
                        league_short_name VARCHAR(100) NOT NULL,
                        league_official_name_simp VARCHAR(200),
                        league_official_name_cant VARCHAR(200),
                        league_official_name_en VARCHAR(200),
                        league_logo VARCHAR(200),
                        available_seasons TEXT,
                        current_season VARCHAR(50) NOT NULL,
                        total_rounds INTEGER,
                        current_round INTEGER,
                        continent_id INTEGER REFERENCES continents(continent_id),
                        country_id VARCHAR(50) REFERENCES countries(country_id),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(league_id, current_season, sub_league_id)
                    )
                """)
                conn.commit()
                
                # 使用UPSERT操作插入或更新联赛数据
                query = sql_builder.SQL("""
                    INSERT INTO leagues (
                        league_id, sub_league_id, league_short_name,
                        league_official_name_simp, league_official_name_cant, league_official_name_en,
                        league_type, league_logo, available_seasons, current_season,
                        total_rounds, current_round, continent_id, country_id,
                        created_at, updated_at
                    ) VALUES (
                        %(league_id)s, %(sub_league_id)s, %(league_short_name)s,
                        %(league_official_name_simp)s, %(league_official_name_cant)s, %(league_official_name_en)s,
                        %(league_type)s, %(league_logo)s, %(available_seasons)s, %(current_season)s,
                        %(total_rounds)s, %(current_round)s, %(continent_id)s, %(country_id)s,
                        %(created_at)s, %(updated_at)s
                    )
                    ON CONFLICT (league_id, current_season, sub_league_id) DO UPDATE SET
                        league_short_name = EXCLUDED.league_short_name,
                        league_official_name_simp = EXCLUDED.league_official_name_simp,
                        league_official_name_cant = EXCLUDED.league_official_name_cant,
                        league_official_name_en = EXCLUDED.league_official_name_en,
                        league_type = EXCLUDED.league_type,
                        league_logo = EXCLUDED.league_logo,
                        available_seasons = EXCLUDED.available_seasons,
                        total_rounds = EXCLUDED.total_rounds,
                        current_round = EXCLUDED.current_round,
                        continent_id = EXCLUDED.continent_id,
                        country_id = EXCLUDED.country_id,
                        updated_at = EXCLUDED.updated_at
                """)
                
                # 执行SQL
                cur.execute(query, dict(item))
                conn.commit()
                
                sub_id_info = f", sub_league_id={item['sub_league_id']}" if item['sub_league_id'] else ""
                self.logger.info(f"联赛信息已保存: league_id={item['league_id']}{sub_id_info}")
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"保存联赛信息出错: {str(e)}")
        finally:
            if conn:
                self.db_pool.putconn(conn)
        return item

class TeamPipeline(BasePipeline):
    """处理球队数据的pipeline"""
    
    def process_item(self, item, spider):
        if item is None:
            logger.debug(f"TeamPipeline: Received None item, skipping.")
            return None
        if not isinstance(item, TeamItem):
            return item
        if spider.name not in ['team', 'league', 'cup', 'match_result']: # 确保此pipeline处理来自这些爬虫的TeamItem
             self.logger.debug(f"TeamPipeline: Item from spider '{spider.name}' is not processed by this pipeline. Skipping team: {item.get('team_id')}")
             return item

        adapter = ItemAdapter(item)
        
        conn = self.db_pool.getconn()
        try:
            with conn.cursor() as cur:
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS teams (
                        id SERIAL PRIMARY KEY,
                        team_id VARCHAR(50) UNIQUE NOT NULL,
                        team_name_simp VARCHAR(255),
                        team_name_cant VARCHAR(255),
                        team_name_en VARCHAR(255),
                        team_logo VARCHAR(255),
                        country_id VARCHAR(50) REFERENCES countries(country_id) ON DELETE SET NULL,
                        league_id VARCHAR(50),
                        sub_league_id VARCHAR(50),
                        season VARCHAR(50),
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit() 

                data = {
                    'team_id': adapter.get('team_id'),
                    'team_name_simp': adapter.get('team_name_simp'),
                    'team_name_cant': adapter.get('team_name_cant'),
                    'team_name_en': adapter.get('team_name_en'),
                    'team_logo': adapter.get('team_logo'),
                    'country_id': adapter.get('country_id'),
                    'league_id': adapter.get('league_id'),
                    'sub_league_id': adapter.get('sub_league_id'),
                    'season': adapter.get('season')
                }

                query = sql_builder.SQL("""
                    INSERT INTO teams (team_id, team_name_simp, team_name_cant, team_name_en, team_logo, country_id, league_id, sub_league_id, season)
                    VALUES (%(team_id)s, %(team_name_simp)s, %(team_name_cant)s, %(team_name_en)s, %(team_logo)s, %(country_id)s, %(league_id)s, %(sub_league_id)s, %(season)s)
                    ON CONFLICT (team_id) DO UPDATE SET
                        team_name_simp = EXCLUDED.team_name_simp,
                        team_name_cant = EXCLUDED.team_name_cant,
                        team_name_en = EXCLUDED.team_name_en,
                        team_logo = EXCLUDED.team_logo,
                        country_id = EXCLUDED.country_id,
                        league_id = EXCLUDED.league_id,
                        sub_league_id = EXCLUDED.sub_league_id,
                        season = EXCLUDED.season,
                        updated_at = CURRENT_TIMESTAMP;
                """)
                
                cur.execute(query, data)
                conn.commit()
                self.logger.info(f"球队信息已保存/更新: team_id={adapter.get('team_id')}")
                    
        except psycopg2.Error as db_err:
            # Use conn.rollback() if get_connection provides a context manager that handles it
            # Otherwise, handle rollback explicitly if necessary
            try: conn.rollback() 
            except: pass # Ignore rollback errors if connection is already closed
            self.logger.error(f"保存球队数据时发生数据库错误 (源ID: {adapter.get('team_id')}): {db_err}. Data: {data}")
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"保存球队数据时发生未知错误 (源ID: {adapter.get('team_id')}): {str(e)}. Data: {data}", exc_info=True)
        finally:
            if conn:
                self.db_pool.putconn(conn)
        return item

class CountryImagePipeline(ImagesPipeline):
    """自定义图片Pipeline，用于将国家Logo保存到指定路径和文件名"""

    def file_path(self, request, response=None, info=None, *, item=None):
        """重写此方法以定义图片的存储路径和文件名"""
        adapter = ItemAdapter(item)
        country_id = adapter.get('country_id')
        if country_id is not None:
            # 构造期望的文件路径: country/country_{country_id}.png
            image_guid = f"country_{country_id}.png" # 使用ID构造文件名
            image_path = f"country/{image_guid}" # 返回相对于IMAGES_STORE的路径
            logger.info(f"CountryImagePipeline: Generated image path for country_id {country_id}: {image_path}") # 添加日志
            return image_path
        else:
            logger.warning(f"无法获取Country ID，无法为图片生成自定义路径: {request.url}")
            raise DropItem(f"Missing country_id for image: {request.url}")

    def image_downloaded(self, response, request, info, *, item=None):
        # Log response details before Pillow tries to open the image
        logger.info(f"Image downloaded: URL={request.url}, Status={response.status}, Headers={response.headers}")
        logger.debug(f"Image response content (first 100 bytes): {response.body[:100]}")
        try:
            # This is where the original ImagesPipeline would process the image
            # We can call the superclass method if we need its functionality
            # or handle image processing (like saving) directly or via item
            checksum = None
            # If you want to save the image and get a checksum (like the original pipeline):
            # from scrapy.utils.misc import md5sum
            # checksum = md5sum(response.body)
            # path = self.file_path(request, response=response, info=info, item=item)
            # self.store.persist_file(path, response.body, info) # self.store needs to be initialized

            # For now, just log and let the item carry the info if needed
            # The actual saving is handled by the ImagesPipeline superclass mechanisms
            # if we call super().image_downloaded() or if the default behavior is triggered
            logger.info(f"Image for item {item.get('country_name')} downloaded successfully from {request.url}")

        except Exception as e:
            logger.error(f"Error processing image for item {item.get('country_name')} from {request.url}: {e}", exc_info=True)
            raise DropItem(f"Failed to process image {request.url}")
        
        # IMPORTANT: To have Scrapy save the file, you must return the results
        # as the superclass's image_downloaded method would.
        # This usually involves returning a list of 2-tuples: (success, dict_with_image_info)
        # For simplicity here if we are just logging and not altering the item/flow for already downloaded files:
        # We might need to call the superclass's method.
        # However, the "uptodate" status is determined *before* image_downloaded is called.
        # This method is called only for *newly* downloaded images.
        
        # Since the issue is "uptodate", this method might not even be getting called.
        # The primary change for "uptodate" issue is IMAGES_EXPIRES.
        return super().image_downloaded(response, request, info, item=item)

    # 可选：重写 get_media_requests 以便在请求中传递 item 信息 (如果需要)
    # def get_media_requests(self, item, info):
    #     # Extract item from meta if passed via request
    #     item = request.meta.get('item', item)
    #     urls = ItemAdapter(item).get(self.images_urls_field, [])
    #     # Pass item in meta to make it available in file_path
    #     return [Request(u, meta={'item': item}) for u in urls]
    
    # Note: The default get_media_requests passes the item to Request meta
    # if ImagesPipeline itself is instantiated correctly. Let's ensure it is.
    # No need to override get_media_requests if the default behavior works.

class PostgreSQLPipeline:
    def __init__(self, db_pool_settings, batch_size_matches, batch_size_odds):
        self.db_pool_settings = db_pool_settings
        self.db_pool = None
        self.batch_size_matches = batch_size_matches
        self.batch_size_odds = batch_size_odds
        self.match_buffer = []
        self.logger = logging.getLogger(self.__class__.__name__)
        self.notified_in_this_run = set() 
        self.retry_needed_matches = set() 

        # Initialize attributes for odds batch processing
        self.odds_batch_enable = self.db_pool_settings.getbool('PIPELINE_ODDS_BATCH_ENABLE', False) # Default to False
        self.odds_buffer = [] # Use self.odds_buffer for batching odds
        self.total_matches_processed = 0 # Initialize counters
        self.total_odds_processed = 0
        self.total_leagues_processed = 0
        self.total_teams_processed = 0
        self.league_cache = set() # Initialize league_cache

    @classmethod
    def from_crawler(cls, crawler):
        # 标准的初始化方式
        settings = crawler.settings
        # 从 settings 中获取批处理大小，如果未定义则使用默认值
        match_batch_size = settings.getint('PIPELINE_MATCH_BATCH_SIZE', 50)
        odds_batch_size = settings.getint('PIPELINE_ODDS_BATCH_SIZE', 100)
        return cls(
            db_pool_settings=settings, # 将整个 settings 对象作为 db_pool_settings 传递
            batch_size_matches=match_batch_size,
            batch_size_odds=odds_batch_size
        )

    def open_spider(self, spider):
        """Spider开启时调用，初始化数据库连接池"""
        # 检查 self.db_pool_settings 是否存在且有效
        if not hasattr(self, 'db_pool_settings') or not self.db_pool_settings:
             self.logger.error("Pipeline db_pool_settings 未在 __init__ 或 from_crawler 中正确设置.")
             # 尝试从 spider 对象获取 settings (备用方案)
             if hasattr(spider, 'settings'):
                  self.db_pool_settings = spider.settings # 修正：赋值给 self.db_pool_settings
                  self.logger.info("从 spider 对象获取了 db_pool_settings.")
             else:
                  self.logger.error("无法获取 settings 对象，数据库连接池可能无法初始化！")
                  return
                  
        try:
            # 使用 self.db_pool_settings 初始化连接池
            self.db_pool = init_db_pool(self.db_pool_settings) 
            self.notified_in_this_run.clear()
            self.logger.info("Pipeline opened. notified_in_this_run set has been cleared.")
        except Exception as e:
            self.logger.error(f"在 open_spider 中初始化数据库连接池失败: {e}")
            # raise # 可以选择在这里抛出错误阻止爬虫继续

    def close_spider(self, spider):
        """Spider关闭时调用，关闭数据库连接池"""
        # 如果启用了批处理，处理剩余的批次
        if self.odds_batch_enable and self.odds_buffer: # Check self.odds_buffer
            logger.info(f"Spider 关闭，处理剩余 {len(self.odds_buffer)} 条赔率数据...")
            self._process_odds_batch() # This method should process self.odds_buffer
            
        if self.db_pool:
            try:
                self.db_pool.closeall() # Use closeall() instead of close()
                self.logger.info("数据库连接池已在pipeline关闭时成功关闭。")
            except Exception as e:
                self.logger.error(f"关闭数据库连接池时出错: {e}")
        logger.info(f"爬虫关闭. 共处理 Match: {self.total_matches_processed}, Odds: {self.total_odds_processed}")

    def process_item(self, item, spider):
        """处理数据项"""
        try:
            # 检查任务是否已被暂停或终止
            if hasattr(spider, 'task_logger') and spider.task_logger and spider.task_logger.check_if_terminated():
                logger.info(f"任务已被暂停或终止，跳过处理数据项")
                return item
                
            # 如果item为None，说明已经被上一个Pipeline(IncrementalUpdatePipeline)过滤掉了，直接返回
            if item is None:
                logger.info("数据项为None（已被增量更新Pipeline过滤），跳过处理")
                return None
                
            # 记录接收到的数据项类型
            item_type = type(item).__name__
            if isinstance(item, dict) and 'match_id' in item and 'update_time' in item:
                item_type = "赔率数据(dict)"
                item_id = f"{item['match_id']}-{item['bookmaker_id']}-{item['update_time']}"
            elif isinstance(item, dict):
                item_type = "其他dict数据"
                item_id = str(hash(frozenset(item.items())))
            else:
                item_id = getattr(item, 'id', None) or getattr(item, 'match_id', None) or getattr(item, 'team_id', None) or getattr(item, 'league_id', None) or "未知ID"
                
            logger.info(f"PostgreSQLPipeline接收到数据项，类型: {item_type}, ID: {item_id}")
            
            # Check for retry marker first
            if isinstance(item, dict) and 'retry_match_id' in item:
                match_id_to_retry = item['retry_match_id']
                logger.info(f"检测到赔率重试标记，比赛ID: {match_id_to_retry}")
                self._queue_odds_retry(match_id_to_retry)
                return None # Stop processing this marker further

            # Check for retry success marker
            if isinstance(item, dict) and 'retry_success' in item:
                queue_id_to_complete = item['retry_success']
                match_id_completed = item.get('match_id', '未知') # Get match_id for logging
                logger.info(f"检测到赔率重试成功标记，队列ID: {queue_id_to_complete}, 比赛ID: {match_id_completed}")
                self._mark_retry_completed(queue_id_to_complete)
                return None # Stop processing this marker further

            if isinstance(item, LeagueItem):
                logger.info("处理联赛数据")
                return self._process_league(item)
            elif isinstance(item, TeamItem):
                logger.info("处理球队数据")
                return self._process_team(item)
            elif isinstance(item, MatchItem):
                logger.info("处理比赛数据")
                self._process_match(item)
            elif isinstance(item, dict) and 'match_id' in item and 'update_time' in item:
                logger.info("处理赔率数据")
                self._process_odds(item)
            elif isinstance(item, ContinentItem):
                logger.info(f"PostgreSQLPipeline接收到ContinentItem (ID: {item.get('continent_id')}), 由ContinentPipeline处理，此处跳过。")
                return item
            elif isinstance(item, CountryItem):
                logger.info(f"PostgreSQLPipeline接收到CountryItem (ID: {item.get('country_id')}), 由CountryPipeline处理，此处跳过。")
                return item
            elif isinstance(item, CupItem):
                logger.info(f"PostgreSQLPipeline接收到CupItem (ID: {item.get('cup_id')}), 由CupPipeline处理，此处跳过。")
                return item
            else:
                logger.warning(f"未知的数据类型: {type(item)}")
            return item
        except Exception as e:
            logger.error(f"Error processing item {type(item)}: {str(e)}")
            raise

    def _process_league(self, item):
        """处理联赛数据"""
        logger.info(f"处理联赛数据: {item['league_short_name']} (ID: {item['league_id']}, sub_id: {item.get('sub_league_id', '')})")
        
        try:
            # 处理total_rounds和current_round的类型转换
            total_rounds = item.get('total_rounds')
            current_round = item.get('current_round')
            
            # 如果是数字字符串，则转换为整数
            if isinstance(total_rounds, str) and total_rounds.isdigit():
                total_rounds = int(total_rounds)
            elif not isinstance(total_rounds, int):
                total_rounds = None
                
            if isinstance(current_round, str) and current_round.isdigit():
                current_round = int(current_round)
            elif not isinstance(current_round, int):
                current_round = None

            # 处理sub_league_id，确保如果为空字符串或None，则统一使用空字符串，而不是NULL
            sub_league_id = item.get('sub_league_id', '')
            if sub_league_id is None:
                sub_league_id = ''  # 使用空字符串代替NULL值

            # 首先检查是否存在相同的联赛记录
            check_sql = """
                SELECT league_id, current_season, sub_league_id
                FROM leagues
                WHERE league_id = %s AND current_season = %s AND sub_league_id = %s;
            """
            existing_league = None
            with self.db_pool.get_cursor() as cur:
                cur.execute(check_sql, (item['league_id'], item['current_season'], sub_league_id))
                existing_league = cur.fetchone()

            result = None
            if existing_league:
                # 如果存在，则更新除了league_id, sub_league_id和current_season之外的字段
                update_sql = """
                    UPDATE leagues
                    SET league_type = %s,
                        league_short_name = %s,
                        league_official_name_simp = %s,
                        league_official_name_cant = %s,
                        league_official_name_en = %s,
                        league_logo = %s,
                        available_seasons = %s,
                        total_rounds = %s,
                        current_round = %s,
                        continent_id = %s,
                        country_id = %s,
                        updated_at = NOW()
                    WHERE league_id = %s AND current_season = %s AND sub_league_id = %s
                    RETURNING league_id;
                """
                with self.db_pool.get_cursor() as cur:
                    cur.execute(update_sql, (
                        item['league_type'],
                        item['league_short_name'],
                        item.get('league_official_name_simp'),
                        item.get('league_official_name_cant'),
                        item.get('league_official_name_en'),
                        item.get('league_logo'),
                        item.get('available_seasons'),
                        total_rounds,
                        current_round,
                        item.get('continent_id'),
                        item.get('country_id'),
                        item['league_id'],
                        item['current_season'],
                        sub_league_id
                    ))
                    result = cur.fetchone()
                    cur.connection.commit()
                    logger.info(f"更新联赛数据成功: {item['league_short_name']} (ID: {item['league_id']})")
            else:
                # 如果不存在，则插入新记录
                insert_sql = """
                    INSERT INTO leagues (
                        league_id, sub_league_id, league_type, league_short_name, 
                        league_official_name_simp, league_official_name_cant, league_official_name_en,
                        league_logo, available_seasons, current_season, 
                        total_rounds, current_round, continent_id, country_id,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    RETURNING league_id;
                """
                with self.db_pool.get_cursor() as cur:
                    try:
                        cur.execute(insert_sql, (
                            item['league_id'],
                            sub_league_id,  # 使用空字符串代替NULL
                            item['league_type'],
                            item['league_short_name'],
                            item.get('league_official_name_simp'),
                            item.get('league_official_name_cant'),
                            item.get('league_official_name_en'),
                            item.get('league_logo'),
                            item.get('available_seasons'),
                            item['current_season'],
                            total_rounds,
                            current_round,
                            item.get('continent_id'),
                            item.get('country_id')
                        ))
                        result = cur.fetchone()
                        cur.connection.commit()
                        logger.info(f"插入联赛数据成功: {item['league_short_name']} (ID: {item['league_id']})")
                    except Exception as e:
                        logger.error(f"插入联赛数据时出错: {str(e)}")
                        cur.connection.rollback()
                        raise

            if result:
                # 使用联赛ID和子联赛ID的组合作为缓存键
                cache_key = f"{item['league_id']}_{sub_league_id}"
                self.league_cache.add(cache_key)
                logger.info(f'成功处理联赛数据并添加到缓存: {item["league_short_name"]} (ID: {item["league_id"]}, cache_key: {cache_key})')
                self.total_leagues_processed += 1
        except Exception as e:
            logger.error(f'处理联赛数据时出错: {str(e)}')
        
        return item

    def _process_team(self, item):
        """处理球队数据 (独立于联赛/赛季)"""
        adapter = ItemAdapter(item)
        team_source_id = adapter.get('team_id')

        if not team_source_id:
            logger.error("TeamItem缺少team_id，无法处理。")
            return item # Or DropItem

        logger.info(f"开始处理球队数据: 源ID={team_source_id}, 名称={adapter.get('team_name_simp')}")

        conn = self.db_pool.getconn()
        try:
            # SQL statement only includes team-specific fields
            # ON CONFLICT checks only team_id (assuming team_id from source is the unique identifier)
            query = sql_builder.SQL("""
                INSERT INTO teams (
                    team_id, team_name_simp, team_name_cant, team_name_en, 
                    team_logo, created_at, updated_at
                ) VALUES (
                    %(team_id)s, %(team_name_simp)s, %(team_name_cant)s, %(team_name_en)s, 
                    %(team_logo)s, NOW(), NOW()
                )
                ON CONFLICT (team_id) 
                DO UPDATE SET
                    team_name_simp = EXCLUDED.team_name_simp,
                    team_name_cant = EXCLUDED.team_name_cant,
                    team_name_en = EXCLUDED.team_name_en,
                    team_logo = EXCLUDED.team_logo,
                    updated_at = NOW()
                RETURNING id; -- Return the database primary key (id)
            """)
            
            # Data dictionary with only team-specific fields
            data = {
                'team_id': team_source_id,
                'team_name_simp': adapter.get('team_name_simp'),
                'team_name_cant': adapter.get('team_name_cant'),
                'team_name_en': adapter.get('team_name_en'),
                'team_logo': adapter.get('team_logo')
            }

            with conn.cursor() as cur:
                cur.execute(query, data)
                result = cur.fetchone()
                conn.commit()
                if result:
                    db_id = result[0]
                    self.logger.info(f'成功保存球队数据: 源ID={team_source_id}, DB ID={db_id}, 名称={adapter.get("team_name_simp")}')
                    self.total_teams_processed += 1
                else: # Should not happen with RETURNING id unless insert/update failed silently
                        self.logger.warning(f'处理球队数据后未返回DB ID: 源ID={team_source_id}')

        except psycopg2.Error as db_err:
            # Use conn.rollback() if get_connection provides a context manager that handles it
            # Otherwise, handle rollback explicitly if necessary
            try: conn.rollback() 
            except: pass # Ignore rollback errors if connection is already closed
            self.logger.error(f"保存球队数据时发生数据库错误 (源ID: {team_source_id}): {db_err}. Data: {data}")
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"保存球队数据时发生未知错误 (源ID: {team_source_id}): {str(e)}. Data: {data}", exc_info=True)
        finally:
            if conn:
                self.db_pool.putconn(conn)
        return item

    def _process_match(self, item):
        """处理比赛数据，将其存入 matches 表"""
        adapter = ItemAdapter(item)
        match_source_id = adapter.get('match_id')
        db_league_id = adapter.get('league_ref_id') # 从league_id修改为league_ref_id，匹配MatchItem类
        db_cup_id = adapter.get('cup_ref_id')          # Changed from adapter.get('cup') assuming item directly has cup_id
        home_team_source_id = adapter.get('home_team_id_source')
        away_team_source_id = adapter.get('away_team_id_source')
        
        self.logger.info(f"开始处理比赛数据: 源ID={match_source_id}")
        self.logger.info(f"比赛数据 league_ref_id = {db_league_id}, cup_ref_id = {db_cup_id}")

        home_team_db_id = None
        away_team_db_id = None
        data_for_insert = None # Initialize to ensure it's available for logging in case of early error
        conn = None 

        try:
            conn = self.db_pool.getconn()
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # --- 1. 查找主客队的数据库ID (teams.id) ---
                self.logger.info(f"Pipeline: Querying for home_team_db_id. home_team_source_id: {home_team_source_id} (type: {type(home_team_source_id)})")
                
                # 调试表中是否存在该球队ID
                cur.execute("SELECT COUNT(*) FROM teams WHERE team_id = %s", (str(home_team_source_id),))
                count_res = cur.fetchone()[0]
                self.logger.info(f"Pipeline: 数据库中team_id={home_team_source_id}的记录数: {count_res}")
                
                # 输出当前表中的所有team_id以便调试
                cur.execute("SELECT team_id FROM teams LIMIT 10")
                sample_teams = [row[0] for row in cur.fetchall()]
                self.logger.info(f"Pipeline: 数据库中前10个team_id样本: {sample_teams}")
                
                # 不再查询id字段，直接使用team_id作为外键值
                home_team_db_id = str(home_team_source_id) if count_res > 0 else None
                self.logger.info(f"Pipeline: 使用home_team_source_id作为home_team_db_id: {home_team_db_id}")
                
                if not home_team_db_id:
                    self.logger.warning(f"未在 teams 表中找到主队源ID {home_team_source_id} (比赛源ID: {match_source_id})，无法关联主队。")
                
                self.logger.info(f"Pipeline: Querying for away_team_db_id. away_team_source_id: {away_team_source_id} (type: {type(away_team_source_id)})")
                
                # 调试表中是否存在该球队ID
                cur.execute("SELECT COUNT(*) FROM teams WHERE team_id = %s", (str(away_team_source_id),))
                count_res = cur.fetchone()[0]
                self.logger.info(f"Pipeline: 数据库中team_id={away_team_source_id}的记录数: {count_res}")
                
                # 不再查询id字段，直接使用team_id作为外键值
                away_team_db_id = str(away_team_source_id) if count_res > 0 else None
                self.logger.info(f"Pipeline: 使用away_team_source_id作为away_team_db_id: {away_team_db_id}")
                
                if not away_team_db_id:
                     self.logger.warning(f"未在 teams 表中找到客队源ID {away_team_source_id} (比赛源ID: {match_source_id})，无法关联客队。")

                if home_team_db_id is None or away_team_db_id is None:
                     self.logger.error(f"缺少主队或客队的数据库ID，无法保存比赛记录 (比赛源ID: {match_source_id})。 HomeDBID: {home_team_db_id}, AwayDBID: {away_team_db_id}")
                     # No commit/rollback needed as we only did SELECTs and are returning early
                     # conn will be returned in finally block
                     return item
             
                # --- 2. 执行 UPSERT 操作 --- 
                query = sql_builder.SQL("""
                    INSERT INTO matches (
                        match_id, league_ref_id, cup_ref_id, season, sub_league_id, round,
                        match_time, home_team_id, away_team_id,
                        full_score, half_score,
                        scraped_1d, scraped_6h, scraped_2h, scraped_15m,
                        created_at, updated_at
                    ) VALUES (
                        %(match_id)s, %(league_ref_id)s, %(cup_ref_id)s, %(season)s, %(sub_league_id_source)s, %(round)s,
                        %(match_time)s, %(home_team_id)s, %(away_team_id)s,
                        %(full_score)s, %(half_score)s,
                        %(scraped_1d)s, %(scraped_6h)s, %(scraped_2h)s, %(scraped_15m)s,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                    ON CONFLICT (match_id) DO UPDATE SET
                        league_ref_id = EXCLUDED.league_ref_id,
                        cup_ref_id = EXCLUDED.cup_ref_id,
                        season = EXCLUDED.season,
                        sub_league_id = EXCLUDED.sub_league_id, 
                        round = EXCLUDED.round,
                        match_time = EXCLUDED.match_time,
                        home_team_id = EXCLUDED.home_team_id,
                        away_team_id = EXCLUDED.away_team_id,
                        full_score = EXCLUDED.full_score,
                        half_score = EXCLUDED.half_score,
                        scraped_1d = GREATEST(matches.scraped_1d, EXCLUDED.scraped_1d),
                        scraped_6h = GREATEST(matches.scraped_6h, EXCLUDED.scraped_6h),
                        scraped_2h = GREATEST(matches.scraped_2h, EXCLUDED.scraped_2h),
                        scraped_15m = GREATEST(matches.scraped_15m, EXCLUDED.scraped_15m),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE 
                        matches.full_score IS NULL OR matches.full_score = '' OR 
                        matches.full_score != EXCLUDED.full_score OR 
                        matches.match_time != EXCLUDED.match_time;
                """)

                data_for_insert = {
                    'match_id': match_source_id,
                    'league_ref_id': db_league_id, 
                    'cup_ref_id': db_cup_id,      
                    'season': adapter.get('season'),
                    'sub_league_id_source': adapter.get('sub_league_id_source'), 
                    'round': adapter.get('round'),
                    'match_time': adapter.get('match_time'),
                    'home_team_id': home_team_db_id,  # 直接使用球队源ID作为外键值 
                    'away_team_id': away_team_db_id,  # 直接使用球队源ID作为外键值
                    'full_score': adapter.get('full_score'),
                    'half_score': adapter.get('half_score'),
                    'scraped_1d': adapter.get('scraped_1d', False),
                    'scraped_6h': adapter.get('scraped_6h', False),
                    'scraped_2h': adapter.get('scraped_2h', False),
                    'scraped_15m': adapter.get('scraped_15m', False),
                }

                cur.execute(query, data_for_insert)
            
            conn.commit() # Commit the transaction if all successful (lookups and insert)
            self.logger.info(f"成功保存比赛数据: 源ID={match_source_id}")
            self.total_matches_processed += 1

        except psycopg2.Error as db_err:
            if conn: conn.rollback()
            self.logger.error(f"保存比赛数据时发生数据库错误 (源ID: {match_source_id}): {db_err}. Data: {data_for_insert if data_for_insert else 'unavailable'}")
        except Exception as e:
            if conn: conn.rollback() 
            self.logger.error(f"保存比赛数据时发生未知错误 (源ID: {match_source_id}): {str(e)}. Data: {data_for_insert if data_for_insert else 'unavailable'}", exc_info=True)
        finally:
            if conn: 
                self.db_pool.putconn(conn)
            
        return item

    def _process_odds(self, item):
        """处理单条赔率数据，使用连接池。"""
        match_id = item.get('match_id')
        bookmaker_id = item.get('bookmaker_id')
        update_time = item.get('update_time')
        if not all([match_id, bookmaker_id, update_time]):
            logger.error(f"赔率数据缺少关键字段: {item}")
            return

        if self.odds_batch_enable:
            self.odds_buffer.append(item)
            logger.info(f"赔率数据已添加到批处理队列 (比赛ID: {match_id}, 当前队列大小: {len(self.odds_buffer)})")
            if len(self.odds_buffer) >= self.batch_size_odds:
                logger.info(f"赔率批处理队列已满 (大小: {self.batch_size_odds})，开始处理批次...")
                self._process_odds_batch() # This method should process self.odds_buffer
            return # Item processed by adding to buffer or by batch

        # --- Code for non-batch processing (single item insert) ---
        logger.info(f"接收到赔率数据 (单条处理模式): match_id={match_id}, bookmaker_id={bookmaker_id}, update_time类型={type(update_time)}, update_time值={update_time}")
        
        # 定义SQL插入语句 - 修正唯一约束字段组合
        sql_insert_query = sql_builder.SQL("""
            INSERT INTO odds (match_id, odds_detail_id, bookmaker_id, home_win, draw, away_win, update_time)
            VALUES (%(match_id)s, %(odds_detail_id)s, %(bookmaker_id)s, %(home_win)s, %(draw)s, %(away_win)s, %(update_time)s)
            ON CONFLICT (match_id, odds_detail_id, bookmaker_id, update_time) DO NOTHING;
        """)
        
        # --- Existing code for odds processing ---
        conn = None  # 初始化连接变量
        try:
            # 从连接池获取连接
            conn = self.db_pool.getconn() 
            with conn.cursor() as cur:
                cur.execute(sql_insert_query, dict(item))
                rows_affected = cur.rowcount # 记录受影响的行数
                conn.commit()

                # -- 修改通知逻辑 (统一类型) --
                match_id_str = str(match_id) # 强制转为字符串
                logger.info(f"[DEBUG] _process_odds: Checking notification status for match_id={match_id_str}. Current notified set: {self.notified_in_this_run}")
                if match_id_str not in self.notified_in_this_run:
                    logger.info(f"[DEBUG] _process_odds: Condition MET for match_id={match_id_str}. Sending notification.")
                    logger.info(f"首次处理比赛 {match_id_str} 的赔率数据 (单条模式)，触发通知。")
                    _send_notification(match_id_str, update_type='odds_updated') # 发送字符串 ID
                    self.notified_in_this_run.add(match_id_str) # 添加字符串 ID 到 set
                    logger.info(f"[DEBUG] _process_odds: Added {match_id_str} to notified set.")
                else:
                    logger.info(f"[DEBUG] _process_odds: Match {match_id_str} already notified in this run. Skipping notification.")
                # -- 结束修改 --

        except psycopg2.Error as e:
            logger.error(f"处理赔率数据时数据库错误 (match_id={match_id}): {e}")
            if conn: # 如果连接存在且发生错误，则回滚
                 conn.rollback()
        except Exception as e:
             logger.error(f"处理赔率数据时发生意外错误 (match_id={match_id}): {e}")
             logger.error(traceback.format_exc()) # 打印详细堆栈
             if conn: # 如果连接存在且发生错误，则回滚
                 conn.rollback()
        finally:
            # 确保连接在使用后总是被放回池中
            if conn:
                self.db_pool.putconn(conn)
                logger.debug(f"Connection for odds processing (match_id={match_id}) returned to pool.")
    
    def _process_odds_batch(self):
        """处理赔率数据批次，使用连接池。"""
        if not self.odds_buffer: # Check self.odds_buffer
            return
            
        batch_size = len(self.odds_buffer) # Use self.odds_buffer
        logger.info(f"开始处理包含 {batch_size} 条赔率数据的批次")

        sql_batch_insert_query = sql_builder.SQL("""
            INSERT INTO odds (match_id, odds_detail_id, bookmaker_id, home_win, draw, away_win, update_time)
            VALUES %s
            ON CONFLICT (match_id, odds_detail_id, bookmaker_id, update_time) DO NOTHING;
        """)
        
        args_list = [
            (
                i.get('match_id'), i.get('odds_detail_id'), i.get('bookmaker_id'),
                i.get('home_win'), i.get('draw'), i.get('away_win'),
                i.get('update_time')
            ) for i in self.odds_buffer # Iterate over self.odds_buffer
        ]

        conn = None
        try:
            conn = self.db_pool.getconn()
            with conn.cursor() as cur:
                # 使用 execute_values 进行高效批量插入
                execute_values(cur, sql_batch_insert_query.as_string(cur), args_list) # 使用 sql_batch_insert_query
                affected_rows = cur.rowcount 
                conn.commit()
                    
                self.total_odds_processed += batch_size # 记录处理的总数
                logger.info(f"成功批量处理 {batch_size} 条赔率数据 (可能插入或忽略，数据库报告影响 {affected_rows} 行)，累计: {self.total_odds_processed}")

                # --- BEGIN: 为批量插入的赔率发送通知 (修改逻辑 + 统一类型 + 调试日志) ---
                processed_match_ids_str = set(str(item.get('match_id')) for item in self.odds_buffer if item.get('match_id'))
                newly_notified_count = 0
                logger.info(f"[DEBUG] _process_odds_batch: Checking notifications for batch. Match IDs in batch: {processed_match_ids_str}. Current notified set: {self.notified_in_this_run}")
                for match_id_str in processed_match_ids_str:
                    logger.info(f"[DEBUG] _process_odds_batch: Checking match_id={match_id_str}")
                    if match_id_str not in self.notified_in_this_run:
                        logger.info(f"[DEBUG] _process_odds_batch: Condition MET for match_id={match_id_str}. Sending notification.")
                        # 假设只要尝试批量插入就通知
                        _send_notification(match_id_str, update_type='odds_updated') # 发送字符串 ID
                        self.notified_in_this_run.add(match_id_str) # 添加字符串 ID 到 set
                        newly_notified_count += 1
                        logger.info(f"[DEBUG] _process_odds_batch: Added {match_id_str} to notified set.")
                    else:
                        logger.info(f"[DEBUG] _process_odds_batch: Match {match_id_str} already notified in this run. Skipping notification.")
                
                if newly_notified_count > 0:
                    logger.info(f"为 {newly_notified_count} 个新比赛触发了赔率更新通知 (批量处理)。")
                # --- END: 为批量插入的赔率发送通知 ---

        except psycopg2.Error as e:
            logger.error(f"批量处理赔率数据时数据库错误: {e}")
            if conn:
                 conn.rollback()
        except Exception as e:
             logger.error(f"批量处理赔率数据时发生意外错误: {e}")
             logger.error(traceback.format_exc())
             if conn:
                 conn.rollback()
        finally:
            if conn:
                self.db_pool.putconn(conn)
                logger.debug(f"Connection for odds batch processing returned to pool.")
            self.odds_buffer.clear() # Clear self.odds_buffer after processing
            logger.info(f"批处理完成，清空队列")

    def _queue_odds_retry(self, match_id):
        """Adds a match_id to the odds_retry_queue table."""
        insert_query = sql_builder.SQL("""
            INSERT INTO odds_retry_queue (match_id, status, attempts, created_at, updated_at)
            VALUES (%s, %s, %s, NOW(), NOW())
            ON CONFLICT (match_id) DO NOTHING;
        """)
        try:
            with self.db_pool.get_cursor() as cur:
                cur.execute(insert_query, (match_id, 'pending', 0))
                cur.connection.commit()
                logger.info(f"已将比赛ID {match_id} 添加到赔率重试队列 (如果不存在)")
        except Exception as e:
            logger.error(f"添加比赛ID {match_id} 到重试队列时出错: {str(e)}")
            # Consider if the connection needs rollback in case of error
            # Depending on db_pool implementation, might handle automatically 

    def _mark_retry_completed(self, queue_id):
        """Marks a retry task as completed in the odds_retry_queue table."""
        update_query = sql_builder.SQL("""
            UPDATE odds_retry_queue
            SET status = %s, updated_at = NOW()
            WHERE id = %s AND status != %s; -- Avoid updating already completed tasks
        """)
        try:
            with self.db_pool.get_cursor() as cur:
                cur.execute(update_query, ('completed', queue_id, 'completed'))
                cur.connection.commit()
                logger.info(f"已将队列ID {queue_id} 的重试任务标记为 completed")
        except Exception as e:
            logger.error(f"标记队列ID {queue_id} 为 completed 时出错: {str(e)}")
            # Consider rollback if using explicit transactions 