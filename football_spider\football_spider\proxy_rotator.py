import random
import logging
import time
from collections import defaultdict

logger = logging.getLogger(__name__)

class ProxyRotator:
    """代理IP轮换器，用于管理和轮换代理IP，避免被网站封禁"""
    
    def __init__(self, proxy_list=None, max_failures=3, ban_time=600):
        """
        初始化代理轮换器
        
        Args:
            proxy_list: 初始代理列表，格式为 ["ip:port", "ip:port"]
            max_failures: 一个代理连续失败多少次后被临时禁用
            ban_time: 禁用时间（秒）
        """
        self.proxy_list = proxy_list or []
        self.max_failures = max_failures
        self.ban_time = ban_time
        
        # 代理状态管理
        self.failure_counts = defaultdict(int)  # 记录每个代理的失败次数
        self.banned_until = {}  # 记录代理被禁用的时间
        self.last_used = {}  # 记录代理最后使用时间
        
        logger.info(f"初始化代理轮换器，代理数量: {len(self.proxy_list)}")
        
    def add_proxy(self, proxy):
        """添加新的代理到列表中"""
        if proxy not in self.proxy_list:
            self.proxy_list.append(proxy)
            logger.info(f"添加代理: {proxy}, 当前代理数量: {len(self.proxy_list)}")
            
    def add_proxies(self, proxy_list):
        """批量添加代理到列表中"""
        count = 0
        for proxy in proxy_list:
            if proxy not in self.proxy_list:
                self.proxy_list.append(proxy)
                count += 1
        logger.info(f"批量添加 {count} 个代理, 当前代理数量: {len(self.proxy_list)}")
        
    def remove_proxy(self, proxy):
        """从列表中移除代理"""
        if proxy in self.proxy_list:
            self.proxy_list.remove(proxy)
            # 清理相关状态
            if proxy in self.failure_counts:
                del self.failure_counts[proxy]
            if proxy in self.banned_until:
                del self.banned_until[proxy]
            if proxy in self.last_used:
                del self.last_used[proxy]
            logger.info(f"移除代理: {proxy}, 剩余代理数量: {len(self.proxy_list)}")
            
    def get_proxy(self):
        """获取一个可用的代理，返回格式为 'http://ip:port'"""
        now = time.time()
        
        # 筛选可用代理
        available_proxies = []
        for proxy in self.proxy_list:
            # 检查是否处于禁用期
            if proxy in self.banned_until and now < self.banned_until[proxy]:
                continue
                
            # 如果之前被禁用，现在已过期，重置失败计数
            if proxy in self.banned_until and now >= self.banned_until[proxy]:
                self.failure_counts[proxy] = 0
                del self.banned_until[proxy]
                
            available_proxies.append(proxy)
            
        if not available_proxies:
            logger.warning("没有可用代理!")
            return None
            
        # 随机选择一个代理
        proxy = random.choice(available_proxies)
        self.last_used[proxy] = now
        
        # 返回带协议的代理字符串
        if not proxy.startswith('http://') and not proxy.startswith('https://'):
            return f'http://{proxy}'
        return proxy
        
    def report_success(self, proxy):
        """报告代理使用成功，重置失败计数"""
        if proxy is None:
            return
            
        # 移除协议前缀
        if proxy.startswith('http://'):
            proxy = proxy[7:]
        elif proxy.startswith('https://'):
            proxy = proxy[8:]
            
        if proxy in self.failure_counts:
            self.failure_counts[proxy] = 0
            
    def report_failure(self, proxy):
        """报告代理使用失败，更新失败计数和禁用状态"""
        if proxy is None:
            return
            
        # 移除协议前缀
        if proxy.startswith('http://'):
            proxy = proxy[7:]
        elif proxy.startswith('https://'):
            proxy = proxy[8:]
            
        # 更新失败计数
        self.failure_counts[proxy] += 1
        
        # 检查是否需要禁用
        if self.failure_counts[proxy] >= self.max_failures:
            now = time.time()
            self.banned_until[proxy] = now + self.ban_time
            logger.warning(f"代理 {proxy} 已被临时禁用 {self.ban_time} 秒，连续失败 {self.failure_counts[proxy]} 次")
            
    def get_stats(self):
        """获取当前代理状态统计"""
        now = time.time()
        
        total = len(self.proxy_list)
        banned = sum(1 for p in self.proxy_list if p in self.banned_until and now < self.banned_until[p])
        available = total - banned
        
        return {
            'total': total,
            'available': available,
            'banned': banned,
            'failure_counts': dict(self.failure_counts)
        }
        
    def clear_stats(self):
        """清除所有状态统计"""
        self.failure_counts.clear()
        self.banned_until.clear()
        self.last_used.clear()
        logger.info("代理状态统计已清除")

# 单例模式，确保全局只有一个实例
_instance = None

def get_rotator():
    """获取ProxyRotator的全局唯一实例"""
    global _instance
    if _instance is None:
        _instance = ProxyRotator()
    return _instance 