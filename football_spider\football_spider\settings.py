# Scrapy settings for football_spider project
#
# For simplicity, this file contains only settings considered important or
# commonly used. You can find more settings consulting the documentation:
#
#     https://docs.scrapy.org/en/latest/topics/settings.html
#     https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
#     https://docs.scrapy.org/en/latest/topics/spider-middleware.html

import asyncio
import sys
import os
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量
current_script_dir = os.path.dirname(os.path.abspath(__file__))
project_root_dir = os.path.dirname(current_script_dir)
dotenv_path = os.path.join(project_root_dir, '.env')

if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path, override=True)


if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# PostgreSQL数据库配置
POSTGRESQL_HOST = os.getenv('DB_HOST', 'localhost')
POSTGRESQL_PORT = int(os.getenv('DB_PORT', '5432'))
POSTGRESQL_DATABASE = os.getenv('DB_NAME', 'oddluck')
POSTGRESQL_USER = os.getenv('DB_USER', 'postgres')
POSTGRESQL_PASSWORD = os.getenv('DB_PASSWORD')
POSTGRESQL_SSLMODE = os.getenv('POSTGRESQL_SSLMODE', 'disable') 

# 数据库连接池配置
DB_MIN_CONNECTIONS = 1  # 最小连接数
DB_MAX_CONNECTIONS = 5  # 减小连接池大小，避免远程数据库连接过多
DB_CONNECT_TIMEOUT = 15 # 新增：数据库连接超时时间（秒）

# 数据批量处理配置
ODDS_BATCH_SIZE = 100  # 赔率批量处理的批次大小
ODDS_BATCH_ENABLE = True  # 启用赔率批量处理
LEAGUE_BATCH_SIZE = 5  # 联赛批量处理的批次大小
LEAGUE_BATCH_ENABLE = True  # 启用联赛批量处理
TEAM_BATCH_SIZE = 20  # 球队批量处理的批次大小
TEAM_BATCH_ENABLE = True  # 启用球队批量处理
MATCH_BATCH_SIZE = 10  # 比赛批量处理的批次大小
MATCH_BATCH_ENABLE = True  # 启用比赛批量处理

# 查询缓存配置
ENABLE_QUERY_CACHE = True  # 启用查询缓存
CACHE_TTL = 300  # 缓存有效期（秒）

# 代理配置
PROXY_ENABLED = False  # 默认禁用代理，需要时手动启用
PROXY_LIST = []  # 代理列表，格式为 ["ip:port", "ip:port"]

# User-Agent配置
USER_AGENT_LIST = [
    # 桌面端浏览器
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    
    # 移动端浏览器
    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 11; SM-G998U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
]

# Scrapy设置
BOT_NAME = 'football_spider'
SPIDER_MODULES = ['football_spider.spiders']
NEWSPIDER_MODULE = 'football_spider.spiders'

# 爬虫基本设置
ROBOTSTXT_OBEY = False

# 并发设置
CONCURRENT_REQUESTS = 1  # 并发请求数
CONCURRENT_REQUESTS_PER_DOMAIN = 8  # 对同一网站的并发请求数
DOWNLOAD_TIMEOUT = 60  # 下载超时时间 (增加到 60 秒)
DOWNLOAD_DELAY = 1  # 下载延迟，避免请求过快被封
RANDOMIZE_DOWNLOAD_DELAY = True  # 随机化下载延迟

# 自动限流
AUTOTHROTTLE_ENABLED = True
# AUTOTHROTTLE_START_DELAY = 5  # 初始下载延迟（秒）
# AUTOTHROTTLE_MAX_DELAY = 60 # 最大下载延迟（秒）
# AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0 # 平均并发请求数
# AUTOTHROTTLE_DEBUG = False # 设置为 True 查看调试信息

# 重试设置
RETRY_ENABLED = True
RETRY_TIMES = 5  # 最大重试次数
RETRY_HTTP_CODES = [500, 502, 503, 504, 522, 524, 408, 429, 403]  # 需要重试的HTTP状态码

# 中间件配置
DOWNLOADER_MIDDLEWARES = {
    'football_spider.middlewares.RandomUserAgentMiddleware': 400,
    'football_spider.middlewares.ProxyMiddleware': 410,
    'football_spider.middlewares.CustomRetryMiddleware': 550,
    'scrapy.downloadermiddlewares.retry.RetryMiddleware': None,  # 禁用默认的重试中间件
}

# Item Pipeline - 数字越小优先级越高，越先执行
ITEM_PIPELINES = {
    'football_spider.pipelines.CountryImagePipeline': 1, # 使用自定义的 Image Pipeline
    # 'scrapy.pipelines.images.ImagesPipeline': 1, # 禁用默认的 Images Pipeline
    'football_spider.incremental_pipeline.IncrementalUpdatePipeline': 200,  # 增量更新Pipeline，优先级高于PostgreSQLPipeline
    'football_spider.pipelines.ContinentPipeline': 250,  # 大洲数据Pipeline
    'football_spider.pipelines.CountryPipeline': 260,    # 国家数据Pipeline
    'football_spider.pipelines.LeaguePipeline': 270,     # 联赛数据Pipeline
    'football_spider.pipelines.CupPipeline': 275,         # 新增：杯赛数据Pipeline
    'football_spider.pipelines.TeamPipeline': 280,       # 球队数据Pipeline
    'football_spider.pipelines.PostgreSQLPipeline': 300, # 通用数据Pipeline
}

# Images Pipeline specific settings
IMAGES_STORE = 'C:/Projects/oddluck/oddluck_dev/backend/static/images/logos/'
IMAGES_EXPIRES = 0  # 设置为1天，强制重新下载过期的图片（实际上会强制下载所有）
# Optional: Set minimum image size if needed
# IMAGES_MIN_HEIGHT = 60 
# IMAGES_MIN_WIDTH = 60
# Optional: Configure thumbnail generation if needed
# IMAGES_THUMBS = {
#     'small': (50, 50),
#     'big': (270, 270),
# }

# 日志配置
LOG_LEVEL = 'INFO'  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
# LOG_FILE = 'C:/Projects/oddluck/football_spider/spiders_log/%(name)s_%(time)s.log'  # 使用绝对路径测试动态文件名 (Failed)
# LOG_FILE = '../spiders_log/%(name)s_%(time)s.log'  # 动态文件名 (Failed)
# LOG_FILE = '../spiders_log/scrapy.log' # Revert to fixed filename -> Comment out this line
# 设置日志编码为 UTF-8
LOG_ENCODING = 'utf-8'

# 禁用cookies
COOKIES_ENABLED = False

# 默认请求头
DEFAULT_REQUEST_HEADERS = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# 缓存设置
HTTPCACHE_ENABLED = False
HTTPCACHE_EXPIRATION_SECS = 0
HTTPCACHE_DIR = 'httpcache'
HTTPCACHE_IGNORE_HTTP_CODES = []
HTTPCACHE_STORAGE = 'scrapy.extensions.httpcache.FilesystemCacheStorage'

# 请求优先级设置
SCHEDULER_PRIORITY_QUEUE = 'scrapy.pqueues.DownloaderAwarePriorityQueue'

# 启用统计收集
STATS_CLASS = 'scrapy.statscollectors.MemoryStatsCollector'

# 启用内存调试器
MEMDEBUG_ENABLED = False

# 启用异步处理
TWISTED_REACTOR = 'twisted.internet.asyncioreactor.AsyncioSelectorReactor'

# Set settings whose default value is deprecated to a future-proof value
REQUEST_FINGERPRINTER_IMPLEMENTATION = "2.7"
FEED_EXPORT_ENCODING = "utf-8"

# LOG_LEVEL = 'INFO' # 设置日志级别 INFO, WARNING, ERROR, CRITICAL
# LOG_FILE = None # 取消注释以输出到控制台
# LOG_FORMAT = '%(asctime)s [%(name)s] %(levelname)s: %(message)s'
# LOG_DATEFORMAT = '%Y-%m-%d %H:%M:%S' 