import scrapy
import json
import re
import logging
from datetime import datetime
from football_spider.items import ContinentItem

class ContinentSpider(scrapy.Spider):
    name = 'continent'
    # allowed_domains = ['titan007.com', 'zq.titan007.com'] # Removed
    # start_urls = ['https://zq.titan007.com/jsData/infoHeader.js'] # Removed
    
    custom_settings = {
        'ITEM_PIPELINES': {
            'football_spider.incremental_pipeline.IncrementalUpdatePipeline': 200,
            'football_spider.pipelines.ContinentPipeline': 300,
        },
        'DOWNLOAD_DELAY': 1, # This might not be strictly necessary anymore but can be kept
        'CONCURRENT_REQUESTS': 1, # This might not be strictly necessary anymore but can be kept
        'LOG_FILE': '../spiders_log/continent_spider.log',
        'LOG_LEVEL': 'INFO',
    }
    
    def __init__(self, task_id=None, *args, **kwargs):
        super(Continent<PERSON>pid<PERSON>, self).__init__(*args, **kwargs)
        self.task_id = task_id
        
    def start_requests(self):
        # Yield a dummy request to trigger the parse method once
        # Or, more cleanly, we can just directly call parse's logic
        # For simplicity with Scrapy's flow, we can make parse not expect a response
        # or just make it a generator that doesn't need a response.
        # Let's make parse a generator that doesn't use response.
        # Scrapy will call parse() if start_urls is empty and start_requests is not overridden to do something else.
        # However, to be explicit and avoid a request, we can override start_requests.
        yield scrapy.Request("data:,", callback=self.parse, dont_filter=True)

    def parse(self, response): # response argument is no longer used but kept for Scrapy's signature
        try:
            # 大洲ID（continent_id）：第0个的第3个，如0代表国际，1代表欧洲，2代表美洲，3代表亚洲，4代表大洋洲，5代表非洲
            continent_mappings = {
                0: {"name": "国际", "name_en": "International"},
                1: {"name": "欧洲", "name_en": "Europe"},
                2: {"name": "美洲", "name_en": "Americas"},
                3: {"name": "亚洲", "name_en": "Asia"},
                4: {"name": "大洋洲", "name_en": "Oceania"},
                5: {"name": "非洲", "name_en": "Africa"}
            }
            
            for continent_id, info in continent_mappings.items():
                item = ContinentItem()
                item['continent_id'] = continent_id
                item['continent_name'] = info['name']
                item['continent_name_en'] = info['name_en']
                item['created_at'] = datetime.now()
                item['updated_at'] = datetime.now()
                
                self.logger.info(f"提取到大洲信息: {item}")
                yield item
                
        except Exception as e:
            self.logger.error(f"生成大洲信息出错: {str(e)}") 