import scrapy
import json
import re
import logging
from datetime import datetime
from urllib.parse import urljoin # Use urljoin for robust URL construction
from football_spider.items import CountryItem

class CountrySpider(scrapy.Spider):
    name = 'country'
    allowed_domains = ['titan007.com', 'zq.titan007.com']
    start_urls = ['https://zq.titan007.com/jsData/infoHeader.js']
    
    # LOGO基础URL常量 (Updated)
    COUNTRY_LOGO_BASE_URL = 'https://zq.titan007.com/Image/info/'
    
    # 本地存储路径 (Path is handled by pipeline/settings, this is the URL part)
    # LOCAL_LOGO_PATH = '../../../oddluck_dev/backend/static/images/logos/country' # Removed, store path is configured elsewhere
    LOCAL_LOGO_URL = '/static/images/logos/country' # Updated path
    
    custom_settings = {
        # \'ITEM_PIPELINES\': { # Removed: Use global settings from settings.py
        #     \'football_spider.pipelines.CountryPipeline\': 300, \n        # },\
        'DOWNLOAD_DELAY': 1,
        'CONCURRENT_REQUESTS': 1,
        'LOG_FILE': '../spiders_log/country_spider.log',
        'LOG_LEVEL': 'INFO',
    }
    
    def __init__(self, task_id=None, *args, **kwargs):
        super(CountrySpider, self).__init__(*args, **kwargs)
        self.task_id = task_id
        # self.logger = logging.getLogger(f'{self.name}_spider') # Removed: Use self.logger directly
        
    def parse(self, response):
        try:
            js_content = response.text
            
            # Find all arr[index] = [...] assignments
            assignments = re.findall(r'arr\[\d+\]\s*=\s*(\[.*?\]);?', js_content)
            
            if not assignments:
                self.logger.error("无法在响应中找到 arr[...] = [...] 格式的数据")
                return

            # Regex to extract the first four elements directly
            # Captures: 1: InfoID_(\d+), 2: Country Name, 3: Logo Path, 4: Continent ID
            # It assumes standard JSON-like string quoting for name and logo path.
            # Needs to handle potential variations if quoting isn't consistent.
            # Example: ["InfoID_1","英格兰","images/164326923816.png","1",[...]]\n            # Regex uses non-greedy matching for strings and assumes numeric continent ID.
            pattern = re.compile(
                r'\[[ ]*"InfoID_(\d+)"[ ]*,[ ]*'
                r'"(.*?)"[ ]*,[ ]*'
                r'"(.*?)"[ ]*,[ ]*'
                r'"(\d+)"'
                # We don't need to match the rest of the array `,[...]`
            )

            for assignment_str in assignments:
                match = pattern.search(assignment_str)
                if match:
                    country_id_str, country_name, country_logo_path, continent_id_str = match.groups()
                    
                    try:
                        item = CountryItem()
                        item['country_id'] = int(country_id_str)
                        item['country_name'] = country_name
                        # Logo path might be empty string if no logo exists
                        item['country_logo'] = None
                        item['image_urls'] = [] # Default to empty list
                        
                        if country_logo_path:
                            # Construct full URL for download
                            full_logo_url = urljoin(self.COUNTRY_LOGO_BASE_URL, country_logo_path)
                            # Generate relative URL path for storage/linking
                            file_name = f"country_{country_id_str}.png"
                            local_logo_url_path = f"{self.LOCAL_LOGO_URL}/{file_name}"
                            item['country_logo'] = local_logo_url_path
                            item['image_urls'] = [full_logo_url]
                        
                        item['continent_id'] = int(continent_id_str)
                        item['created_at'] = datetime.now()
                        item['updated_at'] = datetime.now()
                        
                        # Skip invalid continent IDs if necessary (e.g., ID '0' for International)
                        if item['continent_id'] is not None: # and item['continent_id'] != 0:
                            if item['country_id'] is not None:
                                self.logger.info(f"提取到国家信息: {item}")
                                yield item
                            else:
                                self.logger.warning(f"从 '{assignment_str[:100]}...' 提取到无效的国家ID")
                        else:
                            self.logger.warning(f"从 '{assignment_str[:100]}...' 提取到无效的大洲ID '{continent_id_str}' 的国家: {country_name}")
                            
                    except ValueError as e:
                        self.logger.error(f"处理提取的数据时出错 (ID: {country_id_str}, Name: {country_name}): {e}")
                    except Exception as e:
                        self.logger.error(f"创建CountryItem时未知错误 (ID: {country_id_str}, Name: {country_name}): {str(e)}")
                else:
                    self.logger.warning(f"无法从以下赋值中提取国家数据: {assignment_str[:200]}...")
                                
        except Exception as e:
            self.logger.error(f"解析国家信息时出错: {str(e)}", exc_info=True)
    
    # This method is kept for conceptual clarity but doesn't download.
    # It just generates the *target* URL path where the logo *should* be if downloaded.
    def download_logo(self, url, country_id):
        # This method is no longer called directly in the parse logic above
        # The local path construction happens within parse itself.
        # If a pipeline needs this logic separately, it can be adapted or removed.
        try:
            file_name = f"country_{country_id}.png"
            local_path = f"{self.LOCAL_LOGO_URL}/{file_name}"
            return local_path
        except Exception as e:
            self.logger.error(f"生成Logo路径出错: {str(e)}")
            return None 