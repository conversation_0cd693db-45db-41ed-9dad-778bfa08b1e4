import scrapy
import json
import re
import os
import logging
import time
import requests
from datetime import datetime
from urllib.parse import urlparse, urljoin
from football_spider.items import CupItem # Changed from LeagueItem
import ast
import pytz
import psycopg2 # Added for database connection
from dotenv import load_dotenv # Added for .env loading

class CupSpider(scrapy.Spider): # Changed class name
    name = 'cup' # Changed spider name
    allowed_domains = ['titan007.com', 'zq.titan007.com']
    
    # LOGO基础URL常量
    CUP_LOGO_BASE_URL = 'https://zq.titan007.com/Image/' # Consider if URL is different for cups
    
    # 本地存储路径
    LOCAL_LOGO_PATH = '../../../oddluck_dev/backend/static/images/logos/cups' # Changed path
    LOCAL_LOGO_URL = '/static/images/logos/cups' # Changed path
    
    custom_settings = {
        'ITEM_PIPELINES': {
            'football_spider.pipelines.CupPipeline': 300, # Needs CupPipeline
        },
        'DOWNLOAD_DELAY': 1,
        'CONCURRENT_REQUESTS': 1,
        'LOG_FILE': '../spiders_log/cup_spider.log', # Add LOG_FILE setting
        'LOG_LEVEL': 'INFO',
    }
    
    def __init__(self, cup=None, season=None, task_id=None, *args, **kwargs): # Changed param name
        super(CupSpider, self).__init__(*args, **kwargs)

        # Load .env file from football_spider directory
        try:
            # Path to cup_spider.py: football_spider/football_spider/spiders/cup_spider.py
            # Path to .env: football_spider/.env
            current_script_path = os.path.abspath(__file__)
            # spiders_dir is .../football_spider/football_spider/spiders
            spiders_dir = os.path.dirname(current_script_path)
            # football_spider_internal_dir is .../football_spider/football_spider
            football_spider_internal_dir = os.path.dirname(spiders_dir)
            # football_spider_base_dir is .../football_spider (the one containing .env)
            football_spider_base_dir = os.path.dirname(football_spider_internal_dir)
            env_path = os.path.join(football_spider_base_dir, '.env')

            if os.path.exists(env_path):
                load_dotenv(dotenv_path=env_path)
                self.logger.info(f".env 文件已从 {env_path} 加载")
            else:
                self.logger.warning(f".env 文件未在 {env_path} 找到。如果环境变量中未设置凭据，数据库连接可能会失败。")
        except Exception as e:
            self.logger.error(f"加载 .env 文件时出错: {e}")
            
        self.task_id = task_id
        
        # Store user's cup filter preference
        self.user_cup_filter = cup
            
        self.raw_user_season_input = season # Store raw user input for season
        
        # Store extracted basic cup info from infoHeader.js for ALL found cups
        self.cup_basic_info = {} 
        # Target cup IDs will be determined after parsing infoHeader.js
        self.target_cup_ids = [] 
        
        self.logger.info(f"初始化CupSpider: 用户指定杯赛='{self.user_cup_filter}', 用户输入赛季='{self.raw_user_season_input}'")
    
    def _get_country_id_from_db(self, country_name_simp):
        """
        根据国家/地区简体中文名称从数据库查询 country_id。
        """
        db_host = os.environ.get('DB_HOST')
        db_port = os.environ.get('DB_PORT', '5432')
        db_name = os.environ.get('DB_NAME')
        db_user = os.environ.get('DB_USER')
        db_password = os.environ.get('DB_PASSWORD')
        db_sslmode = os.environ.get('POSTGRESQL_SSLMODE', 'prefer') # Default to 'prefer'

        if not all([db_host, db_name, db_user, db_password]):
            self.logger.error("数据库配置信息不完整。请检查 .env 文件或环境变量 (DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD)。")
            return None

        # 清理 sslmode 值：移除潜在的引号
        if isinstance(db_sslmode, str):
            if db_sslmode.startswith("'") and db_sslmode.endswith("'"):
                db_sslmode = db_sslmode[1:-1]
            elif db_sslmode.startswith('"') and db_sslmode.endswith('"'):
                db_sslmode = db_sslmode[1:-1]

        conn = None
        country_id_val = None
        try:
            conn = psycopg2.connect(
                host=db_host,
                port=db_port,
                dbname=db_name,
                user=db_user,
                password=db_password,
                sslmode=db_sslmode
            )
            cursor = conn.cursor()
            # 假设表名为 'countries'，包含 'country_id' 和 'country_name' 列 (根据用户截图更新)
            query = "SELECT country_id FROM countries WHERE country_name = %s LIMIT 1;"
            cursor.execute(query, (country_name_simp,))
            result = cursor.fetchone()
            
            if result:
                country_id_val = str(result[0]) #确保返回的是字符串
                self.logger.info(f"从数据库为国家 '{country_name_simp}' 获取到 country_id: {country_id_val}")
            else:
                self.logger.warning(f"未能在数据库中为国家 '{country_name_simp}' 找到 country_id。")
            
            cursor.close()
        except psycopg2.Error as e:
            self.logger.error(f"查询国家 '{country_name_simp}' 的 country_id 时发生数据库错误: {e}")
        except Exception as e:
            self.logger.error(f"为国家 '{country_name_simp}' 获取 country_id 时发生非数据库预期错误: {e}")
        finally:
            if conn:
                conn.close()
        
        return country_id_val

    def normalize_season(self, season_str): # Changed param name to avoid confusion
        """
        标准化赛季格式
        如果赛季是单个年份（如'2025'），将其转换为'2024-2025'格式
        如果已经是'2024-2025'格式，则保持不变
        """
        if not season_str:
            return None
            
        # 如果已经是YYYY-YYYY格式，直接返回
        if '-' in season_str:
            return season_str
            
        # 如果是单个年份，转换为YYYY-YYYY格式
        try:
            year = int(season_str)
            return f"{year-1}-{year}"
        except ValueError:
            self.logger.warning(f"无法解析赛季格式: {season_str}")
            return None
    
    def _determine_season_for_cup(self, cup_id):
        """根据用户输入和杯赛可用赛季智能确定请求URL应使用的赛季。"""
        cup_info = self.cup_basic_info.get(cup_id)
        if not cup_info:
            self.logger.error(f"无法为cup_id '{cup_id}' 找到基本信息，无法确定赛季。")
            return None

        cup_available_seasons = cup_info.get('available_seasons')
        if not cup_available_seasons or not isinstance(cup_available_seasons, list) or len(cup_available_seasons) == 0:
            self.logger.warning(f"cup_id '{cup_id}' (名称: {cup_info.get('cup_short_name')}) 没有可用的赛季信息 (available_seasons 为空或格式不正确)。")
            return None

        user_s = self.raw_user_season_input
        final_season_for_url = None

        if not user_s:  # 用户未提供season
            final_season_for_url = cup_available_seasons[0]
            self.logger.info(f"用户未提供赛季，cup_id '{cup_id}' 将使用其可用赛季列表的第一个: '{final_season_for_url}'")
        else:
            # 检查用户输入是否直接在可用赛季中
            if user_s in cup_available_seasons:
                final_season_for_url = user_s
                self.logger.info(f"用户提供赛季 '{user_s}' 在 cup_id '{cup_id}' 的可用赛季中找到，将使用它。")
            # 用户输入的是 YYYY-YYYY 格式
            elif '-' in user_s and len(user_s.split('-')) == 2 and all(p.isdigit() for p in user_s.split('-')):
                parts = user_s.split('-')
                year1_str = parts[0]
                year2_str = parts[1]
                if year1_str in cup_available_seasons:
                    final_season_for_url = year1_str
                    self.logger.info(f"用户提供赛季 '{user_s}' (YYYY-YYYY)，其起始年份 '{year1_str}' 在 cup_id '{cup_id}' 的可用赛季中找到。")
                elif year2_str in cup_available_seasons:
                    final_season_for_url = year2_str
                    self.logger.info(f"用户提供赛季 '{user_s}' (YYYY-YYYY)，其结束年份 '{year2_str}' 在 cup_id '{cup_id}' 的可用赛季中找到。")
                else:
                    final_season_for_url = cup_available_seasons[0]
                    self.logger.warning(f"用户提供赛季 '{user_s}' (YYYY-YYYY) 及其组成年份均未在 cup_id '{cup_id}' 的可用赛季中找到。将回退到可用列表第一个: '{final_season_for_url}'")
            # 用户输入的是 YYYY 格式
            elif user_s.isdigit() and len(user_s) == 4:
                normalized_s_cross_year = self.normalize_season(user_s) # YYYY -> YYYY-1 - YYYY
                if user_s in cup_available_seasons: # 检查 YYYY
                    final_season_for_url = user_s
                    self.logger.info(f"用户提供赛季 '{user_s}' (YYYY) 在 cup_id '{cup_id}' 的可用赛季中找到。")
                elif normalized_s_cross_year and normalized_s_cross_year in cup_available_seasons: # 检查 YYYY-1 - YYYY
                    final_season_for_url = normalized_s_cross_year
                    self.logger.info(f"用户提供赛季 '{user_s}' (YYYY)，其标准化后的跨年格式 '{normalized_s_cross_year}' 在 cup_id '{cup_id}' 的可用赛季中找到。")
                else:
                    final_season_for_url = cup_available_seasons[0]
                    self.logger.warning(f"用户提供赛季 '{user_s}' (YYYY) 及其标准化后的跨年格式均未在 cup_id '{cup_id}' 的可用赛季中找到。将回退到可用列表第一个: '{final_season_for_url}'") 
            else: # 用户输入格式不符合预期
                final_season_for_url = cup_available_seasons[0]
                self.logger.warning(f"用户提供赛季 '{user_s}' 格式无法识别或不符合预期处理规则。cup_id '{cup_id}' 将回退到可用列表第一个: '{final_season_for_url}'")
        
        return final_season_for_url

    def get_random_user_agent(self):
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
        ]
        return user_agents[int(time.time()) % len(user_agents)]
    
    def start_requests(self):
        """Start requests by fetching infoHeader.js first"""
        # Target validation will happen after infoHeader.js is parsed
        self.logger.info("开始请求 infoHeader.js 以发现杯赛...")
        yield scrapy.Request(
            url='https://zq.titan007.com/jsData/infoHeader.js',
            callback=self.parse_infoheader,
            headers={'User-Agent': self.get_random_user_agent()},
            meta={'dont_redirect': True, 'handle_httpstatus_list': [302]},
            errback=self.errback_handler
        )
        
    def parse_infoheader(self, response):
        """Parse infoHeader.js to get basic cup info and available seasons."""
        self.logger.info("正在解析 infoHeader.js (for Cups)")
        try:
            js_content = response.text
            
            # Correctly extract the main 'arr' structure
            # Regex looks for: var arr = [...] ; (and allows for Array() constructor)
            # It captures the content between the outermost brackets [...]
            main_arr_match = re.search(r'var\s*arr\s*=\s*(?:new\s+Array\((?P<constructor>.*?)\)|(?P<literal>\[.*\]))\s*;', js_content, re.DOTALL | re.IGNORECASE)
            
            if not main_arr_match:
                self.logger.error("无法从 infoHeader.js 提取主要的 arr 结构 (for Cups)")
                return

            country_data_matches = [] # Initialize to ensure it's defined
            if main_arr_match.group('constructor') is not None:
                # Handles: var arr = new Array(arr[0]=..., arr[1]=...); arr = arr[0]; (or similar)
                # We need to find assignments like arr[index] = [...]
                # This simplified regex finds all "arr[<index>] = <value>;" parts
                # and we'll parse <value> which should be a JS array string: [...]
                # The <value> is captured by (.*?), which takes everything between "arr[idx] = " and the next semicolon.
                country_data_matches = re.findall(r'arr\s*\[\d+\]\s*=\s*(.*?);', js_content, re.DOTALL) # Ensured single semicolon at the end
            elif main_arr_match.group('literal') is not None:
                # Handles: var arr = [[...], [...], ...];
                # We need to parse the outer list and then each inner list
                # This is a bit more complex with regex alone, direct ast.literal_eval on the captured group is better
                try:
                    # The captured group is a string representation of a list of lists
                    # Example: "[[data_country1], [data_country2]]"
                    # Replace undefined with None for safe evaluation, handle potential JS quirks
                    countries_list_str = main_arr_match.group('literal')
                    countries_list_str = countries_list_str.replace('undefined', 'None')
                    
                    # Attempt to evaluate the string as a Python list of lists
                    # This requires careful handling of JS array syntax vs Python list syntax
                    # For simple arrays of strings and numbers, it might work.
                    # A more robust solution would be a proper JS parser if complexity increases.
                    
                    # Let's try to parse it as a list of strings first, then eval each string
                    # This assumes each country_info is a string representation of a list
                    # e.g. "['England', ...]", "['Spain', ...]"
                    # This is safer than a direct eval on the whole structure initially.
                    
                    # A simplified assumption: the main list is directly evaluable
                    # We expect something like: "[[id,name,logo,continent_id,[cup_data_str1, cup_data_str2]], ...]"
                    
                    # We need to be careful with eval. A safer approach for complex JS is needed.
                    # For now, assuming simple structure that json.loads might handle after some cleaning.
                    # Let's try a json.loads approach after ensuring valid JSON syntax (e.g. single quotes to double)
                    try:
                        # Replace single quotes with double quotes for JSON compatibility, carefully
                        # to avoid messing up quotes within strings.
                        # This is a common hack and might not be fully robust.
                        json_like_str = countries_list_str.replace("'", '"')
                        # Further cleaning might be needed, e.g. for trailing commas if any.
                        
                        all_countries_data = json.loads(json_like_str)
                        country_data_matches = [json.dumps(country_data) for country_data in all_countries_data] # re-stringify for uniform processing below
                    except json.JSONDecodeError:
                        self.logger.error(f"无法使用 json.loads 解析 infoHeader.js 中的 literal arr 结构: {countries_list_str[:200]}...")
                        # Fallback or error out if direct parsing fails
                        return
                except Exception as e:
                    self.logger.error(f"解析 infoHeader.js 中的 literal arr 结构时发生错误: {e}")
                    return
            else:
                # This case should ideally not be reached if main_arr_match was successful and regex is correct
                self.logger.error("无法从 infoHeader.js 提取 arr 数据 (for Cups) - 未知格式或正则捕获组逻辑问题")
                return

            if not country_data_matches:
                self.logger.error("从 infoHeader.js 解析后未找到任何国家/赛事组织数据条目 (for Cups)")
                return

            parsed_cup_count = 0
            for country_info_str in country_data_matches:
                try:
                    # Each country_info_str should be a string like "['England', 'logo.png', id, [cup1_str, cup2_str]]"
                    # Replace undefined with None before eval
                    country_info_str_cleaned = country_info_str.replace('undefined', 'None')
                    # Ensure string literals within the list are properly quoted for eval if they come from JSON.
                    # If it's already a Python-like string representation, direct eval might work.
                    
                    # Given country_data_matches now contains strings of lists (either from regex or json.dumps)
                    # We can attempt ast.literal_eval
                    parts = ast.literal_eval(country_info_str_cleaned)

                    # Basic validation of structure for a country/region entry
                    if not isinstance(parts, list) or len(parts) < 5:
                        self.logger.warning(f"跳过格式不正确的国家/地区条目: {country_info_str_cleaned[:100]}...")
                        continue

                    # Extract country/continent information
                    # arr[i][0]: InfoID_X (seems like an internal ID, maybe country specific for the site)
                    # arr[i][1]: Country Name (e.g., "英格兰", "欧洲赛事")
                    # arr[i][2]: Country Logo
                    # arr[i][3]: Continent ID (e.g., "1" for Europe, "0" for International)
                    # arr[i][4]: List of competitions (leagues and cups)
                    
                    country_name_simp = parts[1] if len(parts) > 1 else "Unknown Country"
                    continent_id = str(parts[3]) if len(parts) > 3 else None # Ensure it's a string
                    competitions_data_list = parts[4] if len(parts) > 4 and isinstance(parts[4], list) else []

                    if not continent_id:
                        self.logger.warning(f"国家/地区 '{country_name_simp}' 缺少 continent_id，跳过其下的赛事。")
                        continue
                        
                    # Get country_id from database (or simulation)
                    country_id = self._get_country_id_from_db(country_name_simp)
                    if not country_id: # Log if country_id couldn't be found, but proceed with None
                        self.logger.warning(f"未能为国家/地区 '{country_name_simp}' 获取 country_id。Cup items related will have None.")
                        
                    for comp_detail_str in competitions_data_list:
                        # comp_detail_str is like "90,英足总杯,2,0,2024-2025,2023-2024,..."
                        comp_parts = [p.strip() for p in comp_detail_str.split(',')]
                        
                        if len(comp_parts) < 4:  # Need at least id, name, type_part1, type_part2
                            self.logger.warning(f"跳过信息不完整的赛事条目: '{comp_detail_str}' in {country_name_simp}")
                            continue
                        
                        cup_id_from_source = comp_parts[0]
                        cup_short_name_from_source = comp_parts[1]
                        match_type = f"{comp_parts[2]},{comp_parts[3]}"
                        
                        # DEBUG LOGGING (can be removed later)
                        # if match_type == '2,0':
                        #     self.logger.info(f"DEBUG: Cup Entry Details: comp_detail_str='{comp_detail_str}', Parts='{comp_parts}'")

                        if match_type == '2,0': # Process only cups
                            available_seasons_from_source = [s.strip() for s in comp_parts[4:] if s.strip()]
                            if not available_seasons_from_source:
                                self.logger.warning(f"杯赛 {cup_short_name_from_source} (ID: {cup_id_from_source}) 在 infoHeader.js 中没有有效的可用赛季。")
                                # Decide if we should skip or proceed with an empty list.
                                # For now, we'll store it but _determine_season_for_cup will handle no seasons.

                            # Store basic info, including available_seasons, continent_id, and country_name
                            self.cup_basic_info[cup_id_from_source] = {
                                'cup_short_name': cup_short_name_from_source,
                                'match_type': "杯赛", # Store Chinese representation
                                'available_seasons': available_seasons_from_source,
                                'continent_id': continent_id, 
                                'country_name_simp': country_name_simp, # Keep for reference if needed
                                'country_id': country_id # Add country_id
                            }
                            parsed_cup_count += 1
                            
                            # Apply user's cup filter if provided
                            if self.user_cup_filter:
                                if self.user_cup_filter.lower() in cup_id_from_source.lower() or \
                                   self.user_cup_filter.lower() in cup_short_name_from_source.lower():
                                    if cup_id_from_source not in self.target_cup_ids:
                                        self.target_cup_ids.append(cup_id_from_source)
                            else: # If no filter, add all found cup_ids
                                if cup_id_from_source not in self.target_cup_ids:
                                    self.target_cup_ids.append(cup_id_from_source)
                
                except (SyntaxError, ValueError, TypeError, IndexError) as e_item:
                    self.logger.error(f"解析infoHeader.js中单个国家/地区条目失败: '{country_info_str[:100]}...' - 错误: {e_item}", exc_info=False) # Reduce noise by not printing full exc_info for each item
                except Exception as e_global:
                    self.logger.error(f"解析infoHeader.js时发生未预料的错误处理条目 '{country_info_str[:100]}...': {e_global}", exc_info=True)


            self.logger.info(f"从 infoHeader.js 解析到 {parsed_cup_count} 个杯赛的基础信息。")

            if not self.target_cup_ids and self.user_cup_filter:
                 self.logger.warning(f"用户指定了杯赛过滤器 '{self.user_cup_filter}'，但在 infoHeader.js 中未找到匹配的杯赛。")
                 return # Stop if filter is given but no match
            elif not self.target_cup_ids:
                self.logger.warning("在 infoHeader.js 中没有找到任何符合条件的杯赛 (match_type='2,0')。")
                return

            # Log the final list of target cup IDs that will be processed
            self.logger.info(f"最终确定的目标杯赛ID列表: {self.target_cup_ids}")
            
            # IMPORTANT: This method should YIELD requests, not call another method that yields them,
            # unless that method is specifically designed to be called and its yielded items re-yielded.
            # The previous `yield from self.request_cup_details()` was correct.
            yield from self.request_cup_details() 
            
        except Exception as e:
            self.logger.error(f"解析 infoHeader.js (Cups) 时发生意外错误: {str(e)}", exc_info=True)

    def request_cup_details(self): # Changed method name
        """Request detailed info for each target cup."""
        version = time.strftime("%Y%m%d%H", time.localtime())
        requests_made = 0
        
        for cup_id in self.target_cup_ids:
            # Call the new helper method to determine the season to use for this cup_id
            season_to_use = self._determine_season_for_cup(cup_id)
            
            if not season_to_use:
                self.logger.warning(f"无法为 cup_id '{cup_id}' 确定有效的赛季，跳过请求其详情。")
                continue
                
            # Retrieve basic info stored from infoHeader.js parsing
            basic_cup_info = self.cup_basic_info.get(cup_id)
            if not basic_cup_info:
                self.logger.error(f"在 self.cup_basic_info 中找不到 cup_id '{cup_id}' 的基本信息，无法请求详情。")
                continue

            continent_id_to_pass = basic_cup_info.get('continent_id')
            country_id_to_pass = basic_cup_info.get('country_id')

            # Construct the URL for the cup's JS details file (e.g., c103.js for cup_id 103)
            # URL_Cup_Details = f"https://zq.titan007.com/jsData/CupData/{season_to_use}/c{cup_id}.js?{version}"
            # The source seems to be /jsData/CupMatchAll/{season_prefix}/c{CupID}.js
            # Example: https://zq.titan007.com/jsData/CupMatchAll/2024/c103.js (season is YYYY, not YYYY-YYYY)
            # Let's assume season_to_use is in YYYY-YYYY format from _determine_season_for_cup.
            # We need to extract the starting year for the URL.
            
            # season_start_year = season_to_use.split('-')[0] if '-' in season_to_use else season_to_use # OLD LOGIC
            # cup_detail_url = f"https://zq.titan007.com/jsData/CupMatchAll/{season_start_year}/c{cup_id}.js?{version}" # OLD URL
            
            # New URL structure based on user's screenshot:
            # https://zq.titan007.com/jsData/matchResult/YYYY-YYYY/c{CupID}.js
            # season_to_use should already be in the correct "YYYY-YYYY" or "YYYY" format.
            cup_detail_url = f"https://zq.titan007.com/jsData/matchResult/{season_to_use}/c{cup_id}.js?{version}"
            
            self.logger.info(f"请求杯赛详情: CupID={cup_id}, SeasonToUse={season_to_use}, URL={cup_detail_url}")
            requests_made +=1
            yield scrapy.Request(
                url=cup_detail_url,
                callback=self.parse_cup_details,
                meta={
                    'cup_id': cup_id,
                    'season_used_for_request': season_to_use, # The YYYY-YYYY season string we decided to use
                    'continent_id': continent_id_to_pass,    # Pass continent_id
                    'country_id': country_id_to_pass          # Pass country_id
                },
                headers={"User-Agent": self.get_random_user_agent(), "Referer": "https://zq.titan007.com/"}
            )
            
        if requests_made == 0:
            self.logger.info("没有为任何杯赛生成详情请求 (可能是因为所有目标杯赛都无法确定赛季)。")

    def parse_cup_details(self, response):
        """Parse the detailed cup JS file (e.g., c283.js for a cup)"""
        meta = response.meta
        cup_id = meta['cup_id'] 
        request_url = response.url
        season_actually_used_for_request = meta.get('season_used_for_request') 
        # Get continent_id and country_id from meta
        continent_id_from_meta = meta.get('continent_id')
        country_id_from_meta = meta.get('country_id')
        
        self.logger.info(f"解析杯赛详情: URL={request_url}, CupID={cup_id}, SeasonUsedForRequest={season_actually_used_for_request}, ContinentID={continent_id_from_meta}, CountryID={country_id_from_meta}")

        js_content = response.text
        
        # Initialize item with fallback data first from meta and infoHeader cache
        item = CupItem()
        item['cup_id'] = cup_id
        item['continent_id'] = continent_id_from_meta
        item['country_id'] = country_id_from_meta
        item['created_at'] = datetime.now(pytz.utc)
        item['updated_at'] = datetime.now(pytz.utc)

        basic_cup_info_for_this_id = self.cup_basic_info.get(cup_id, {})
        item['match_type'] = basic_cup_info_for_this_id.get('match_type', '杯赛') # Get the Chinese string, default to it if not found
        item['available_seasons'] = basic_cup_info_for_this_id.get('available_seasons', [])
        item['cup_short_name'] = basic_cup_info_for_this_id.get('cup_short_name', f'Cup {cup_id}')
        item['current_season'] = season_actually_used_for_request # Default to requested season
        item['cup_official_name_simp'] = basic_cup_info_for_this_id.get('cup_short_name') # Fallback

        if response.status == 404:
            self.logger.warning(f"杯赛详情文件未找到 (404): {request_url}. 将仅保存基本信息。")
            yield item
            return
            
        arr_cup_str = None
        arr_cup_data = None

        try:
            # Programmatic extraction of arrCup content
            assignment_pattern = r"var\s*arrCup\s*=\s*"
            
            # <<< NEW DEBUGGING CODE START >>>
            self.logger.info(f"DEBUG: Type of js_content: {type(js_content)}")
            test_prefix = "var arrCup ="
            if js_content.strip().lower().startswith(test_prefix.lower()):
                self.logger.info(f"DEBUG: js_content STARTS WITH (case-insensitive, stripped) '{test_prefix}'")
            else:
                self.logger.info(f"DEBUG: js_content DOES NOT START WITH (case-insensitive, stripped) '{test_prefix}'. Actual start: '{js_content[:len(test_prefix)+20]}'")
            # <<< NEW DEBUGGING CODE END >>>
            
            match = re.search(assignment_pattern, js_content, re.IGNORECASE)

            if match:
                start_after_assignment = match.end()
                first_bracket_index = js_content.find('[', start_after_assignment)

                if first_bracket_index != -1:
                    depth = 0
                    content_end_index = -1
                    for i in range(first_bracket_index, len(js_content)):
                        if js_content[i] == '[':
                            depth += 1
                        elif js_content[i] == ']':
                            depth -= 1
                            if depth == 0:
                                content_end_index = i + 1  # Include the closing bracket
                                arr_cup_str = js_content[first_bracket_index:content_end_index]
                                break
                    if not arr_cup_str:
                        self.logger.error(f"未能找到 arrCup 的匹配闭合方括号: CupID={cup_id}, URL={request_url}. 开方括号位置: {first_bracket_index}")
                else:
                    self.logger.error(f"在 'var arrCup =' 声明后未能找到开方括号 '[': CupID={cup_id}, URL={request_url}")
            else:
                self.logger.error(f"未能找到 'var arrCup =' 声明: CupID={cup_id}, URL={request_url}. JS content prefix (first 500 chars): {js_content[:500]}")
                # Add more detailed logging about the content around the expected pattern
                var_idx = js_content.lower().find('var')
                arrCup_idx = js_content.lower().find('arrcup')
                eq_idx = js_content.lower().find('=')
                self.logger.info(f"详细检查: 'var' found at {var_idx}, 'arrCup' found at {arrCup_idx}, '=' found at {eq_idx}.")
                if var_idx != -1 and arrCup_idx != -1 and var_idx < arrCup_idx:
                    self.logger.info(f"Content between 'var' and 'arrCup': '{js_content[var_idx + 3 : arrCup_idx]}'")
                if arrCup_idx != -1 and eq_idx != -1 and arrCup_idx < eq_idx:
                    self.logger.info(f"Content between 'arrCup' and '=': '{js_content[arrCup_idx + 6 : eq_idx]}'")

            if arr_cup_str:
                self.logger.info(f"成功提取 arrCup 字符串片段 (CupID: {cup_id}). 准备解析...")
                arr_cup_str_cleaned = arr_cup_str.replace('undefined', 'None')
                arr_cup_data = ast.literal_eval(arr_cup_str_cleaned)

                if isinstance(arr_cup_data, list) and len(arr_cup_data) > 10:
                    self.logger.info(f"成功解析 arrCup 数据 (CupID: {cup_id}). 更新 Item...")
                    item['cup_official_name_simp'] = arr_cup_data[1]
                    item['cup_official_name_cant'] = arr_cup_data[2]
                    item['cup_official_name_en'] = arr_cup_data[3]
                    item['cup_short_name'] = arr_cup_data[4]
                    item['current_season'] = arr_cup_data[7] # OVERWRITE with season from the file
                    logo_path = arr_cup_data[8]
                    if logo_path:
                        # 拼接完整URL
                        if logo_path.startswith('http'):
                            logo_url = logo_path
                        else:
                            logo_url = urljoin(self.CUP_LOGO_BASE_URL, logo_path)
                        item['cup_logo'] = self.download_logo(logo_url, cup_id=cup_id)
                    else:
                        item['cup_logo'] = None
                    if season_actually_used_for_request and season_actually_used_for_request != item['current_season']:
                        self.logger.warning(f"Season mismatch for cup {cup_id}: requested_season={season_actually_used_for_request}, season_in_file={item['current_season']}. Using season_in_file.")
                else:
                    self.logger.error(f"arrCup 数据解析成功但格式不符合预期 (非列表或长度不足): CupID={cup_id}, Data: {str(arr_cup_data)[:200]}")
            # If arr_cup_str is None, item remains populated with basic_info

        except (SyntaxError, ValueError, TypeError, IndexError) as e_eval:
            self.logger.error(f"解析 arrCup 内容时发生错误 (CupID: {cup_id}): {e_eval}. arr_cup_str (前200字符): {str(arr_cup_str)[:200]}", exc_info=False) # exc_info=False to reduce noise
        except Exception as e_generic:
            self.logger.error(f"处理 arrCup 时发生未知错误 (CupID: {cup_id}): {e_generic}", exc_info=True)
        
        yield item

    def download_logo(self, url, cup_id=None):
        """
        下载杯赛logo到本地static/images/logos/cup/{cup_id}/xxx.png，并返回相对路径。
        """
        try:
            if not url or cup_id is None:
                self.logger.warning(f"下载logo时URL为空或cup_id未提供: URL={url}, CupID={cup_id}")
                return None
            parsed_url = urlparse(url)
            path = parsed_url.path
            file_name = os.path.basename(path)
            if '.' not in file_name:
                file_name += '.png'
            target_cup_dir = os.path.join(self.LOCAL_LOGO_PATH, str(cup_id))
            os.makedirs(target_cup_dir, exist_ok=True)
            local_file_path = os.path.join(target_cup_dir, file_name)
            if not os.path.exists(local_file_path):
                self.logger.info(f"正在下载 Logo: {url} 到 {local_file_path}")
                try:
                    headers = {
                        'User-Agent': self.get_random_user_agent(),
                        'Referer': 'https://zq.titan007.com/'
                    }
                    response = requests.get(url, stream=True, timeout=10, headers=headers)
                    response.raise_for_status()
                    with open(local_file_path, 'wb') as f:
                        for chunk in response.iter_content(8192):
                            f.write(chunk)
                    self.logger.info(f"Logo 下载成功: {local_file_path}")
                except requests.exceptions.RequestException as req_e:
                    self.logger.error(f"下载 Logo 时发生请求错误: {url}, Error: {req_e}")
                    return None
                except IOError as io_e:
                    self.logger.error(f"写入 Logo 文件时出错: {local_file_path}, Error: {io_e}")
                    if os.path.exists(local_file_path):
                        try: os.remove(local_file_path)
                        except OSError: pass
                    return None
            else:
                self.logger.debug(f"Logo 已存在，跳过下载: {local_file_path}")
            return f"/static/images/logos/cup/{cup_id}/{file_name}"
        except Exception as e:
            self.logger.error(f"处理 Logo 下载时发生意外错误: {url}, CupID: {cup_id}, Error: {str(e)}", exc_info=True)
            return None

    def errback_handler(self, failure):
        self.logger.error(f"CupSpider 请求失败: {failure.request.url}, Meta: {failure.request.meta}, Error: {failure.value}")

    def closed(self, reason):
        self.logger.info(f"CupSpider 已关闭: {reason}") 