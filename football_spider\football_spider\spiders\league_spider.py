import scrapy
import json
import re
import os
import logging
import time
import requests
from datetime import datetime, timezone
from urllib.parse import urlparse
from football_spider.items import LeagueItem
from ..sub_league_mappings import SUB_LEAGUE_ID_MAP

class LeagueSpider(scrapy.Spider):
    name = 'league'
    allowed_domains = ['titan007.com', 'zq.titan007.com']
    
    # LOGO基础URL常量
    LEAGUE_LOGO_BASE_URL = 'https://zq.titan007.com/Image/'
    
    # 本地存储路径
    LOCAL_LOGO_PATH = '../../../oddluck_dev/backend/static/images/logos/league'
    LOCAL_LOGO_URL = '/static/images/logos/league'
    
    custom_settings = {
        'ITEM_PIPELINES': {
            'football_spider.incremental_pipeline.IncrementalUpdatePipeline': 200,
            'football_spider.pipelines.LeaguePipeline': 300,
        },
        'DOWNLOAD_DELAY': 1,
        'CONCURRENT_REQUESTS': 1,
        'LOG_FILE': '../spiders_log/league_spider.log',
        'LOG_LEVEL': 'INFO',
    }
    
    def __init__(self, league=None, season=None, task_id=None, *args, **kwargs):
        super(LeagueSpider, self).__init__(*args, **kwargs)
        self.task_id = task_id
        
        # Store user's league filter preference
        self.user_league_filter = league
            
        self.season = season  # 如果用户指定了season，就使用用户指定的
        
        # Store extracted basic league info from infoHeader.js for ALL found leagues
        self.league_basic_info = {}
        # Target league IDs will be determined after parsing infoHeader.js
        self.target_league_ids = []
        
        self.logger.info(f"初始化LeagueSpider: 用户指定联赛='{self.user_league_filter}', 赛季={self.season}")
    
    def normalize_season(self, season):
        """
        标准化赛季格式
        如果赛季是单个年份（如'2025'），将其转换为'2024-2025'格式
        如果已经是'2024-2025'格式，则保持不变
        """
        if not season:
            return None
            
        # 如果已经是YYYY-YYYY格式，直接返回
        if '-' in season:
            return season
            
        # 如果是单个年份，转换为YYYY-YYYY格式
        try:
            year = int(season)
            return f"{year-1}-{year}"
        except ValueError:
            self.logger.warning(f"无法解析赛季格式: {season}")
            return None
    
    def get_random_user_agent(self):
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
        ]
        return user_agents[int(time.time()) % len(user_agents)]
    
    def start_requests(self):
        """Start requests by fetching infoHeader.js first"""
        # Target validation will happen after infoHeader.js is parsed
        self.logger.info("开始请求 infoHeader.js 以发现联赛...")
        yield scrapy.Request(
            url='https://zq.titan007.com/jsData/infoHeader.js',
            callback=self.parse_infoheader,
            headers={'User-Agent': self.get_random_user_agent()},
            meta={'dont_redirect': True, 'handle_httpstatus_list': [302]},
            errback=self.errback_handler
        )
    
    def parse_infoheader(self, response):
        """Parse infoHeader.js to get basic league info and available seasons."""
        self.logger.info("正在解析 infoHeader.js")
        try:
            js_content = response.text
            self.logger.debug(f"收到的 infoHeader.js 内容 (前1000字符): {js_content[:1000]}")
            
            # --- Start: Replace Regex with String Manipulation ---
            all_countries_data = []
            lines = js_content.splitlines() # Split content into lines
            
            for line in lines:
                line = line.strip() # Remove leading/trailing whitespace
                if line.startswith('arr[') and ' = [' in line and line.endswith('];'):
                    try:
                        # Find the start of the list '[' and the end ']' (last occurrence)
                        start_index = line.find(' = [') + 3 # Index after ' = ['
                        end_index = line.rfind(']')
                        
                        if start_index < end_index:
                            match_str = line[start_index : end_index + 1] # Extract the list string [...]
                            self.logger.debug(f"通过字符串处理找到潜在的国家数据: {match_str[:100]}...")
                            
                            country_data_list = None
                            # Attempt to parse the extracted string
                            try: # Outer try for parsing one country entry
                                try:
                                    cleaned_match_str = match_str.replace("\'", '"')
                                    country_data_list = json.loads(cleaned_match_str)
                                except json.JSONDecodeError:
                                    self.logger.debug(f"直接JSON解析失败 (国家数据: {match_str[:100]}...)，尝试eval。")
                                    try:
                                        safe_globals = {"__builtins__": {"list": list, "True": True, "False": False, "None": None}}
                                        country_data_list = eval(match_str, safe_globals, {"list": list})
                                        self.logger.warning(f"使用 eval 解析 infoHeader.js 中的国家数据，请确保数据源可信 (数据片段: {match_str[:100]}...)")
                                    except Exception as e_eval:
                                        self.logger.error(f"使用 eval 解析国家数据失败: {e_eval}, 数据: {match_str[:200]}...", exc_info=True)
                                        continue # Skip this entry if eval fails
                                     
                                if country_data_list and isinstance(country_data_list, list):
                                    all_countries_data.append(country_data_list)
                                else:
                                    self.logger.error(f"解析后的国家数据不是有效列表: {match_str[:100]}... 类型: {type(country_data_list)}")

                            except Exception as e_outer_parse: # Catch any unexpected error during processing this entry
                                self.logger.error(f"处理单个国家数据块时发生意外错误: {e_outer_parse}, 数据: {match_str[:200]}...", exc_info=True)
                                continue # Skip to the next line
                        else:
                             self.logger.debug(f"在行内未找到有效的列表起始/结束索引: {line[:100]}...")
                    except Exception as e_line_proc:
                         self.logger.error(f"处理行时出错: '{line[:100]}...', 错误: {e_line_proc}", exc_info=True)
            # --- End: Replace Regex with String Manipulation ---

            # Check if any data was successfully extracted
            if not all_countries_data:
                self.logger.error("无法通过字符串处理从 infoHeader.js 提取任何有效的国家数据。")
                return

            # Now iterate over all_countries_data (populated by string manipulation)
            self.league_basic_info = {} # Reset before populating
            for country_info in all_countries_data:
                if not isinstance(country_info, list) or len(country_info) < 5:
                    self.logger.debug(f"跳过格式不正确的国家数据: {country_info}")
                    continue
                   
                country_id_str = country_info[0] # e.g., "InfoID_1"
                country_id = country_id_str.split('_')[1] if '_' in country_id_str else country_id_str
                continent_id = country_info[3]
                leagues_data = country_info[4]
                
                for league_detail_str in leagues_data:
                    parts = league_detail_str.split(',')
                    if len(parts) < 5: continue
                    
                    league_id = parts[0].strip()
                    league_short_name = parts[1].strip()
                    league_type = f"{parts[2].strip()},{parts[3].strip()}"
                    # Filter only league types ('1,0' or '1,1')
                    if league_type not in ['1,0', '1,1']:
                        continue
                    
                    # Extract available seasons from the remaining parts
                    available_seasons = [season.strip() for season in parts[4:] if season.strip()] # Ensure no empty strings

                    # Store info for all found leagues
                    self.league_basic_info[league_id] = {
                        'league_short_name': league_short_name,
                        'league_type': league_type,
                        'available_seasons': ",".join(available_seasons), # Use the defined variable
                        'continent_id': continent_id,
                        'country_id': country_id # Store country_id derived from country_info[0]
                    }
                    self.logger.debug(f"在 infoHeader.js 中发现联赛: ID={league_id}, Name={league_short_name}, Type={league_type}")
            
            # Determine target_league_ids based on user_league_filter and populated league_basic_info
            if self.user_league_filter:
                if self.user_league_filter.isdigit(): # User provided an ID
                    if self.user_league_filter in self.league_basic_info:
                        self.target_league_ids = [self.user_league_filter]
                        self.logger.info(f"目标联赛ID已指定并找到: {self.user_league_filter}")
                    else:
                        self.logger.warning(f"用户指定的联赛ID '{self.user_league_filter}' 未在 infoHeader.js 中找到。")
                else: # User provided a name
                    found_by_name = False
                    for l_id, l_info in self.league_basic_info.items():
                        # Check against short name from infoHeader
                        if l_info['league_short_name'] == self.user_league_filter:
                            self.target_league_ids.append(l_id)
                            found_by_name = True
                    if found_by_name:
                         self.logger.info(f"通过名称 '{self.user_league_filter}' 找到目标联赛ID: {self.target_league_ids}")
                    else:
                        self.logger.warning(f"用户指定的联赛名称 '{self.user_league_filter}' 未在 infoHeader.js 中找到。")
            else: # No specific league filter, target all found leagues
                self.target_league_ids = list(self.league_basic_info.keys())
                self.logger.info(f"未指定特定联赛，将爬取所有在 infoHeader.js 中发现的 {len(self.target_league_ids)} 个联赛。")

            if not self.target_league_ids:
                self.logger.warning("没有有效的联赛目标被确定。爬虫将停止。")
                return

            # Log the final list of target league IDs that will be processed
            self.logger.info(f"最终确定的目标联赛ID列表: {self.target_league_ids}")

            # Use yield from to correctly delegate request generation
            yield from self.request_league_details()
            
        except Exception as e: # General exception handler for parse_infoheader
            self.logger.error(f"解析 infoHeader.js 时发生意外错误: {str(e)}", exc_info=True)

    def request_league_details(self):
        """Request detailed info for each target league."""
        version = time.strftime("%Y%m%d%H", time.localtime())
        requests_made = 0
        
        for league_id in self.target_league_ids:
            if league_id not in self.league_basic_info:
                self.logger.warning(f"跳过请求联赛详情，因为未在 infoHeader.js 中找到基本信息: {league_id}")
                continue
                
            # 获取联赛的基本信息
            league_info = self.league_basic_info[league_id]
            # 从available_seasons中获取第一个赛季（最新赛季）
            available_seasons = league_info.get('available_seasons', '')
            if not available_seasons:
                self.logger.warning(f"联赛 {league_id} 没有可用的赛季信息")
                continue
                
            # 如果用户指定了赛季，就使用用户指定的赛季
            season_for_url = self.season if self.season else available_seasons.split(',')[0]
            self.logger.info(f"联赛 {league_id} 使用赛季: {season_for_url}")
                
            # 使用导入的 SUB_LEAGUE_ID_MAP
            sub_league_id_list = SUB_LEAGUE_ID_MAP.get(league_id) # 这会返回一个列表或 None
            
            requested_for_league = False
            if sub_league_id_list: # 如果找到了子联赛ID列表并且列表不为空
                for sub_id in sub_league_id_list:
                    # 为每个 sub_id 构建URL并发起请求
                    url = f'https://zq.titan007.com/jsData/matchResult/{season_for_url}/s{league_id}_{sub_id}.js?version={version}'
                    self.logger.info(f"准备请求子联赛详情: {url}")
                    yield scrapy.Request(
                        url,
                        callback=self.parse_league_details,
                        meta={
                            'league_id': league_id,
                            'sub_league_id': sub_id, # 传递当前的 sub_id
                            'is_sub_league_type_url': True,
                            'season_for_url': season_for_url # 传递当前使用的赛季
                        },
                        errback=self.handle_error_for_league_details
                    )
                    requests_made += 1
                    requested_for_league = True
            
            if not requested_for_league: # 如果没有子联赛ID或列表为空，则请求主联赛
                url = f'https://zq.titan007.com/jsData/matchResult/{season_for_url}/s{league_id}.js?version={version}'
                self.logger.info(f"准备请求主联赛详情: {url}")
                yield scrapy.Request(
                    url,
                    callback=self.parse_league_details,
                    meta={
                        'league_id': league_id,
                        'sub_league_id': None, # 主联赛没有 sub_league_id
                        'is_sub_league_type_url': False,
                        'season_for_url': season_for_url # 传递当前使用的赛季
                    },
                    errback=self.handle_error_for_league_details
                )
                requests_made += 1
        
        if requests_made == 0 and self.target_league_ids:
            self.logger.info("没有为任何目标联赛生成请求。")
        elif requests_made > 0:
            self.logger.info(f"总共为联赛详情生成了 {requests_made} 个请求。")

    def parse_league_details(self, response):
        """Parse the detailed league JS file (e.g., s36.js or s34_2948.js)"""
        try:
            request_url = response.url
            self.logger.info(f"开始解析联赛详情: {request_url}")
            
            # 从meta中获取season_for_url
            season_for_url = response.meta.get('season_for_url', self.season)
            
            # Extract league_id and sub_league_id from URL
            match = re.search(r's(\d+)(?:_(\d+))?\.js', request_url)
            if not match:
                self.logger.error(f"无法从URL解析联赛ID: {request_url}")
                return
                
            league_id = match.group(1)
            sub_league_id = match.group(2)
            
            # Get effective sub_league_id
            effective_sub_league_id = sub_league_id if sub_league_id else None
            
            if response.status == 404:
                self.logger.warning(f"联赛详情文件未找到 (404): {request_url}")
                if league_id in self.league_basic_info:
                    basic_info = self.league_basic_info[league_id]
                    current_utc_time = datetime.now(timezone.utc)
                    
                    original_league_type = basic_info.get('league_type')
                    transformed_league_type = original_league_type
                    if original_league_type == '1,0':
                        transformed_league_type = '主联赛'
                    elif original_league_type == '1,1':
                        transformed_league_type = '子联赛'

                    item = LeagueItem(
                        league_id=league_id,
                        sub_league_id=effective_sub_league_id,
                        league_type=transformed_league_type,
                        league_short_name=basic_info.get('league_short_name'),
                        available_seasons=basic_info.get('available_seasons'),
                        current_season=season_for_url, # 使用从meta中获取的season_for_url
                        continent_id=basic_info.get('continent_id'),
                        country_id=basic_info.get('country_id'),
                        task_id=self.task_id,
                        created_at=current_utc_time,
                        updated_at=current_utc_time
                    )
                    yield item
                return

            league_data_str = None
            league_details_list = None
            try:
                js_content = response.text
                # Log the beginning of the JS content BEFORE trying to parse arrLeague
                self.logger.debug(f"League details JS content (first 500 chars) BEFORE arrLeague regex: {js_content[:500]}")
                
                league_info_match = re.search(r'var\s+arrLeague\s*=\s*(\[.*?\]);', js_content, re.DOTALL)
                if not league_info_match:
                    self.logger.error(f"在 {request_url} 中未找到 arrLeague 数据")
                    if league_id in self.league_basic_info:
                         basic_info = self.league_basic_info[league_id]
                         current_utc_time = datetime.now(timezone.utc)

                         original_league_type = basic_info.get('league_type')
                         transformed_league_type = original_league_type
                         if original_league_type == '1,0':
                             transformed_league_type = '主联赛'
                         elif original_league_type == '1,1':
                             transformed_league_type = '子联赛'

                         item_data = {
                             'league_id': league_id,
                             'sub_league_id': effective_sub_league_id,
                             'country_id': basic_info.get('country_id'),
                             'continent_id': basic_info.get('continent_id'),
                             'league_type': transformed_league_type,
                             'available_seasons': basic_info.get('available_seasons'),
                             'league_short_name': basic_info.get('league_short_name'),
                             'league_official_name_simp': None,
                             'league_official_name_cant': None,
                             'league_official_name_en': None,
                             'current_season': self.normalize_season(self.season), 
                             'league_logo_path': None,
                             'league_logo': None,
                             'total_rounds': None,
                             'current_round': None,
                             'task_id': self.task_id,
                             'created_at': current_utc_time,
                             'updated_at': current_utc_time
                         }
                         item = LeagueItem(**item_data)
                         self.logger.info(f"由于未找到 arrLeague，创建了基础 LeagueItem: {league_id}")
                         yield item
                    return
                    
                league_data_str = league_info_match.group(1)
                try:
                    league_details_list_eval = eval(league_data_str, {"__builtins__": {}}, {"list": list, "true": True, "false": False, "null": None})
                    
                    processed_league_details = None
                    if league_details_list_eval and isinstance(league_details_list_eval, list) and len(league_details_list_eval) > 0:
                        if isinstance(league_details_list_eval[0], list):
                            # Case: [[details_list, ...]] - arrLeague is a list of lists
                            processed_league_details = league_details_list_eval[0]
                        else:
                            # Case: [detail1, detail2, ...] - arrLeague is a single list of details
                            processed_league_details = league_details_list_eval
                    
                    # Check if processed_league_details is now a valid list of details
                    if not processed_league_details or not isinstance(processed_league_details, list) or len(processed_league_details) < 9:
                        log_msg = (f"arrLeague data format incorrect or incomplete after processing: {request_url}. "
                                   f"Raw eval data (type {type(league_details_list_eval).__name__}): {str(league_details_list_eval)[:150]}. "
                                   f"Processed list for item (type {type(processed_league_details).__name__}): {str(processed_league_details)[:150]}.")
                        self.logger.error(log_msg)
                        if league_id in self.league_basic_info:
                            basic_info = self.league_basic_info[league_id]
                            current_utc_time = datetime.now(timezone.utc)
                            
                            original_league_type = basic_info.get('league_type')
                            transformed_league_type = original_league_type
                            if original_league_type == '1,0':
                                transformed_league_type = '主联赛'
                            elif original_league_type == '1,1':
                                transformed_league_type = '子联赛'

                            item_data = {
                                'league_id': league_id,
                                'sub_league_id': effective_sub_league_id,
                                'country_id': basic_info.get('country_id'),
                                'continent_id': basic_info.get('continent_id'),
                                'league_type': transformed_league_type,
                                'available_seasons': basic_info.get('available_seasons'),
                                'league_short_name': basic_info.get('league_short_name'),
                                'league_official_name_simp': None,
                                'league_official_name_cant': None,
                                'league_official_name_en': None,
                                'current_season': self.normalize_season(self.season),
                                'league_logo_path': None,
                                'league_logo': None,
                                'total_rounds': None,
                                'current_round': None,
                                'task_id': self.task_id,
                                'created_at': current_utc_time,
                                'updated_at': current_utc_time
                            }
                            item = LeagueItem(**item_data)
                            self.logger.info(f"Yielding fallback LeagueItem due to arrLeague parsing issue for league_id: {league_id}")
                            yield item
                        return
                     
                    league_details = processed_league_details # This is now the single list of details.

                except Exception as e_eval:
                     self.logger.error(f"解析 arrLeague 数据时出错 (eval or processing exception): {e_eval} in {request_url}. Data: {league_data_str[:200]}", exc_info=True)
                     if league_id in self.league_basic_info:
                         basic_info = self.league_basic_info[league_id]
                         current_utc_time = datetime.now(timezone.utc)

                         original_league_type = basic_info.get('league_type')
                         transformed_league_type = original_league_type
                         if original_league_type == '1,0':
                             transformed_league_type = '主联赛'
                         elif original_league_type == '1,1':
                             transformed_league_type = '子联赛'

                         item_data = {
                             'league_id': league_id,
                             'sub_league_id': effective_sub_league_id,
                             'country_id': basic_info.get('country_id'),
                             'continent_id': basic_info.get('continent_id'),
                             'league_type': transformed_league_type,
                             'available_seasons': basic_info.get('available_seasons'),
                             'league_short_name': basic_info.get('league_short_name'),
                             'league_official_name_simp': None,
                             'league_official_name_cant': None,
                             'league_official_name_en': None,
                             'current_season': self.normalize_season(self.season),
                             'league_logo_path': None,
                             'league_logo': None,
                             'total_rounds': None,
                             'current_round': None,
                             'task_id': self.task_id,
                             'created_at': current_utc_time,
                             'updated_at': current_utc_time
                         }
                         item = LeagueItem(**item_data)
                         self.logger.info(f"Yielding fallback LeagueItem due to arrLeague eval/processing exception for league_id: {league_id}")
                         yield item
                     return

                basic_info = self.league_basic_info.get(league_id, {})
                
                current_utc_time = datetime.now(timezone.utc)
                
                original_league_type_from_basic = basic_info.get('league_type')
                transformed_league_type_for_item = original_league_type_from_basic
                if original_league_type_from_basic == '1,0':
                    transformed_league_type_for_item = '主联赛'
                elif original_league_type_from_basic == '1,1':
                    transformed_league_type_for_item = '子联赛'

                # ========== 新增：处理主联赛与子联赛轮次 ===========
                total_rounds = None
                current_round = None
                if effective_sub_league_id is None:
                    # 主联赛，依然从arrLeague获取
                    if len(league_details) > 7 and str(league_details[7]).isdigit():
                        total_rounds = int(league_details[7])
                    if len(league_details) > 8 and str(league_details[8]).isdigit():
                        current_round = int(league_details[8])
                else:
                    # 子联赛，优先从arrSubLeague获取
                    arr_sub_league_match = re.search(r'var\s+arrSubLeague\s*=\s*(\[.*?\]);', js_content, re.DOTALL)
                    arr_sub_league_list = None
                    if arr_sub_league_match:
                        arr_sub_league_str = arr_sub_league_match.group(1)
                        try:
                            arr_sub_league_list = eval(arr_sub_league_str, {"__builtins__": {}}, {"list": list, "true": True, "false": False, "null": None})
                        except Exception as e:
                            self.logger.warning(f"解析arrSubLeague失败: {e}")
                    if arr_sub_league_list and isinstance(arr_sub_league_list, list):
                        # 多个子联赛时，精确匹配sub_league_id
                        found = False
                        for sub in arr_sub_league_list:
                            if str(sub[0]) == str(effective_sub_league_id):
                                if len(sub) > 5 and str(sub[5]).isdigit():
                                    total_rounds = int(sub[5])
                                if len(sub) > 6 and str(sub[6]).isdigit():
                                    current_round = int(sub[6])
                                found = True
                                break
                    if not found and len(arr_sub_league_list) == 1:
                        # 只有一个子联赛时，直接取第0个
                        sub = arr_sub_league_list[0]
                        if len(sub) > 5 and str(sub[5]).isdigit():
                            total_rounds = int(sub[5])
                        if len(sub) > 6 and str(sub[6]).isdigit():
                            current_round = int(sub[6])
                    else:
                        # arrSubLeague不存在或解析失败，兜底用arrLeague
                        if len(league_details) > 7 and str(league_details[7]).isdigit():
                            total_rounds = int(league_details[7])
                        if len(league_details) > 8 and str(league_details[8]).isdigit():
                            current_round = int(league_details[8])
                # ========== END ===========

                # 在解析联赛详情时使用normalize_season
                item = LeagueItem(
                    league_id=league_id,
                    sub_league_id=effective_sub_league_id,
                    league_type=transformed_league_type_for_item, 
                    league_short_name=basic_info.get('league_short_name', league_details[1] if len(league_details) > 1 else None),
                    available_seasons=basic_info.get('available_seasons'),
                    league_official_name_simp=league_details[1] if len(league_details) > 1 else None,
                    league_official_name_cant=league_details[2] if len(league_details) > 2 else None,
                    league_official_name_en=league_details[3] if len(league_details) > 3 else None,
                    current_season=self.normalize_season(league_details[4] if len(league_details) > 4 else self.season),
                    league_logo_path=league_details[6] if len(league_details) > 6 else None,
                    total_rounds=total_rounds,
                    current_round=current_round,
                    continent_id=basic_info.get('continent_id'),
                    country_id=basic_info.get('country_id'),
                    task_id=self.task_id,
                    created_at=current_utc_time,
                    updated_at=current_utc_time
                )
                
                if item['league_logo_path']:
                     logo_full_url = self.LEAGUE_LOGO_BASE_URL + item['league_logo_path']
                     item['league_logo'] = self.download_logo(logo_full_url, logo_type='league', league_id=league_id)
                    
                if item['current_season'] != self.normalize_season(self.season):
                     self.logger.warning(f"文件中的赛季 ({item['current_season']}) 与请求的赛季 ({self.normalize_season(self.season)}) 不匹配: {request_url}. 使用文件中的赛季。")

                yield item
                
            except Exception as e:
                self.logger.error(f"解析联赛详情时发生意外错误: {str(e)} in {request_url}", exc_info=True)
                if league_id in self.league_basic_info:
                    basic_info = self.league_basic_info[league_id]
                    current_utc_time = datetime.now(timezone.utc)

                    original_league_type = basic_info.get('league_type')
                    transformed_league_type = original_league_type
                    if original_league_type == '1,0':
                        transformed_league_type = '主联赛'
                    elif original_league_type == '1,1':
                        transformed_league_type = '子联赛'

                    fallback_item = LeagueItem(
                        league_id=league_id,
                        sub_league_id=effective_sub_league_id,
                        league_type=transformed_league_type,
                        league_short_name=basic_info.get('league_short_name'),
                        available_seasons=basic_info.get('available_seasons'),
                        current_season=self.normalize_season(self.season),
                        continent_id=basic_info.get('continent_id'),
                        country_id=basic_info.get('country_id'),
                        task_id=self.task_id, # Ensure task_id is included
                        created_at=current_utc_time,
                        updated_at=current_utc_time
                    )
                    self.logger.info(f"在发生错误后，尝试生成基础 LeagueItem: {league_id}")
                    yield fallback_item
                
        except Exception as e:
            self.logger.error(f"解析联赛详情时发生意外错误: {str(e)} in {request_url}", exc_info=True)

    def download_logo(self, url, logo_type='league', league_id=None):
        """Download logo and store locally."""
        try:
            if not url or league_id is None: # Ensure league_id is provided for directory structure
                self.logger.warning(f"下载logo时URL为空或league_id未提供: URL={url}, LeagueID={league_id}")
                return None
            
            parsed_url = urlparse(url)
            path = parsed_url.path
            if path.startswith('/') and self.LEAGUE_LOGO_BASE_URL.endswith('/'):
                 path = path[1:]
            
            file_name = os.path.basename(path)
            if '.' not in file_name:
                 file_name += '.png'
            
            # Create a specific directory for the league_id if it doesn't exist
            target_league_dir = os.path.join(self.LOCAL_LOGO_PATH, str(league_id))
            os.makedirs(target_league_dir, exist_ok=True)
            
            local_file_path = os.path.join(target_league_dir, file_name)
            
            if not os.path.exists(local_file_path):
                 self.logger.info(f"正在下载 Logo: {url} 到 {local_file_path}")
                 try:
                     headers = {
                         'User-Agent': self.get_random_user_agent(),
                         'Referer': 'https://zq.titan007.com/' 
                     }
                     response = requests.get(url, stream=True, timeout=10, headers=headers)
                     response.raise_for_status()
                     with open(local_file_path, 'wb') as f:
                         for chunk in response.iter_content(8192):
                             f.write(chunk)
                     self.logger.info(f"Logo 下载成功: {local_file_path}")
                 except requests.exceptions.RequestException as req_e:
                     self.logger.error(f"下载 Logo 时发生请求错误: {url}, Error: {req_e}")
                     return None
                 except IOError as io_e:
                     self.logger.error(f"写入 Logo 文件时出错: {local_file_path}, Error: {io_e}")
                     if os.path.exists(local_file_path):
                          try: os.remove(local_file_path)
                          except OSError: pass
                     return None
            else:
                 self.logger.debug(f"Logo 已存在，跳过下载: {local_file_path}")
            
            return f"{self.LOCAL_LOGO_URL}/{league_id}/{file_name}"
            
        except Exception as e:
            self.logger.error(f"处理 Logo 下载时发生意外错误: {url}, LeagueID: {league_id}, Error: {str(e)}", exc_info=True)
            return None
    
    def errback_handler(self, failure):
        self.logger.error(f"请求失败: {failure.request.url}, Meta: {failure.request.meta}, Error: {failure.value}")

    def closed(self, reason):
        self.logger.info(f"LeagueSpider 已关闭: {reason}")
        # Log summary if needed 

    def handle_error_for_league_details(self, failure):
        # 基本的错误处理，可以根据需要扩展
        self.logger.error(f"请求联赛详情失败: {failure.request.url}, 错误: {failure.value}")
        # 可以在这里决定是否重试，或者记录到特定的失败列表等 