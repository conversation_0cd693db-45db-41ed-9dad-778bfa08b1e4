import scrapy
import json
import re
import time
import random
from datetime import datetime
from urllib.parse import urljoin, urlparse
from ..items import Match<PERSON><PERSON>, Odds<PERSON>tem, TeamItem
from scrapy import Spider, Request
import logging
import os
import sys
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
# from webdriver_manager.chrome import ChromeDriverManager
import ast
import pytz
from twisted.python.failure import Failure # Import Failure
import psycopg2
import psycopg2.extras
from pytz import timezone as pytz_timezone

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import database pool
from ..db_pool import DbPool # Assuming db_pool is in the parent directory utils

class MatchSpider(Spider):
    name = 'match'
    allowed_domains = ['titan007.com', 'zq.titan007.com']
    
    # 博彩公司ID映射
    BOOKMAKER_IDS = {
        '281': 'Bet365',
        '115': 'William Hill',
        '82': 'Ladbrokes',
    }
    
    custom_settings = {
        'CONCURRENT_REQUESTS': 6,
        'DOWNLOAD_DELAY': 2,
        'RETRY_TIMES': 5,
        'RETRY_HTTP_CODES': [500, 502, 503, 504, 522, 524, 408, 429, 403],
        'LOG_FILE': '../spiders_log/match_spider.log',
        'LOG_LEVEL': 'INFO',
        'ITEM_PIPELINES': {
            'football_spider.pipelines.PostgreSQLPipeline': 300, # Or a specific MatchPipeline
        },
    }
    
    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        """
        Scrapy calls this method to create your Spider.
        It's the standard place to pass settings to the spider.
        """
        # Instantiate DbPool here and pass settings
        db_pool = DbPool(settings=crawler.settings)
        # Create the spider instance, passing the db_pool
        spider = cls(db_pool=db_pool, *args, **kwargs)
        spider._set_crawler(crawler)
        return spider

    def __init__(self, db_pool, competition_type=None, competition_db_id=None, season=None, 
                 source_competition_id=None, source_sub_league_id=None, # For fetching the initial JS file
                 target_match_source_id=None, target_team_name=None, task_id=None, use_proxy=False, 
                 target_round=None, *args, **kwargs):
        """
        初始化爬虫参数
        :param db_pool: Pre-initialized DbPool instance from from_crawler
        :param competition_type: 'league' 或 'cup'
        :param competition_db_id: 联赛或杯赛在数据库中的主键ID (leagues.id or cups.id)
        :param season: 赛季 (例如 '2024-2025' or '2024')
        :param source_competition_id: 源数据中的联赛/杯赛ID (例如 '36' for 英超, '283' for 欧洲杯), 用于构建初始JS URL
        :param source_sub_league_id: 源数据中的子联赛ID (可选)
        :param target_match_source_id: 指定要爬取的比赛的源ID (可选)
        :param target_team_name: 指定包含该球队名称的比赛 (可选)
        :param task_id: 任务ID
        :param use_proxy: Whether to use a proxy for requests
        :param target_round: 指定要爬取的轮次或杯赛阶段名称 (可选)
        """
        super(MatchSpider, self).__init__(*args, **kwargs)
        
        # Assign critical attributes early
        self.db_pool = db_pool # Use the db_pool passed from from_crawler
        self.season = season
        self.current_season = season # Assuming current_season is the same as season for these operations
        
        if not competition_type and not target_match_source_id:
            raise ValueError("必须提供 competition_type 或 target_match_source_id")
        if competition_type and competition_type not in ['league', 'cup']:
            raise ValueError("competition_type 必须是 'league' 或 'cup'")
        # Simplified this condition as source_competition_id is usually needed if type is provided
        if competition_type and not source_competition_id and not competition_db_id :
             raise ValueError("当提供 competition_type 时，必须提供 competition_db_id 或 source_competition_id")
        if target_match_source_id and target_team_name:
            raise ValueError("target_match_source_id 和 target_team_name 不能同时指定")

        self.competition_type = competition_type
        self.competition_db_id = competition_db_id
        self.source_competition_id = source_competition_id
        self.source_sub_league_id = source_sub_league_id or '' # Default to empty string
        self.target_match_source_id = target_match_source_id
        self.target_team_name = target_team_name
        self.task_id = task_id
        self.processed_matches = set() # Keep track of processed matches to avoid duplicates per spider run
        self.use_proxy = use_proxy
        self.proxy_url = None
        self.target_round = target_round # Store target_round
        self.source_id_to_team_name_map = {} # 初始化球队ID到名称的映射字典

        if self.use_proxy:
            self.proxy_url = self.settings.get('HTTP_PROXY')
            self.logger.info(f"Using proxy: {self.proxy_url}")
        else:
            self.logger.info("Not using proxy")

        # Parameter validation and dynamic fetching of competition details
        # Ensure self.season and self.db_pool are set before these calls
        if not self.target_match_source_id: # Only fetch competition details if not targeting a specific match
            if self.competition_db_id and self.competition_type:
                self._fetch_source_ids_from_db_id() 
            elif self.source_competition_id and self.competition_type:
                self._fetch_competition_details_from_source_id()
            # Removed the else ValueError here as initial checks cover lack of necessary IDs.
            # else:
            #     raise ValueError("When not targeting a specific match, either (competition_db_id and competition_type) or (source_competition_id and competition_type) must be provided.")
        
        self.logger.info(f"MatchSpider initialized with competition_type: {self.competition_type}, competition_db_id: {self.competition_db_id}, source_competition_id: {self.source_competition_id}, source_sub_league_id: {self.source_sub_league_id}, season: {self.season}, target_team: {self.target_team_name}, target_match: {self.target_match_source_id}")

        # self.db_pool = db_pool # Moved this assignment to the top of __init__
        self.competition_name_for_log = "未知赛事"

        # If only source_competition_id is given, try to find db_id and type
        # This also needs self.season and self.db_pool to be set
        if self.source_competition_id and not self.competition_db_id and not self.competition_type: # Ensure type isn't already set
            self._fetch_competition_details_from_db()
        elif self.competition_db_id and self.competition_type:
            # If db_id and type are given, try to get a name for logging
            self._fetch_competition_name_for_log()

        # Initialize task logger (if any)
        if task_id:
            try:
                from ..utils import SpiderLogger
                self.task_logger = SpiderLogger(task_id)
                self.logger.info(f'已初始化任务日志记录器，任务ID: {task_id}')
            except Exception as e:
                self.logger.error(f'初始化任务日志记录器失败: {str(e)}')
                self.task_logger = None
        else:
            self.task_logger = None
        
        self.match_ids_to_process = set()  # Store source match IDs to process
        self.processed_match_count = 0
        self.total_match_count = 0
        self.start_time = datetime.now()
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
        ]

        self.logger.info(f'MatchSpider 初始化完成:')
        self.logger.info(f'赛事类型: {self.competition_type}')
        self.logger.info(f'赛事DB ID: {self.competition_db_id}')
        self.logger.info(f'赛事源ID: {self.source_competition_id}')
        self.logger.info(f'赛季: {season}')
        if self.target_round:
            self.logger.info(f'目标轮次/阶段: {self.target_round}')
        if self.target_match_source_id:
            self.logger.info(f'目标比赛源ID: {self.target_match_source_id}')
        self.logger.info(f'子联赛ID: {self.source_sub_league_id if self.source_sub_league_id else "无"}')
        
        if self.task_id:
            log_message = f'爬虫初始化完成，准备获取{self.competition_name_for_log} {season}赛季'
            if self.source_competition_id:
                log_message += f'源ID: {self.source_competition_id}'
            if self.source_sub_league_id:
                log_message += f'子联赛ID: {self.source_sub_league_id}'
            if self.target_match_source_id:
                log_message += f'比赛ID {self.target_match_source_id} 的数据'
            else:
                 log_message += '数据'
            self.logger.info(f'任务ID: {self.task_id}')
            self.update_task_status(0, log_message)
        
    def _fetch_source_ids_from_db_id(self):
        """Fetches source_competition_id and source_sub_league_id using competition_db_id and competition_type."""
        try:
            with self.db_pool.get_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    if self.competition_type == 'league':
                        cur.execute("SELECT league_id, sub_league_id FROM leagues WHERE id = %s AND current_season = %s", 
                                    (self.competition_db_id, self.current_season))
                        record = cur.fetchone()
                        if record:
                            self.source_competition_id = str(record['league_id'])
                            self.source_sub_league_id = str(record['sub_league_id']) if record['sub_league_id'] else None
                            self.logger.info(f"Fetched league details from DB for id={self.competition_db_id}, season={self.current_season}: source_id={self.source_competition_id}, source_sub_id={self.source_sub_league_id}")
                        else:
                            raise ValueError(f"No league found in DB with id={self.competition_db_id} for season={self.current_season}")
                    elif self.competition_type == 'cup':
                        cur.execute("SELECT cup_id FROM cups WHERE id = %s AND current_season = %s", 
                                    (self.competition_db_id, self.current_season))
                        record = cur.fetchone()
                        if record:
                            self.source_competition_id = str(record['cup_id'])
                            self.logger.info(f"Fetched cup details from DB for id={self.competition_db_id}, season={self.current_season}: source_id={self.source_competition_id}")
                        else:
                            raise ValueError(f"No cup found in DB with id={self.competition_db_id} for season={self.current_season}")
                    else:
                        raise ValueError(f"Invalid competition_type: {self.competition_type}. Must be 'league' or 'cup'.")
        except (Exception, psycopg2.Error) as error:
            self.logger.error(f"Error fetching competition source IDs from DB: {error}")
            raise

    def _fetch_competition_details_from_source_id(self):
        """Fetches competition_db_id and potentially source_sub_league_id if it's a league, using source_competition_id."""
        try:
            with self.db_pool.get_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    if self.competition_type == 'league':
                        cur.execute("SELECT id, sub_league_id FROM leagues WHERE league_id = %s AND current_season = %s", 
                                    (self.source_competition_id, self.current_season))
                        record = cur.fetchone()
                        if record:
                            self.competition_db_id = record['id']
                            self.source_sub_league_id = str(record['sub_league_id']) if record['sub_league_id'] else None
                            self.logger.info(f"Fetched league DB ID for source_id={self.source_competition_id}, season={self.current_season}: db_id={self.competition_db_id}, source_sub_id={self.source_sub_league_id}")
                        else:
                            self.logger.warning(f"No league found in DB with source_id={self.source_competition_id} for season={self.current_season}. competition_db_id will be None.")
                            self.competition_db_id = None

                    elif self.competition_type == 'cup':
                        cur.execute("SELECT id FROM cups WHERE cup_id = %s AND current_season = %s", 
                                    (self.source_competition_id, self.current_season))
                        record = cur.fetchone()
                        if record:
                            self.competition_db_id = record['id']
                            self.logger.info(f"Fetched cup DB ID for source_id={self.source_competition_id}, season={self.current_season}: db_id={self.competition_db_id}")
                        else:
                            self.logger.warning(f"No cup found in DB with source_id={self.source_competition_id} for season={self.current_season}. competition_db_id will be None.")
                            self.competition_db_id = None
                    else:
                        raise ValueError(f"Invalid competition_type: {self.competition_type}. Must be 'league' or 'cup'.")
        except (Exception, psycopg2.Error) as error:
            self.logger.error(f"Error fetching competition DB ID from source ID: {error}")
            self.competition_db_id = None 

    def _fetch_competition_details_from_db(self):
        """If only source_id is given, try to find its type and db_id from the database."""
        if not self.source_competition_id:
            return
        try:
            with self.db_pool.get_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    # Check leagues first - leagues.league_id is varchar
                    cur.execute("SELECT id, league_short_name FROM leagues WHERE league_id = %s AND current_season = %s", 
                                (self.source_competition_id, self.season)) # Ensure string comparison
                    league_rec = cur.fetchone()
                    if league_rec:
                        self.competition_type = 'league'
                        self.competition_db_id = league_rec['id']
                        self.competition_name_for_log = league_rec['league_short_name']
                        self.logger.info(f"通过源ID {self.source_competition_id} 和赛季 {self.season} 在数据库中找到联赛: {self.competition_name_for_log} (DB ID: {self.competition_db_id})")
                        return

                    # Check cups if not found in leagues - cups.cup_id is assumed to be varchar from source
                    cur.execute("SELECT id, cup_short_name FROM cups WHERE cup_id = %s AND current_season = %s", 
                                (self.source_competition_id, self.season)) # Ensure string comparison
                    cup_rec = cur.fetchone()
                    if cup_rec:
                        self.competition_type = 'cup'
                        self.competition_db_id = cup_rec['id']
                        self.competition_name_for_log = cup_rec['cup_short_name']
                        self.logger.info(f"通过源ID {self.source_competition_id} 和赛季 {self.season} 在数据库中找到杯赛: {self.competition_name_for_log} (DB ID: {self.competition_db_id})")
                        return
                    
                    self.logger.warning(f"无法通过源ID {self.source_competition_id} 和赛季 {self.season} 在数据库中找到对应的联赛或杯赛记录。爬虫可能无法正确关联比赛数据。")
        except Exception as e:
            self.logger.error(f"从数据库获取赛事详情时出错: {e}")

    def _fetch_competition_name_for_log(self):
        if not self.competition_db_id or not self.competition_type:
            return
        try:
            with self.db_pool.get_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    if self.competition_type == 'league':
                        cur.execute("SELECT league_short_name FROM leagues WHERE id = %s", (self.competition_db_id,))
                        rec = cur.fetchone()
                        if rec: self.competition_name_for_log = rec['league_short_name']
                    elif self.competition_type == 'cup':
                        cur.execute("SELECT cup_short_name FROM cups WHERE id = %s", (self.competition_db_id,))
                        rec = cur.fetchone()
                        if rec: self.competition_name_for_log = rec['cup_short_name']
        except Exception as e:
            self.logger.error(f"为日志获取赛事名称时出错: {e}")

    def update_task_status(self, progress, message=''):
        """更新任务状态"""
        if not self.task_id:
            return
            
        try:
            # 调用Django API更新任务状态
            log_entry = message
            url = f"http://localhost:8000/api/spider-logs/{self.task_id}/update-status/"
            data = {
                'progress': progress,
                'message': message,
                'log_entry': log_entry
            }
            
            # 使用requests库发送POST请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(url, json=data, headers=headers, timeout=5)
            
            if response.status_code == 200:
                self.logger.debug(f"更新任务状态成功: {progress}% - {message}")
            else:
                self.logger.warning(f"更新任务状态返回错误: HTTP {response.status_code}, {response.text}")
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {str(e)}")
            # 异常不应影响爬虫的正常运行，所以这里只记录错误而不抛出异常

    def get_random_user_agent(self):
        return random.choice(self.user_agents)
    
    def start_requests(self):
        """开始请求比赛数据"""
        if self.target_match_source_id: # If a specific match ID is targeted
            # Directly fetch this match's details and odds. 
            # The URL for a single match's detailed data might be different or inferred.
            # For now, we assume we can construct an odds page URL or similar.
            # This part needs to be adapted based on how individual match data is fetched.
            match_detail_url = f"https://www.titan007.com/asian/{self.target_match_source_id}.htm" # Example, might need specific URL structure
            self.logger.info(f"目标为单个比赛源ID: {self.target_match_source_id}, 请求URL: {match_detail_url}")
            # Meta needs to convey enough info to create MatchItem and OddsItem
            # We might not have full competition_db_id if only target_match_source_id is given.
            # This scenario needs careful handling of how MatchItem.league/cup is populated.
            yield Request(url=match_detail_url, callback=self.parse_single_match_page, 
                          headers={'User-Agent': self.get_random_user_agent()},
                          meta={'match_source_id': self.target_match_source_id, 
                                'competition_type': self.competition_type, # May be None if not identified
                                'competition_db_id': self.competition_db_id # May be None
                               })
            return

        if not self.source_competition_id or not self.season:
            self.logger.error("未提供 source_competition_id 或 season，无法生成比赛列表URL。")
            return
        
        # If competition_db_id is still None after trying to fetch, log a warning
        if self.competition_type and not self.competition_db_id:
            self.logger.warning(f"赛事类型为 {self.competition_type} 但未能确定数据库ID (competition_db_id)，比赛可能无法正确关联。源ID: {self.source_competition_id}")

        # Generate URL for the league/cup match list JS file
        version = time.strftime("%Y%m%d%H", time.localtime())
        
        if self.competition_type == 'league':
            js_file_url_segment = f"s{self.source_competition_id}"
            if self.source_sub_league_id:
                js_file_url_segment += f"_{self.source_sub_league_id}"
            match_list_url = f'https://zq.titan007.com/jsData/matchResult/{self.season}/{js_file_url_segment}.js?version={version}'
            self.logger.info(f'开始爬取联赛 {self.season} 赛季 {self.competition_name_for_log} (源ID: {self.source_competition_id}) 的比赛数据')
        elif self.competition_type == 'cup':
            js_file_url_segment = f"c{self.source_competition_id}"
            match_list_url = f'https://zq.titan007.com/jsData/matchResult/{self.season}/{js_file_url_segment}.js?version={version}'
            self.logger.info(f'开始爬取杯赛 {self.season} 赛季 {self.competition_name_for_log} (源ID: {self.source_competition_id}) 的比赛数据')
        else:
            self.logger.error(f"未知的 competition_type: {self.competition_type}，无法生成URL。")
            return
        
        self.logger.info(f'请求比赛列表URL: {match_list_url}')
        # self.update_task_status(10, f'正在获取比赛列表: {self.competition_name_for_log}')

        yield Request(
            url=match_list_url,
            callback=self.parse_match_list, # Changed to parse_match_list
            headers={
                'User-Agent': self.get_random_user_agent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            },
            meta={
                'competition_type': self.competition_type,
                'competition_db_id': self.competition_db_id, # Pass the DB ID (FK for MatchItem)
                'season': self.season,
                'source_competition_id': self.source_competition_id,
                'source_sub_league_id': self.source_sub_league_id
            },
            errback=self.handle_error_for_request
        )

    def get_odds_url_with_selenium(self, match_id):
        """使用 Selenium 获取赔率 URL"""
        odds_list_url = f'https://1x2.titan007.com/oddslist/{match_id}.htm'
        odds_url = None
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument(f'user-agent={self.get_random_user_agent()}')
        
        try:
            # 修改 webdriver.Chrome 调用，使用固定的绝对路径
            chromedriver_path = r'C:\WebDriver\bin\chromedriver.exe' # 使用绝对路径
            if not os.path.exists(chromedriver_path):
                self.logger.error(f"指定的ChromeDriver路径不存在: {chromedriver_path}")
                raise FileNotFoundError(f"未在指定路径找到ChromeDriver: {chromedriver_path}")
            else:
                 self.logger.info(f"使用ChromeDriver路径: {chromedriver_path}")

            service = Service(executable_path=chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # 访问页面
            driver.get(odds_list_url)
            
            # 等待页面加载完成
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待一段时间让 JavaScript 执行完成
            time.sleep(5)
            
            # 获取页面源码
            page_source = driver.page_source
            
            # 使用正则表达式查找赔率 URL
            url_pattern = f'https://1x2d\.titan007\.com/{match_id}\.js\?r=[0-9]+'
            match = re.search(url_pattern, page_source)
            
            if match:
                odds_url = match.group(0)
                self.logger.info(f'找到目标赔率URL: {odds_url}')
            else:
                # 尝试从 script 标签中查找
                script_elements = driver.find_elements(By.TAG_NAME, "script")
                for script in script_elements:
                    src = script.get_attribute("src") or ""
                    if '1x2d.titan007.com' in src and f'{match_id}.js' in src:
                        odds_url = src
                        self.logger.info(f'从script标签中找到目标赔率URL: {odds_url}')
                        break
                        
                if not odds_url:
                    # 尝试执行 JavaScript 来获取 URL
                    try:
                        js_code = f'''
                        var scripts = document.getElementsByTagName('script');
                        for(var i = 0; i < scripts.length; i++) {{
                            var src = scripts[i].src;
                            if(src.includes('1x2d.titan007.com') && src.includes('{match_id}.js')) {{
                                return src;
                            }}
                        }}
                        return null;
                        '''
                        odds_url = driver.execute_script(js_code)
                        if odds_url:
                            self.logger.info(f'从JavaScript执行中找到目标赔率URL: {odds_url}')
                    except Exception as e:
                        self.logger.error(f"执行JavaScript时出错: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"获取赔率URL时出错: {str(e)}")
        finally:
            try:
                driver.quit()
            except:
                pass
        
        if not odds_url:
            self.logger.warning(f"未找到比赛ID {match_id} 的赔率URL")
            
        return odds_url

    def process_match_odds(self, match_item):
        """处理比赛赔率数据"""
        match_id = match_item["match_id"]
        
        try:
            # 先检查任务是否被暂停或终止
            if hasattr(self, 'task_logger') and self.task_logger:
                if self.task_logger.check_if_terminated():
                    self.logger.info(f'任务已被用户暂停或终止，跳过处理比赛ID {match_id} 的赔率数据')
                    return None
            
            # 使用 Selenium 获取赔率 URL
            odds_url = self.get_odds_url_with_selenium(match_id)
            
            if odds_url:
                self.logger.info(f'自动获取比赛ID {match_id} 的赔率URL: {odds_url}')
                return Request(
                    url=odds_url,
                    callback=self.parse_odds,
                    meta={
                        'match_id': match_id
                    },
                    errback=self.handle_odds_error,
                    dont_filter=True
                )
            else:
                self.logger.warning(f'未找到比赛ID {match_id} 的赔率URL')
                
        except Exception as e:
            self.logger.error(f'获取赔率URL时出错: {str(e)}')
        
        return None

    def handle_odds_error(self, failure: Failure):
        """Handles errors during odds data download."""
        match_id = failure.request.meta.get('match_id')
        if match_id:
            self.logger.error(f'Failed to download/process odds for match_id {match_id} after retries: {failure.value}')
            # Yield a special item to be caught by the pipeline
            yield {'retry_match_id': match_id}
        else:
            self.logger.error(f'Unknown error during odds download: {failure.value}')

    async def parse_match_list(self, response):
        """解析包含比赛列表的JS文件 (例如 s36.js or s283.js)"""
        self.logger.info(f"开始解析比赛列表: {response.url}")
        target_team_id = None # Initialize target_team_id to None here (will be source ID if target_team_name is found)
        try:
            content = response.body.decode('utf-8-sig') # Use utf-8-sig to handle BOM
            self.logger.info(f"parse_match_list: Received content (first 500 chars after utf-8-sig decode): {content[:500]}") 
            self.logger.info(f"parse_match_list: Content length after utf-8-sig decode: {len(content)}")

            # Try to manually strip BOM if utf-8-sig didn't catch it or if there's another similar prefix issue
            if content.startswith('\ufeff'):
                content = content[1:]
                self.logger.info("parse_match_list: Manually stripped BOM.")
            
            self.logger.info(f"parse_match_list: Final content check (first 500 chars): {content[:500]}")

            competition_type = response.meta.get('competition_type')
            competition_db_id = response.meta.get('competition_db_id')
            season = response.meta.get('season')
            source_competition_id = response.meta.get('source_competition_id')
            source_sub_league_id = response.meta.get('source_sub_league_id')
            
            if hasattr(self, 'task_logger') and self.task_logger and self.task_logger.check_if_terminated():
                self.logger.info('任务已被用户终止，停止处理比赛列表')
                return

            # --- 解析 arrTeam ---
            self.logger.info("Attempting to parse arrTeam with REGEX.")
            
            # Debug: Check if the basic marker string is present
            arr_team_marker = "var arrTeam = "
            marker_pos = content.find(arr_team_marker)
            if marker_pos != -1:
                self.logger.info(f"Found '{arr_team_marker}' at index {marker_pos}. Content snippet following marker (200 chars): {content[marker_pos : marker_pos + 200]}")
            else:
                self.logger.error(f"CRITICAL: Could not find arrTeam marker string '{arr_team_marker}' in content. Regex will fail.")
                # If marker is not found, it's a strong indicator of a problem with content or expectations.
                # Log a larger portion of content for diagnostics.
                self.logger.info(f"Content sample where marker was expected (first 1000 chars): {content[:1000]}")
                # Decide if to return or let regex attempt fail naturally
            
            # Corrected regex pattern for Python's re module (raw string, single backslashes for regex specials)
            arr_team_pattern = r'var arrTeam\s*=\s*(\[.*?\]);'
            self.logger.info(f"Using arrTeam_pattern: '{arr_team_pattern}'") # Log the exact pattern being used
            
            arr_team_match = None # Initialize
            try:
                arr_team_match = re.search(arr_team_pattern, content, re.DOTALL)
            except Exception as e_re_search:
                self.logger.error(f"Exception during re.search for arrTeam: {e_re_search}", exc_info=True)

            if arr_team_match:
                self.logger.info("SUCCESS: Regex found arrTeam match!")
                teams_data_raw = arr_team_match.group(1).strip()
                self.logger.info(f"arrTeam raw data for eval (first 200 chars): {teams_data_raw[:200]}")
                try:
                    safe_globals = {"__builtins__": {"list": list, "True": True, "False": False, "None": None}}
                    parsed_teams_list = eval(teams_data_raw, safe_globals, {"list": list})

                    if parsed_teams_list and isinstance(parsed_teams_list, list):
                        self.logger.info(f"Regex method: Successfully eval'd arrTeam. Parsed {len(parsed_teams_list)} teams.")
                        for team_info_list in parsed_teams_list:
                            if isinstance(team_info_list, list) and len(team_info_list) >= 3:
                                team_id_val = str(team_info_list[0]).strip("'\"")
                                team_name_simp = str(team_info_list[1]).strip("'\"")
                                self.source_id_to_team_name_map[team_id_val] = team_name_simp
                                
                                team_item = TeamItem()
                                team_item['team_id'] = team_id_val
                                team_item['team_name_simp'] = team_name_simp
                                team_item['team_name_cant'] = str(team_info_list[2]).strip("'\"") if len(team_info_list) > 2 else ''
                                team_item['team_name_en'] = str(team_info_list[3]).strip("'\"") if len(team_info_list) > 3 else ''
                                team_item['team_logo'] = str(team_info_list[5]).strip("'\"") if len(team_info_list) > 5 and team_info_list[5] else ''
                                yield team_item
                            else:
                                self.logger.debug(f"Regex method: Skipping non-standard team_info_list in arrTeam: {team_info_list}")
                    else:
                        self.logger.warning(f"Regex method: Parsed arrTeam (from regex) is not a list or is empty: {parsed_teams_list}")
                except Exception as e_eval_team:
                    self.logger.error(f"Regex method: Error eval'ing arrTeam data: {e_eval_team}. Raw data snippet: {teams_data_raw[:500]}", exc_info=True)
            else:
                self.logger.error("FAILURE: Regex did NOT find arrTeam match.")
                # Log a larger portion of content around where arrTeam is expected for manual inspection,
                # but only if the initial marker was found (otherwise previous log is sufficient).
                if marker_pos != -1: # Only log this if the basic string "var arrTeam = " was found
                    self.logger.info(f"Content around expected arrTeam (marker_pos - 50 to marker_pos + 500): {content[max(0, marker_pos - 50) : marker_pos + 500]}")
            # --- arrTeam 处理结束 ---

            if self.target_team_name:
                found_target_source_id = None
                for team_id_s, name_s in self.source_id_to_team_name_map.items():
                    if self.target_team_name == name_s:
                        found_target_source_id = team_id_s
                        break
                
                if found_target_source_id:
                    target_team_id = found_target_source_id 
                    self.logger.info(f'找到目标球队 "{self.target_team_name}" 的源ID: {target_team_id}')
                else:
                    self.logger.error(f'未在赛事数据(arrTeam)中找到球队名称: "{self.target_team_name}"')
                    return

            if competition_type == 'league':
                async for item in self.parse_league_matches(response, content, competition_db_id, season, source_competition_id, source_sub_league_id, target_team_id):
                    yield item
            elif competition_type == 'cup':
                async for item in self.parse_cup_matches(response, content, competition_db_id, season, source_competition_id, target_team_id):
                    yield item
            else:
                self.logger.error(f"未知的 competition_type: {competition_type} 在 parse_match_list 中")

        except Exception as e:
            self.logger.error(f'解析比赛列表时出错: {str(e)} ({response.url})', exc_info=True)

    async def parse_league_matches(self, response, content, competition_db_id, season, source_competition_id, source_sub_league_id, target_team_id):
        """解析联赛的比赛数据 (例如 s36.js)"""
        self.logger.info(f"开始解析联赛比赛数据: {self.competition_name_for_log} (源ID: {source_competition_id})")
        self.logger.info(f"parse_league_matches: Received content (first 500 chars): {content[:500]}")
        self.logger.info(f"parse_league_matches: Content length: {len(content)}")
        
        matches_pattern = r'jh\["R_(\d+)"\]\s*=\s*\[(.*?)\];'
        self.logger.info(f"parse_league_matches: Using matches_pattern: {matches_pattern}")
        
        match_iterator = list(re.finditer(matches_pattern, content, re.DOTALL)) # Convert to list to count, ADDED re.DOTALL
        self.logger.info(f"parse_league_matches: Found {len(match_iterator)} match objects for rounds.")

        all_matches_in_file = [] 
        found_target_match_for_team = False

        # 先检查 competition_db_id 是否设置正确，这是个关键字段
        if not competition_db_id:
            self.logger.error(f"competition_db_id 为空，无法正确设置 league_ref_id 字段。")
            return

        for match_obj in match_iterator:
            round_num_str = match_obj.group(1)
            matches_data_str = match_obj.group(2)

            if self.target_round and round_num_str != self.target_round:
                self.logger.info(f"跳过联赛轮次 {round_num_str} 因为目标是轮次 {self.target_round}")
                continue

            try:
                safe_globals = {"__builtins__": {"list": list, "True": True, "False": False, "None": None}}
                processed_data_str = matches_data_str
                while ',,' in processed_data_str:
                    processed_data_str = processed_data_str.replace(',,', ',None,')
                
                processed_matches_list = eval(f"[{processed_data_str}]", safe_globals, {"list": list})
            except Exception as e:
                self.logger.error(f"解析联赛轮次 {round_num_str} 的比赛数据时出错: {e}")
                self.logger.error(f"出错的原始数据 (轮次 {round_num_str}): BEGIN_DATA >>>{matches_data_str}<<< END_DATA")
                continue
                
            for match_data in processed_matches_list:
                if not isinstance(match_data, list) or len(match_data) < 8: # 需要至少8个元素来安全访问到 half_score
                    self.logger.warning(f"轮次 {round_num_str} 中发现格式不正确的比赛数据 (长度不足8): {match_data}")
                    continue
                
                match_data_source_id = str(match_data[0])
                home_team_s_id = str(match_data[4]) 
                away_team_s_id = str(match_data[5]) 
                
                if target_team_id: 
                    if target_team_id not in (home_team_s_id, away_team_s_id):
                        self.logger.debug(f"跳过比赛 {match_data_source_id} 因为与目标球队 {target_team_id} (源ID) 不符。主队: {home_team_s_id}, 客队: {away_team_s_id}")
                        continue # 如果设置了目标球队源ID，且当前比赛不涉及该球队，则跳过
                    else:
                        self.logger.info(f"处理涉及目标球队 {target_team_id} (源ID) 的比赛: {match_data_source_id}")
                        found_target_match_for_team = True

                if self.target_match_source_id and match_data_source_id != self.target_match_source_id:
                    continue 

                if match_data_source_id in self.processed_matches:
                    self.logger.info(f"比赛 {match_data_source_id} 已在本轮次处理过，跳过。")
                    continue
                
                item = MatchItem()
                item['match_id'] = match_data_source_id
                item['league_ref_id'] = competition_db_id 
                self.logger.info(f"设置比赛 {match_data_source_id} 的 league_ref_id = {competition_db_id}，轮次 = {round_num_str}")
                item['cup_ref_id'] = None                 
                item['season'] = season
                item['round'] = round_num_str
                item['sub_league_id_source'] = source_sub_league_id 

                item['home_team_id_source'] = home_team_s_id
                item['away_team_id_source'] = away_team_s_id
                
                # 从 self.source_id_to_team_name_map 获取球队名称
                item['home_team_name_source'] = self.source_id_to_team_name_map.get(home_team_s_id)
                item['away_team_name_source'] = self.source_id_to_team_name_map.get(away_team_s_id)
                if not item['home_team_name_source']:
                    self.logger.warning(f"未能从映射中找到主队源ID {home_team_s_id} 对应的名称 (比赛源ID: {match_data_source_id})")
                if not item['away_team_name_source']:
                    self.logger.warning(f"未能从映射中找到客队源ID {away_team_s_id} 对应的名称 (比赛源ID: {match_data_source_id})")

                match_time_str = str(match_data[3])
                try:
                    parsed_time = datetime.strptime(match_time_str, '%Y-%m-%d %H:%M')
                    beijing_tz = pytz_timezone('Asia/Shanghai')
                    local_dt = beijing_tz.localize(parsed_time)
                    utc_dt = local_dt.astimezone(pytz.utc)
                    item['match_time'] = utc_dt
                except ValueError as ve:
                    self.logger.error(f"轮次 {round_num_str}, 比赛 {match_data_source_id} 的比赛时间转换错误: {ve}, 原始数据: {match_time_str}")
                    item['match_time'] = None

                item['full_score'] = str(match_data[6]) if len(match_data) > 6 and match_data[6] is not None else None
                item['half_score'] = str(match_data[7]) if len(match_data) > 7 and match_data[7] is not None else None
                
                item['scraped_1d'] = False
                item['scraped_6h'] = False
                item['scraped_2h'] = False
                item['scraped_15m'] = False

                all_matches_in_file.append(item)
                self.processed_matches.add(match_data_source_id)

        if not all_matches_in_file:
            self.logger.info(f"在联赛JS文件中未找到符合条件的比赛数据 (目标轮次: {self.target_round if self.target_round else '所有轮次'}).")
            return

        self.logger.info(f"从联赛JS文件中解析到 {len(all_matches_in_file)} 场比赛需要处理。")
        for match_item in all_matches_in_file:
            yield match_item
            # After yielding MatchItem, trigger odds fetching if it's a target match or no target is specified
            if self.target_match_source_id and match_item['match_id'] == self.target_match_source_id:
                request_or_none = self.process_match_odds(match_item)
                if request_or_none:
                    yield request_or_none
            elif not self.target_match_source_id and not self.target_team_name: # If processing all matches
                 request_or_none = self.process_match_odds(match_item)
                 if request_or_none:
                    yield request_or_none

    async def parse_cup_matches(self, response, content, competition_db_id, season, source_competition_id, target_team_id):
        """解析杯赛的比赛数据 (例如 c103.js)"""
        self.logger.info(f"开始解析杯赛比赛数据: {self.competition_name_for_log} (源ID: {source_competition_id})")
        
        cup_stages_map = {}
        arr_cup_kind_match = re.search(r'var\s+arrCupKind\s*=\s*(\[.*?\]);', content, re.DOTALL)
        if arr_cup_kind_match:
            cup_kind_data_str = arr_cup_kind_match.group(1)
            try:
                safe_globals = {"__builtins__": {"list": list, "True": True, "False": False, "None": None}}
                cup_kind_list = eval(cup_kind_data_str, safe_globals, {"list": list})
                for stage_info in cup_kind_list:
                    if isinstance(stage_info, list) and len(stage_info) >= 3:
                        cup_stages_map[str(stage_info[0])] = str(stage_info[2]) # stage_id_source : stage_name_cn
                self.logger.info(f"解析到 {len(cup_stages_map)} 个杯赛阶段: {cup_stages_map}")
            except Exception as e:
                self.logger.error(f"解析杯赛阶段数据 arrCupKind 失败: {e}")

        # Regex to find all rounds/stages (jh["Cup_分组赛阶段ID"]) and their matches
        # Example: jh["C_103_A"] = [...] or jh["C_283_1631"]
        # The ID after "C_" is source_competition_id, then stage_source_id
        # Pattern for stages like C_103_A (group stage) or C_103_1631 (knockout stage)
        # The stage identifier can be alphanumeric (like 'A', 'B') or numeric (like '1631')
        cup_matches_pattern = rf'jh\["C_{source_competition_id}_([\w\d]+)"\]\s*=\s*\[(.*?)\];'
        
        # 添加对jh["G阶段ID"]格式的支持，这种格式通常用于决赛阶段
        g_matches_pattern = r'jh\["G(\d+)"\]\s*=\s*\[(.*?)\];'
        
        all_cup_matches_in_file = []
        found_target_match_for_team_cup = False

        # 先处理常规C_格式的杯赛阶段
        for stage_match_obj in re.finditer(cup_matches_pattern, content, re.DOTALL):
            stage_source_id = stage_match_obj.group(1) # e.g., 'A' or '1631'
            matches_data_str = stage_match_obj.group(2)
            
            stage_name_cn = cup_stages_map.get(stage_source_id, f"阶段 {stage_source_id}") # Get readable stage name

            if self.target_round and stage_name_cn != self.target_round:
                self.logger.info(f"跳过杯赛阶段 '{stage_name_cn}' (源阶段ID: {stage_source_id}) 因为目标是 '{self.target_round}'")
                continue
            else:
                 self.logger.info(f"正在处理杯赛阶段 '{stage_name_cn}' (源阶段ID: {stage_source_id})")

            try:
                safe_globals = {"__builtins__": {"list": list, "True": True, "False": False, "None": None}}
                # Pre-process the string for cup matches as well
                processed_data_str_cup = matches_data_str
                while ',,' in processed_data_str_cup:
                    processed_data_str_cup = processed_data_str_cup.replace(',,', ',None,')

                processed_matches_list = eval(f"[{processed_data_str_cup}]", safe_globals, {"list": list})
            except Exception as e:
                self.logger.error(f"解析杯赛阶段 {stage_name_cn} (源阶段ID: {stage_source_id}) 的比赛数据时出错: {e}")
                self.logger.debug(f"出错的原始数据: {matches_data_str}")
                continue

            for match_data in processed_matches_list:
                # Cup match data structure (example from Euro Cup C_283_1631):
                # [
                #   match_id_source, home_team_id_source, away_team_id_source, 
                #   home_team_name_simp, away_team_name_simp, 
                #   year, month, day, hour, minute, 
                #   home_score_full, away_score_full, 
                #   home_score_half, away_score_half, 
                #   status_id, home_rank_source, away_rank_source, 
                #   neutral_flag (0=false, 1=true), level (importance?), group_name_source, 
                #   round_id_source_for_cup (differs from stage_source_id sometimes?), 
                #   home_red_cards, away_red_cards, home_yellow_cards, away_yellow_cards,
                #   sub_league_id_source (often empty for cups), home_corner, away_corner
                # ]
                # Indices for C_283_1631 (Euro Cup Knockout):
                # 0: match_id_source (e.g., 4525916)
                # 1: home_team_id_source (e.g., 83)
                # 2: away_team_id_source (e.g., 95)
                # 3: home_team_name_simp_source (e.g., "瑞士")
                # 4: away_team_name_simp_source (e.g., "德国")
                # 5: year (e.g., 2024)
                # 6: month (e.g., 6)
                # 7: day (e.g., 24)
                # 8: hour (e.g., 3)
                # 9: minute (e.g., 0)
                # 10: home_score_full (e.g., "1")
                # 11: away_score_full (e.g., "1")
                # ... many more fields, up to 26 or more
                
                if not isinstance(match_data, list) or len(match_data) < 12: # Minimum check
                    self.logger.warning(f"杯赛阶段 {stage_name_cn} 中发现格式不正确的比赛数据: {match_data}")
                    continue

                match_data_source_id = str(match_data[0])
                home_team_s_id = str(match_data[1])
                away_team_s_id = str(match_data[2])

                # Similar filtering logic as in league matches for target_team / target_match
                if target_team_id:
                    # This needs careful implementation if target_team_id is DB ID
                    # See notes in parse_league_matches
                    pass # Temporarily bypassing

                if self.target_match_source_id and match_data_source_id != self.target_match_source_id:
                                    continue

                if match_data_source_id in self.processed_matches:
                    self.logger.info(f"杯赛比赛 {match_data_source_id} 已在本轮次处理过，跳过。")
                    continue
                
                item = MatchItem()
                item['match_id'] = match_data_source_id
                item['cup_ref_id'] = competition_db_id # Assign cup_ref_id for cup matches
                self.logger.info(f"设置杯赛比赛 {match_data_source_id} 的 cup_ref_id = {competition_db_id}，阶段 = {stage_name_cn}")
                item['league_ref_id'] = None            # Explicitly set league_ref_id to None for cup matches
                item['season'] = season
                item['round'] = stage_name_cn # Use the mapped stage name as the round
                # sub_league_id for cups might be present in match_data[25] if available, otherwise None
                item['sub_league_id_source'] = str(match_data[25]) if len(match_data) > 25 and match_data[25] else None


                item['home_team_id_source'] = home_team_s_id
                item['home_team_name_source'] = match_data[3] if len(match_data) > 3 else None
                item['away_team_id_source'] = away_team_s_id
                item['away_team_name_source'] = match_data[4] if len(match_data) > 4 else None
                
                try:
                    year, month, day, hour, minute = int(match_data[5]), int(match_data[6]), int(match_data[7]), int(match_data[8]), int(match_data[9])
                    if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31 and 0 <= hour <= 23 and 0 <= minute <= 59:
                        beijing_tz = pytz_timezone('Asia/Shanghai')
                        local_dt = beijing_tz.localize(datetime(year, month, day, hour, minute))
                        dt_obj_utc = local_dt.astimezone(pytz.utc)
                        item['match_time'] = dt_obj_utc
                    else:
                        self.logger.info(f"杯赛阶段 {stage_name_cn}, 比赛 {match_data_source_id} 的时间数据超出有效范围: {year}-{month}-{day} {hour}:{minute}，设置为None")
                        item['match_time'] = None
                except (ValueError, TypeError) as ve:
                    self.logger.error(f"杯赛阶段 {stage_name_cn}, 比赛 {match_data_source_id} 的比赛时间转换错误: {ve}, 原始数据: {match_data[5:10]}")
                    item['match_time'] = None

                item['full_score'] = f"{match_data[10]}-{match_data[11]}" if len(match_data) > 11 and match_data[10] is not None and match_data[11] is not None else None
                item['half_score'] = f"{match_data[12]}-{match_data[13]}" if len(match_data) > 13 and match_data[12] is not None and match_data[13] is not None else None
                
                item['scraped_1d'] = False
                item['scraped_6h'] = False
                item['scraped_2h'] = False
                item['scraped_15m'] = False

                all_cup_matches_in_file.append(item)
                self.processed_matches.add(match_data_source_id)

        # 处理G格式的杯赛阶段（通常是决赛阶段）
        for g_match_obj in re.finditer(g_matches_pattern, content, re.DOTALL):
            stage_source_id = g_match_obj.group(1)  # 例如 "26545"
            matches_data_str = g_match_obj.group(2)
            
            stage_name_cn = cup_stages_map.get(stage_source_id, "决赛")  # 默认为"决赛"，如果在cup_stages_map中有映射则使用映射值
            
            if self.target_round and stage_name_cn != self.target_round:
                self.logger.info(f"跳过杯赛阶段 '{stage_name_cn}' (G格式，源阶段ID: {stage_source_id}) 因为目标是 '{self.target_round}'")
                continue
            else:
                self.logger.info(f"正在处理杯赛阶段 '{stage_name_cn}' (G格式，源阶段ID: {stage_source_id})")
            
            try:
                safe_globals = {"__builtins__": {"list": list, "True": True, "False": False, "None": None}}
                processed_data_str_g = matches_data_str
                while ',,' in processed_data_str_g:
                    processed_data_str_g = processed_data_str_g.replace(',,', ',None,')
                
                processed_matches_list = eval(f"[{processed_data_str_g}]", safe_globals, {"list": list})
            except Exception as e:
                self.logger.error(f"解析G格式杯赛阶段 {stage_name_cn} (源阶段ID: {stage_source_id}) 的比赛数据时出错: {e}")
                self.logger.debug(f"出错的原始数据: {matches_data_str}")
                continue
            
            for match_data in processed_matches_list:
                # G格式的数据结构可能与C格式略有不同，但基本字段应该相似
                if not isinstance(match_data, list) or len(match_data) < 4:  # 最低要求检查
                    self.logger.warning(f"G格式杯赛阶段 {stage_name_cn} 中发现格式不正确的比赛数据: {match_data}")
                    continue
                
                match_data_source_id = str(match_data[0])
                
                # 检查是否已处理过该比赛
                if match_data_source_id in self.processed_matches:
                    self.logger.info(f"G格式杯赛比赛 {match_data_source_id} 已处理过，跳过。")
                    continue
                
                # 获取主客队ID
                home_team_s_id = str(match_data[4]) if len(match_data) > 4 else None
                away_team_s_id = str(match_data[5]) if len(match_data) > 5 else None
                
                # 如果指定了目标比赛ID且不匹配，则跳过
                if self.target_match_source_id and match_data_source_id != self.target_match_source_id:
                    continue
                
                # 创建比赛项
                item = MatchItem()
                item['match_id'] = match_data_source_id
                item['cup_ref_id'] = competition_db_id
                self.logger.info(f"设置G格式杯赛比赛 {match_data_source_id} 的 cup_ref_id = {competition_db_id}，阶段 = {stage_name_cn}")
                item['league_ref_id'] = None
                item['season'] = season
                item['round'] = stage_name_cn
                
                # G格式中可能没有sub_league_id
                item['sub_league_id_source'] = None
                
                # 设置主客队信息
                item['home_team_id_source'] = home_team_s_id
                item['away_team_id_source'] = away_team_s_id
                
                # 从映射中获取球队名称
                item['home_team_name_source'] = self.source_id_to_team_name_map.get(home_team_s_id)
                item['away_team_name_source'] = self.source_id_to_team_name_map.get(away_team_s_id)
                
                # 处理比赛时间
                match_time_str = str(match_data[3])
                if match_time_str:
                    try:
                        if re.match(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}', match_time_str):
                            parsed_time = datetime.strptime(match_time_str, '%Y-%m-%d %H:%M')
                            beijing_tz = pytz_timezone('Asia/Shanghai')
                            local_dt = beijing_tz.localize(parsed_time)
                            item['match_time'] = local_dt.astimezone(pytz.utc)
                        else:
                            self.logger.info(f"G格式杯赛阶段 {stage_name_cn}, 比赛 {match_data_source_id} 的时间格式非标准: {match_time_str}，设置为None")
                            item['match_time'] = None
                    except ValueError as ve:
                        self.logger.error(f"G格式杯赛阶段 {stage_name_cn}, 比赛 {match_data_source_id} 的比赛时间转换错误: {ve}, 原始数据: {match_time_str}")
                        item['match_time'] = None
                else:
                    item['match_time'] = None
                
                # 处理比分
                item['full_score'] = str(match_data[6]) if len(match_data) > 6 and match_data[6] else None
                item['half_score'] = str(match_data[7]) if len(match_data) > 7 and match_data[7] else None
                
                # 设置爬取标志
                item['scraped_1d'] = False
                item['scraped_6h'] = False
                item['scraped_2h'] = False
                item['scraped_15m'] = False
                
                all_cup_matches_in_file.append(item)
                self.processed_matches.add(match_data_source_id)

        if not all_cup_matches_in_file:
            self.logger.info(f"在杯赛JS文件中未找到符合条件的比赛数据 (目标阶段: {self.target_round if self.target_round else '所有阶段'}).")
            return
            
        self.logger.info(f"从杯赛JS文件中解析到 {len(all_cup_matches_in_file)} 场比赛需要处理。")
        for match_item in all_cup_matches_in_file:
            yield match_item
            # After yielding MatchItem, trigger odds fetching
            if self.target_match_source_id and match_item['match_id'] == self.target_match_source_id:
                request_or_none = self.process_match_odds(match_item)
                if request_or_none:
                    yield request_or_none
            elif not self.target_match_source_id and not self.target_team_name:
                request_or_none = self.process_match_odds(match_item)
                if request_or_none:
                    yield request_or_none

    def parse_single_match_page(self, response):
        # Placeholder: This method would parse a page dedicated to a single match
        # It would extract match details and then likely call methods to get odds
        self.logger.info(f"解析单个比赛页面: {response.url}")
        match_source_id = response.meta.get('match_source_id')
        # ... logic to extract match details from this page ...
        # ... create MatchItem ...
        # ... then fetch odds for this match_source_id ...
        pass

    def parse_odds(self, response):
        """解析赔率信息"""
        try:
            match_id = response.meta['match_id']
            content = response.text
            
            self.logger.info(f'正在解析比赛ID: {match_id} 的赔率数据')
            
            # 定义目标博彩公司ID
            target_bookmakers = {
                '281': 'Bet365',
                '82': '立博',
                '115': '威廉希尔'
            }
            
            # 解析var game数据
            game_pattern = r'var\s+game\s*=\s*Array\("(.*?)"\);'
            game_match = re.search(game_pattern, content)
            
            if game_match:
                self.logger.info('成功匹配var game数据')
                game_data = game_match.group(1).split('","')
                
                # 遍历所有数据项，查找目标博彩公司
                for data in game_data:
                    if '|' not in data:
                        continue
                        
                    odds_info = data.split('|')
                    if len(odds_info) < 2:
                        continue
                        
                    bookmaker_id = odds_info[0]
                    odds_detail_id = odds_info[1]
                    
                    # 检查是否是目标博彩公司
                    if bookmaker_id in target_bookmakers:
                        self.logger.info(f'找到目标博彩公司: {target_bookmakers[bookmaker_id]}')
                        self.logger.info(f'对应的欧赔详细数据ID: {odds_detail_id}')
                        
                        # 解析var gameDetail数据
                        game_detail_pattern = r'var\s+gameDetail\s*=\s*Array\("(.*?)"\);'
                        game_detail_match = re.search(game_detail_pattern, content)
                        
                        if game_detail_match:
                            game_details = game_detail_match.group(1).split('","')
                            
                            # 查找对应的赔率详情
                            for detail in game_details:
                                if detail.startswith(f"{odds_detail_id}^"):
                                    odds_data = detail.split('^')[1]
                                    odds_changes = odds_data.split(';')
                                    
                                    self.logger.info(f'找到赔率变化历史数据，共 {len(odds_changes)} 条记录')
                                    
                                    # 处理每一条赔率变化记录
                                    for change in odds_changes:
                                        if not change.strip():
                                            continue
                                        
                                        parts = change.split('|')
                                        if len(parts) >= 4:
                                            try:
                                                odds_item = {
                                                    'match_id': match_id,
                                                    'odds_detail_id': odds_detail_id,
                                                    'bookmaker_id': bookmaker_id,
                                                    'home_win': float(parts[0]),
                                                    'draw': float(parts[1]),
                                                    'away_win': float(parts[2]),
                                                    'update_time': parts[3]
                                                }
                                                
                                                # 标准化update_time格式为PostgreSQL可接受的时间戳格式
                                                try:
                                                    if parts[3].isdigit():
                                                        # 处理Unix时间戳
                                                        timestamp = int(parts[3])
                                                        dt_object = datetime.fromtimestamp(timestamp)
                                                        odds_item['update_time'] = dt_object.strftime('%Y-%m-%d %H:%M:%S')
                                                        self.logger.info(f'转换Unix时间戳: {parts[3]} -> {odds_item["update_time"]}')
                                                    elif re.match(r'^[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$', parts[3]):
                                                        # 处理"MM-DD HH:MM"格式
                                                        current_year = datetime.now().year
                                                        time_str = f"{current_year}-{parts[3][:5]} {parts[3][6:]}"
                                                        dt_object = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                                                        odds_item['update_time'] = dt_object.strftime('%Y-%m-%d %H:%M:%S')
                                                        self.logger.info(f'转换短日期格式: {parts[3]} -> {odds_item["update_time"]}')
                                                except Exception as e:
                                                    self.logger.error(f'标准化时间格式失败: {str(e)}')
                                                
                                                self.logger.info(f'创建赔率记录: {odds_item}')
                                                self.logger.info(f'赔率记录详情 - 比赛ID: {match_id}, 赔率详情ID: {odds_detail_id}, 博彩公司: {target_bookmakers[bookmaker_id]}')
                                                self.logger.info(f'赔率值 - 主胜: {odds_item["home_win"]}, 平局: {odds_item["draw"]}, 客胜: {odds_item["away_win"]}')
                                                self.logger.info(f'更新时间(标准化格式): {odds_item["update_time"]}')
                                                yield odds_item
                                                
                                            except (ValueError, IndexError) as e:
                                                self.logger.error(f'处理赔率数值时出错: {str(e)}')
                                                self.logger.error(f'赔率数据: {parts}')
                                    break
            else:
                self.logger.error('未找到var game数据')
                
        except Exception as e:
            self.logger.error(f'解析赔率数据时出错: {str(e)}')
            if not response.meta.get("retried", False):
                yield Request(
                    response.url,
                    callback=self.parse_odds,
                    meta={**response.meta, "retried": True},
                    dont_filter=True
                )
                
    def handle_error_for_request(self, failure):
        self.logger.error(f"请求失败: {failure.request.url}, 错误: {failure.value}")
        # ... (potential specific error handling) ...

    def closed(self, reason):
        """爬虫关闭时调用"""
        if self.task_id:
            # 计算总耗时
            duration = datetime.now() - self.start_time
            minutes, seconds = divmod(duration.seconds, 60)
            
            # 根据关闭原因决定任务是成功还是失败
            is_success = reason == 'finished'
            status = '已完成' if is_success else '异常终止'
            
            # 更新最终状态
            if self.target_match_source_id:
                 message = f"目标比赛 {self.target_match_source_id} 爬取{status}！耗时{minutes}分{seconds}秒"
            else:
                 message = f"爬虫{status}！处理了{self.processed_match_count}场比赛，耗时{minutes}分{seconds}秒"
            
            progress = 100 if is_success else 90
            
            # 记录到爬虫日志
            self.logger.info(f"爬虫{status}，原因: {reason}")
            if self.target_match_source_id:
                 self.logger.info(f"处理了目标比赛ID: {self.target_match_source_id}")
            else:
                 self.logger.info(f"总共处理了 {self.processed_match_count} 场比赛")
            self.logger.info(f"总耗时: {minutes}分{seconds}秒")
            
            # 尝试直接通过API完成任务
            try:
                url = f"http://localhost:8000/api/spider-logs/{self.task_id}/complete/"
                data = {
                    'success': is_success,
                    'message': message
                }
                headers = {'Content-Type': 'application/json'}
                response = requests.post(url, json=data, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    self.logger.info(f"成功更新任务完成状态: {is_success}")
                else:
                    # 如果完成API不存在，使用常规状态更新
                    self.update_task_status(progress, message)
            except Exception as e:
                self.logger.error(f"通过complete API更新状态失败: {str(e)}")
                # 回退到常规状态更新
                self.update_task_status(progress, message)

        if self.db_pool:
            self.db_pool.close_all()