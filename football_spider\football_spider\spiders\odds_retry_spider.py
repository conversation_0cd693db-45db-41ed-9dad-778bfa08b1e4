import scrapy
import logging
import os
import re
import time
import sys
import random
from datetime import datetime
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
# from ..items import OddsItem # Pipeline handles dicts
from ..db_pool import get_db_pool, init_db_pool # Need db pool
from psycopg2.extras import DictCursor
from psycopg2 import sql
from twisted.python.failure import Failure

# Add project root if needed
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.append(project_root)

class OddsRetrySpider(scrapy.Spider):
    """
    Spider dedicated to retrying failed odds data fetching tasks
    from the odds_retry_queue table.
    """
    name = 'odds_retry'
    allowed_domains = ['titan007.com']
    custom_settings = {
        'CONCURRENT_REQUESTS': 2, # Can be slightly higher for retries
        'DOWNLOAD_DELAY': 5,      # Can be slightly lower for retries
        'RETRY_TIMES': 2,         # Fewer retries within the retry spider itself
        'RETRY_HTTP_CODES': [500, 502, 503, 504, 522, 524, 408, 429, 403],
        # Ensure the pipeline is enabled
        'ITEM_PIPELINES': {
           'football_spider.pipelines.PostgreSQLPipeline': 300,
        },
        'LOG_LEVEL': 'INFO',
        'LOG_FILE': '../spiders_log/odds_retry_spider.log',
    }
    max_attempts = 3 # Max retry attempts for a specific match_id via this spider

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.db_pool = None
        self.user_agents = [ # Copy from match_spider
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ]
        # db_pool is initialized in from_crawler

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        spider = super().from_crawler(crawler, *args, **kwargs)
        spider.settings = crawler.settings # Make settings available
        try:
            # Initialize db_pool here using settings from crawler
            spider.db_pool = init_db_pool(spider.settings)
            spider.logger.info("Database pool initialized for OddsRetrySpider.")
        except Exception as e:
            spider.logger.error(f"Failed to initialize database pool in from_crawler: {e}")
            raise  # Stop spider if DB pool fails
        return spider

    def start_requests(self):
        self.logger.info("Starting odds retry spider...")
        if not self.db_pool:
            self.logger.error("Database pool not initialized. Cannot fetch tasks.")
            return

        tasks_to_fetch = 10 # Limit batch size
        select_sql = sql.SQL(r"""
            SELECT id, match_id, attempts
            FROM odds_retry_queue
            WHERE status = %s AND attempts < %s
            ORDER BY created_at ASC
            LIMIT %s
            FOR UPDATE SKIP LOCKED;
        """)
        update_sql = sql.SQL(r"UPDATE odds_retry_queue SET status = %s, updated_at = NOW() WHERE id = ANY(%s::int[]);") # Cast to int array

        pending_tasks = []
        task_ids_to_process = []

        try:
            # Use a single connection and cursor for the transaction
            conn = self.db_pool.pool.getconn() # Access internal pool object
            conn.autocommit = False # Start transaction
            cur = conn.cursor(cursor_factory=DictCursor)

            try:
                # Fetch tasks
                cur.execute(select_sql, ('pending', self.max_attempts, tasks_to_fetch))
                pending_tasks = cur.fetchall()

                if not pending_tasks:
                    self.logger.info("No pending odds retry tasks found.")
                    conn.rollback() # Rollback the transaction
                    cur.close()
                    self.db_pool.putconn(conn)
                    return

                task_ids_to_process = [task['id'] for task in pending_tasks]

                # Mark tasks as 'processing'
                cur.execute(update_sql, ('processing', task_ids_to_process))
                conn.commit() # Commit transaction
                self.logger.info(f"Fetched and marked {len(pending_tasks)} tasks as processing: {[task['match_id'] for task in pending_tasks]}")

            except Exception as fetch_err:
                self.logger.error(f"Error fetching or marking retry tasks: {fetch_err}")
                conn.rollback() # Rollback on error
                raise # Re-raise to stop if needed
            finally:
                cur.close()
                self.db_pool.pool.putconn(conn) # Return connection to pool

        except Exception as e:
            self.logger.error(f"Database transaction failed: {e}")
            return # Stop if transaction fails

        # Yield requests for fetched tasks
        for task in pending_tasks:
            queue_id = task['id']
            match_id = task['match_id']
            attempts = task['attempts']

            self.logger.info(f"Processing retry task: queue_id={queue_id}, match_id={match_id}, attempt={attempts + 1}")

            # Reuse selenium logic to get the dynamic odds URL
            try:
                odds_url = self.get_odds_url_with_selenium(match_id)
            except Exception as selenium_err:
                self.logger.error(f"Selenium failed for match_id {match_id}: {selenium_err}")
                self.handle_failed_attempt(queue_id, match_id, attempts + 1, f"Selenium error: {selenium_err}")
                continue # Skip to next task

            if odds_url:
                yield scrapy.Request(
                    url=odds_url,
                    callback=self.parse_odds_retry,
                    errback=self.handle_retry_error,
                    meta={
                        'queue_id': queue_id,
                        'match_id': match_id,
                        'attempt': attempts + 1
                    },
                    headers={'User-Agent': self.get_random_user_agent()},
                    dont_filter=True
                )
            else:
                # If Selenium finishes but returns no URL
                self.logger.warning(f"Could not get odds URL for match_id {match_id} via Selenium. Marking as failed attempt.")
                self.handle_failed_attempt(queue_id, match_id, attempts + 1, "Selenium could not find odds URL")

    def get_random_user_agent(self):
        """Returns a random user agent string."""
        return random.choice(self.user_agents)

    def get_odds_url_with_selenium(self, match_id):
        """
        Uses Selenium to navigate to the odds list page and extract the dynamic JS URL
        containing the odds data for a specific match.
        Adapted from MatchSpider.
        """
        odds_list_url = f'https://1x2.titan007.com/oddslist/{match_id}.htm'
        odds_url = None
        self.logger.info(f"[Retry Selenium] Getting odds URL for match_id: {match_id} from {odds_list_url}")

        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument(f'user-agent={self.get_random_user_agent()}')
        # Reduce log spam from webdriver
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])

        # Ensure chromedriver path is correct (relative to project root)
        chromedriver_path = os.path.join(project_root, 'chromedriver.exe')
        if not os.path.exists(chromedriver_path):
             self.logger.error(f"Chromedriver not found at: {chromedriver_path}")
             raise FileNotFoundError(f"Chromedriver not found at: {chromedriver_path}")

        driver = None
        try:
            service = Service(executable_path=chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(30) # Set timeout for page load

            driver.get(odds_list_url)

            # Wait for body, consider waiting for a more specific element if possible
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Give some time for JS execution, might need adjustment
            time.sleep(random.uniform(4, 7))

            page_source = driver.page_source

            # Regex to find the target JS URL
            url_pattern = rf'https://1x2d\.titan007\.com/{match_id}\.js\?r=\d+'
            match = re.search(url_pattern, page_source)

            if match:
                odds_url = match.group(0)
                self.logger.info(f'[Retry Selenium] Found odds URL via regex: {odds_url}')
            else:
                # Fallback: Try finding in script tags
                script_elements = driver.find_elements(By.TAG_NAME, "script")
                for script in script_elements:
                    src = script.get_attribute("src") or ""
                    if '1x2d.titan007.com' in src and f'{match_id}.js' in src:
                        odds_url = urljoin(odds_list_url, src) # Ensure absolute URL
                        self.logger.info(f'[Retry Selenium] Found odds URL in script tag: {odds_url}')
                        break
                # Fallback: Try executing JS (less reliable)
                if not odds_url:
                    try:
                        js_code = f'''
                        var scripts = document.getElementsByTagName('script');
                        for(var i = 0; i < scripts.length; i++) {{
                            var src = scripts[i].src;
                            if(src && src.includes('1x2d.titan007.com') && src.includes('/{match_id}.js')) {{
                                return src; // Return the full URL found
                            }}
                        }}
                        // Look for inline script defining game or gameDetail related to the URL pattern
                        var inlineScripts = document.querySelectorAll('script:not([src])');
                        var pattern = /https:\\/\\/1x2d\\.titan007\\.com\\/{match_id}\\.js\\?r=\\d+/;
                        for(var i = 0; i < inlineScripts.length; i++) {{
                           var match = inlineScripts[i].innerHTML.match(pattern);
                           if (match) {{
                               return match[0];
                           }}
                        }}
                        return null;
                        '''
                        potential_url = driver.execute_script(js_code)
                        if potential_url:
                             odds_url = urljoin(odds_list_url, potential_url) # Ensure absolute
                             self.logger.info(f'[Retry Selenium] Found odds URL via JS execution: {odds_url}')
                    except Exception as js_e:
                        self.logger.error(f"[Retry Selenium] Error executing JS for {match_id}: {js_e}")

        except Exception as e:
            self.logger.error(f"[Retry Selenium] Error getting odds URL for {match_id}: {e.__class__.__name__} - {e}")
            # Re-raise the exception so start_requests can handle it
            raise
        finally:
            if driver:
                driver.quit()

        if not odds_url:
            self.logger.warning(f"[Retry Selenium] Failed to find odds URL for match_id {match_id}")

        return odds_url

    def parse_odds_retry(self, response):
        """
        Parses the odds data from the fetched JS file and yields odds dicts.
        If successful, yields a success marker for the pipeline.
        """
        queue_id = response.meta['queue_id']
        match_id = response.meta['match_id']
        self.logger.info(f"Parsing odds for retry task: queue_id={queue_id}, match_id={match_id}, url={response.url}")

        try:
            # Use a generator to yield items from the parsing logic
            odds_items_generated = False
            for item_dict in self._parse_odds_logic(response):
                yield item_dict
                odds_items_generated = True

            # Only yield success if parsing logic completed AND generated items
            if odds_items_generated:
                self.logger.info(f"Successfully parsed odds for retry task: queue_id={queue_id}, match_id={match_id}")
                yield {'retry_success': queue_id, 'match_id': match_id}
            else:
                 # If parsing completed but found no relevant odds data
                 self.logger.warning(f"Parsing completed, but no target odds data yielded for queue_id={queue_id}, match_id={match_id}. Considering failed.")
                 # Raise an error to trigger errback and mark as failed attempt
                 raise ValueError(f"No target odds data found/parsed for match {match_id}")

        except Exception as e:
            self.logger.error(f"Error during parsing odds logic for queue_id={queue_id}, match_id={match_id}: {e}")
            # Re-raise the exception to ensure Scrapy calls the errback
            raise

    def _parse_odds_logic(self, response):
        """
        Core logic to parse odds data from the JS file content.
        Yields dictionaries representing odds records.
        Adapted from MatchSpider.
        """
        match_id = response.meta['match_id']
        content = response.text
        target_bookmakers = {'281': 'Bet365', '82': '立博', '115': '威廉希尔'}

        # Check if content is valid JS or looks like HTML (error page)
        if content.strip().startswith('<') or not content.strip():
            self.logger.warning(f"Received non-JS content (likely error page or empty) for match {match_id}, url: {response.url}")
            raise ValueError(f"Invalid content received for odds URL of match {match_id}")

        game_pattern = r'var\s+game\s*=\s*Array\((.*?)\);' # Be careful with potential quotes inside
        try:
            # More robust parsing of the Array string
            game_match_str = re.search(game_pattern, content, re.DOTALL)
            if not game_match_str:
                 self.logger.warning(f"Could not find 'var game = Array(...);' data for match {match_id}")
                 raise ValueError(f"Missing 'var game' data for match {match_id}")

            # Extract content between quotes, handle escaped quotes if necessary
            # This assumes simple string format, might need refinement for complex cases
            array_content = game_match_str.group(1).strip('"')
            game_data = array_content.split('","') # Split based on delimiter ",\""

        except Exception as parse_err:
            self.logger.error(f"Error parsing 'var game' data for match {match_id}: {parse_err}")
            raise ValueError(f"Error parsing 'var game' data for match {match_id}")


        found_bookmaker_details = False # Flag if we found *any* detail pattern

        # Find gameDetail pattern once
        game_detail_pattern = r'var\s+gameDetail\s*=\s*Array\((.*?)\);'
        try:
            game_detail_match_str = re.search(game_detail_pattern, content, re.DOTALL)
            if not game_detail_match_str:
                 self.logger.warning(f"Could not find 'var gameDetail = Array(...)' data for match {match_id}")
                 # Continue, maybe game array has all info (unlikely based on docs)
                 game_details_split = [] # Empty list if not found
            else:
                 detail_array_content = game_detail_match_str.group(1).strip('"')
                 game_details_split = detail_array_content.split('","')
                 found_bookmaker_details = True # We found the details block

        except Exception as parse_err:
            self.logger.error(f"Error parsing 'var gameDetail' data for match {match_id}: {parse_err}")
            raise ValueError(f"Error parsing 'var gameDetail' data for match {match_id}")


        odds_yielded_count = 0
        processed_details = set() # Avoid processing same detail ID multiple times if structure allows

        for data_entry in game_data:
            if '|' not in data_entry: continue
            odds_info = data_entry.split('|')
            if len(odds_info) < 2: continue

            bookmaker_id = odds_info[0].strip()
            odds_detail_id = odds_info[1].strip()

            if bookmaker_id in target_bookmakers and odds_detail_id not in processed_details:
                self.logger.debug(f"Found target bookmaker {target_bookmakers[bookmaker_id]} ({bookmaker_id}) with detail ID {odds_detail_id} for match {match_id}")

                # Search for corresponding details in the parsed game_details_split
                detail_found = False
                for detail_entry in game_details_split:
                    if detail_entry.startswith(f"{odds_detail_id}^"):
                        detail_found = True
                        processed_details.add(odds_detail_id) # Mark as processed
                        self.logger.debug(f"Found matching detail entry for {odds_detail_id}")
                        try:
                            odds_data_part = detail_entry.split('^', 1)[1]
                            odds_changes = odds_data_part.split(';')

                            for change in odds_changes:
                                if not change.strip(): continue
                                parts = change.split('|')
                                if len(parts) >= 4:
                                    try:
                                        # Standardize update_time
                                        raw_update_time = parts[3].strip()
                                        update_time_str = self._standardize_update_time(raw_update_time, match_id)

                                        if update_time_str: # Only yield if time was parsed
                                            odds_item_dict = {
                                                'match_id': match_id,
                                                'odds_detail_id': odds_detail_id,
                                                'bookmaker_id': bookmaker_id,
                                                'home_win': float(parts[0]),
                                                'draw': float(parts[1]),
                                                'away_win': float(parts[2]),
                                                'update_time': update_time_str
                                            }
                                            yield odds_item_dict
                                            odds_yielded_count += 1
                                    except (ValueError, IndexError) as val_e:
                                        self.logger.warning(f"Skipping odds record due to value error for match {match_id}, detail {odds_detail_id}: {val_e}, data: '{parts}'")
                                    except Exception as item_err:
                                         self.logger.error(f"Unexpected error processing odds change item for match {match_id}, detail {odds_detail_id}: {item_err}, data: '{parts}'")
                        except Exception as detail_proc_err:
                            self.logger.error(f"Error processing details for {odds_detail_id}, match {match_id}: {detail_proc_err}")
                        break # Stop searching details once found for this odds_detail_id
                # else: (loop finished without break)
                if not detail_found and found_bookmaker_details:
                     self.logger.warning(f"Found bookmaker {bookmaker_id} ({odds_detail_id}) in 'game' but no matching entry in 'gameDetail' for match {match_id}")


        if odds_yielded_count == 0:
             # This check is now redundant due to the check in parse_odds_retry, but kept for clarity
             self.logger.warning(f"Completed parsing for match {match_id}, but yielded 0 target odds records.")
             # Exception will be raised by the caller (parse_odds_retry)

    def _standardize_update_time(self, raw_time_str, match_id):
        """Attempts to parse different time formats into 'YYYY-MM-DD HH:MM:SS'."""
        time_formats = [
            '%Y-%m-%d %H:%M',      # Standard format (sometimes used)
            '%y-%m-%d %H:%M',      # Short year
        ]
        # Handle Unix timestamp (assuming it's seconds)
        if raw_time_str.isdigit():
            try:
                timestamp = int(raw_time_str)
                # Check for reasonable timestamp range (e.g., after 2000)
                if timestamp > 946684800: # 2000-01-01 UTC
                    dt_object = datetime.fromtimestamp(timestamp)
                    return dt_object.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    self.logger.warning(f"Ignoring likely invalid Unix timestamp: {raw_time_str} for match {match_id}")
                    return None
            except ValueError: # Handle potential large numbers not fitting int
                self.logger.warning(f"Could not convert supposed timestamp '{raw_time_str}' to int for match {match_id}")
                return None

        # Handle "MM-DD HH:MM" format
        if re.match(r'^\d{2}-\d{2} \d{2}:\d{2}$', raw_time_str):
            try:
                # Assume current year - THIS MIGHT BE WRONG FOR OLD DATA
                current_year = datetime.now().year
                time_str = f"{current_year}-{raw_time_str}"
                dt_object = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                return dt_object.strftime('%Y-%m-%d %H:%M:%S')
            except ValueError as e:
                 self.logger.warning(f"Could not parse short date format '{raw_time_str}' (assuming year {current_year}) for match {match_id}: {e}")
                 return None

        # Try standard formats
        for fmt in time_formats:
            try:
                dt_object = datetime.strptime(raw_time_str, fmt)
                return dt_object.strftime('%Y-%m-%d %H:%M:%S')
            except ValueError:
                continue # Try next format

        # If no format matches
        self.logger.warning(f"Unknown update_time format '{raw_time_str}' for match {match_id}. Cannot standardize.")
        return None


    def handle_retry_error(self, failure: Failure):
        """
        Handles request failures (download errors, processing errors raised in callback).
        Updates the retry queue status accordingly.
        """
        queue_id = failure.request.meta.get('queue_id')
        match_id = failure.request.meta.get('match_id')
        attempt = failure.request.meta.get('attempt', 1) # Default attempt if missing

        if not queue_id or not match_id:
            self.logger.error(f"Critical: Could not get queue_id or match_id from failed request meta: {failure.request.meta}. Failure: {failure.value}")
            # Cannot update DB without queue_id
            return

        # Log the failure reason clearly
        error_type = failure.type.__name__
        error_value = str(failure.value)
        self.logger.error(f"Retry attempt {attempt} FAILED for queue_id={queue_id}, match_id={match_id}. Error Type: {error_type}, Reason: {error_value}")

        # Call the DB update logic
        self.handle_failed_attempt(queue_id, match_id, attempt, f"{error_type}: {error_value[:200]}") # Limit reason length

    def handle_failed_attempt(self, queue_id, match_id, attempt, reason="Unknown"):
        """Updates the database for a failed attempt."""
        if not self.db_pool:
            self.logger.error("Database pool not available in handle_failed_attempt.")
            return

        next_status = 'failed' if attempt >= self.max_attempts else 'pending'
        update_sql = sql.SQL(r"""
            UPDATE odds_retry_queue
            SET status = %s, attempts = %s, updated_at = NOW()
            WHERE id = %s;
        """)

        conn = None
        cur = None
        try:
            conn = self.db_pool.pool.getconn()
            conn.autocommit = True # Use autocommit for single update
            cur = conn.cursor()
            cur.execute(update_sql, (next_status, attempt, queue_id))
            # No commit needed with autocommit=True
            self.logger.info(f"Updated retry task status: queue_id={queue_id}, match_id={match_id}, new_status={next_status}, attempts={attempt}, reason='{reason}'")
        except Exception as e:
            self.logger.error(f"Failed to update retry task status for queue_id={queue_id}, match_id={match_id}: {e}")
            # No rollback needed with autocommit
        finally:
            if cur:
                cur.close()
            if conn:
                self.db_pool.pool.putconn(conn) # Always return connection

    def closed(self, reason):
        """Called when the spider closes."""
        if self.db_pool:
            self.db_pool.close()
            self.logger.info("OddsRetrySpider database pool closed.")
        self.logger.info(f"Odds retry spider finished. Reason: {reason}") 