import scrapy
import json
import re
import os
import logging
import time
import requests
from datetime import datetime
from urllib.parse import urlparse
from football_spider.items import TeamItem
from ..sub_league_mappings import SUB_LEAGUE_ID_MAP
import psycopg2 # 新增：数据库驱动
from scrapy.utils.project import get_project_settings # 新增：获取项目设置

class TeamSpider(scrapy.Spider):
    name = 'team'
    allowed_domains = ['titan007.com', 'zq.titan007.com']
    
    # LOGO基础URL常量
    TEAM_LOGO_BASE_URL = 'https://zq.titan007.com/Image/team/'
    
    # 本地存储路径
    # LOCAL_LOGO_PATH = '../../../oddluck_dev/backend/static/images/logos/teams' # 旧
    # LOCAL_LOGO_URL = '/static/images/logos/teams' # 旧

    # 新的基础路径，不包含 league_id 和 /teams/ - 指向 .../logos/league/
    LOCAL_LOGO_BASE_STORAGE_PATH = '../../../oddluck_dev/backend/static/images/logos/league'
    LOCAL_LOGO_BASE_URL_PATH = '/static/images/logos/league'

    # 新增：默认处理的主要联赛ID列表 (例如：五大联赛)
    DEFAULT_MAIN_LEAGUE_IDS = ['36', '34', '31', '8', '11']
    
    CURRENT_SEASON = "2024-2025"  # 可以通过参数传入
    
    custom_settings = {
        'ITEM_PIPELINES': {
            'football_spider.pipelines.TeamPipeline': 300,
        },
        'DOWNLOAD_DELAY': 1,
        'CONCURRENT_REQUESTS': 1,
        'LOG_FILE': '../spiders_log/team_spider.log',
        'LOG_LEVEL': 'INFO',
    }
    
    def __init__(self, league=None, season=None, task_id=None, *args, **kwargs):
        super(TeamSpider, self).__init__(*args, **kwargs)
        self.task_id = task_id
        # self.logger = logging.getLogger(f'{self.name}_spider') # 已移除, Scrapy会自动提供self.logger
        self.cli_provided_season = season # 存储命令行传入的season
        self.target_requests_info = []
        processed_url_parts = set() # 用于防止生成重复的URL部分
        self.league_to_country_map = {} # 新增：用于存储 league_id 到 country_id 的映射

        if league: # 如果命令行提供了 league 参数，则优先处理
            self.logger.info(f"命令行提供了 'league' 参数: '{league}'. 将优先处理此参数指定的联赛。")
            effective_season_for_cli = self.cli_provided_season if self.cli_provided_season else self.CURRENT_SEASON
            self.logger.info(f"用于命令行指定联赛的赛季: '{effective_season_for_cli}'")

            input_main_league_ids_cli = []
            if isinstance(league, list):
                input_main_league_ids_cli = [str(l_id).strip() for l_id in league if str(l_id).strip()]
            elif isinstance(league, str):
                input_main_league_ids_cli = [l_id.strip() for l_id in league.split(',') if l_id.strip()]
            elif isinstance(league, (int, float)):
                input_main_league_ids_cli = [str(league).strip()]
            else:
                self.logger.warning(f"未知的命令行 'league' 参数类型: {league}. 将尝试使用默认联赛ID列表。")
                input_main_league_ids_cli = list(self.DEFAULT_MAIN_LEAGUE_IDS)
            
            if not input_main_league_ids_cli:
                self.logger.warning("命令行 'league' 参数解析后为空，将尝试使用默认联赛ID列表。")
                input_main_league_ids_cli = list(self.DEFAULT_MAIN_LEAGUE_IDS)

            for main_league_id_str in input_main_league_ids_cli:
                main_league_url_part = f's{main_league_id_str}'
                if main_league_url_part not in processed_url_parts:
                    self.target_requests_info.append({
                        'url_league_part': main_league_url_part,
                        'league_id_for_item': main_league_id_str,
                        'sub_league_id_for_item': None,
                        'season_for_url': effective_season_for_cli
                    })
                    processed_url_parts.add(main_league_url_part)

                if main_league_id_str in SUB_LEAGUE_ID_MAP:
                    sub_ids_for_main = SUB_LEAGUE_ID_MAP[main_league_id_str]
                    if isinstance(sub_ids_for_main, list):
                        for sub_league_id_str_map in sub_ids_for_main: # Changed variable name
                            clean_sub_id = str(sub_league_id_str_map).strip()
                            if not clean_sub_id:
                                continue
                            sub_league_url_part = f's{main_league_id_str}_{clean_sub_id}'
                            if sub_league_url_part not in processed_url_parts:
                                self.target_requests_info.append({
                                    'url_league_part': sub_league_url_part,
                                    'league_id_for_item': main_league_id_str,
                                    'sub_league_id_for_item': clean_sub_id,
                                    'season_for_url': effective_season_for_cli
                                })
                                processed_url_parts.add(sub_league_url_part)
                    else:
                        self.logger.warning(f"SUB_LEAGUE_ID_MAP 中主联赛ID '{main_league_id_str}' 的值不是列表: {sub_ids_for_main}")
        
        else: # 如果命令行未提供 league 参数，则从数据库获取
            self.logger.info("命令行未提供 'league' 参数。将从数据库获取目标联赛和赛季。")
            settings = get_project_settings()
            conn = None
            db_leagues = [] # Initialize to ensure it's defined for fallback
            try:
                conn = psycopg2.connect(
                    dbname=settings.get('POSTGRESQL_DATABASE'),
                    user=settings.get('POSTGRESQL_USER'),
                    password=settings.get('POSTGRESQL_PASSWORD'),
                    host=settings.get('POSTGRESQL_HOST'),
                    port=settings.get('POSTGRESQL_PORT', '5432')
                )
                with conn.cursor() as cur:
                    sql_query = """
                        SELECT DISTINCT l.league_id, l.sub_league_id, l.current_season, l.country_id 
                        FROM leagues l
                        WHERE l.current_season IS NOT NULL AND TRIM(l.current_season) <> ''
                        ORDER BY l.league_id, l.sub_league_id;
                    """ # 查询中已加入 country_id
                    cur.execute(sql_query)
                    db_leagues = cur.fetchall()
                self.logger.info(f"数据库查询完成，获取到 {len(db_leagues)} 条原始记录。")

            except psycopg2.Error as e:
                self.logger.error(f"数据库连接或查询错误: {e}。将尝试使用默认联赛ID列表作为备用。")
                # db_leagues will remain empty, leading to fallback
                # 如果从DB失败，country_id 的映射将为空
                self.league_to_country_map.clear()

            finally:
                if conn:
                    conn.close()
                    self.logger.info("数据库连接已关闭。")

            if not db_leagues: # Fallback if DB query failed or returned no results
                self.logger.warning("数据库中未找到任何有效的联赛信息或查询失败。将尝试使用默认联赛ID列表作为备用。")
                fallback_season = self.cli_provided_season if self.cli_provided_season else self.CURRENT_SEASON
                self.logger.info(f"使用备用赛季: {fallback_season} 和默认联赛列表。")
                for main_league_id_str_default in self.DEFAULT_MAIN_LEAGUE_IDS: # Changed variable name
                    main_url_part = f's{main_league_id_str_default}'
                    if main_url_part not in processed_url_parts:
                        self.target_requests_info.append({
                            'url_league_part': main_url_part,
                            'league_id_for_item': main_league_id_str_default,
                            'sub_league_id_for_item': None,
                            'season_for_url': fallback_season
                        })
                        processed_url_parts.add(main_url_part)
                    
                    if main_league_id_str_default in SUB_LEAGUE_ID_MAP:
                        for sub_id_default in SUB_LEAGUE_ID_MAP[main_league_id_str_default]: # Changed variable name
                            clean_sub_id_default = str(sub_id_default).strip()
                            if not clean_sub_id_default: continue
                            sub_url_part_default = f's{main_league_id_str_default}_{clean_sub_id_default}'
                            if sub_url_part_default not in processed_url_parts:
                                self.target_requests_info.append({
                                    'url_league_part': sub_url_part_default,
                                    'league_id_for_item': main_league_id_str_default,
                                    'sub_league_id_for_item': clean_sub_id_default,
                                    'season_for_url': fallback_season
                                })
                                processed_url_parts.add(sub_url_part_default)
            else: # Process leagues fetched from DB
                self.logger.info(f"处理从数据库获取到的 {len(db_leagues)} 条联赛/子联赛记录。")
                for db_league_id, db_sub_league_id, db_season, db_country_id in db_leagues:
                    main_id_str = str(db_league_id).strip()
                    sub_id_str = str(db_sub_league_id).strip() if db_sub_league_id is not None and str(db_sub_league_id).strip() else None
                    season_for_url = str(db_season).strip()
                    
                    if not season_for_url:
                        self.logger.warning(f"联赛ID {main_id_str}, 子联赛ID {sub_id_str} 在数据库中的赛季为空，跳过。")
                        continue

                    # 填充 league_to_country_map
                    if main_id_str and db_country_id is not None:
                        if main_id_str not in self.league_to_country_map:
                            self.league_to_country_map[main_id_str] = str(db_country_id).strip()

                    url_part = f's{main_id_str}'
                    if sub_id_str: 
                        url_part += f'_{sub_id_str}'
                    
                    if url_part not in processed_url_parts:
                        country_id_for_item = self.league_to_country_map.get(main_id_str) # 从映射获取 country_id
                        self.target_requests_info.append({
                            'url_league_part': url_part,
                            'league_id_for_item': main_id_str,
                            'sub_league_id_for_item': sub_id_str,
                            'season_for_url': season_for_url,
                            'country_id_for_item': country_id_for_item # 添加到 target_info
                        })
                        processed_url_parts.add(url_part)
                    else:
                        self.logger.debug(f"URL部分 '{url_part}' (赛季: {season_for_url}) 已被处理，跳过。")
        
        # Final check and logging for targets
        if not self.target_requests_info:
            self.logger.error("关键错误: 初始化后未能确定任何有效的爬取目标 (target_requests_info 为空)。请检查命令行参数、数据库 'leagues' 表内容 (特别是 league_id, sub_league_id, current_season) 或 SUB_LEAGUE_ID_MAP 以及 DEFAULT_MAIN_LEAGUE_IDS 配置。")
        else:
            self.logger.info(f"爬虫初始化完成: 将为 {len(self.target_requests_info)} 个目标联赛/子联赛组合生成请求。")
        
    def get_random_user_agent(self):
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
        ]
        return user_agents[int(time.time()) % len(user_agents)]
        
    def start_requests(self):
        """开始请求"""
        if not hasattr(self, 'target_requests_info') or not self.target_requests_info:
            self.logger.warning("start_requests: self.target_requests_info 未初始化或为空，不发送任何请求。")
            return

        version = time.strftime("%Y%m%d%H", time.localtime())
        self.logger.info(f"将为 {len(self.target_requests_info)} 个目标联赛/子联赛的球队信息发送请求...")

        for target_info in self.target_requests_info:
            url_league_part = target_info.get('url_league_part')
            league_id_for_item = target_info.get('league_id_for_item')
            sub_league_id_for_item = target_info.get('sub_league_id_for_item')
            season_for_url = target_info.get('season_for_url') # 获取目标特定的赛季
            country_id_for_item_meta = target_info.get('country_id_for_item') # 获取 country_id 以传递到 meta

            if not url_league_part or not league_id_for_item or not season_for_url:
                self.logger.error(
                    f"target_info 中缺少必要字段 (url_league_part, league_id_for_item, or season_for_url): "
                    f"{target_info} - 跳过此目标"
                )
                continue

            # 1. 先请求原始 season（如 2024-2025）
            request_url_full = f'https://zq.titan007.com/jsData/matchResult/{season_for_url}/{url_league_part}.js?version={version}'
            
            self.logger.debug(
                f"准备发送球队信息请求: URL={request_url_full}, "
                f"主联赛ID(Item)={league_id_for_item}, "
                f"子联赛ID(Item)={sub_league_id_for_item}, "
                f"赛季(URL)={season_for_url}"
            )
            
            yield scrapy.Request(
                url=request_url_full,
                callback=self.parse,
                headers={'User-Agent': self.get_random_user_agent()},
                meta={
                    'league_id': league_id_for_item,
                    'sub_league_id': sub_league_id_for_item,
                    'season_for_url': season_for_url,
                    'country_id': country_id_for_item_meta,
                    'season_format': 'full',  # 标记为完整赛季格式
                    'dont_redirect': True,
                    'handle_httpstatus_list': [302]
                },
                errback=self.errback_handler
            )

            # 2. 如果 season 是 YYYY-YYYY 格式，再请求一次 YYYY 格式
            if '-' in season_for_url:
                short_season = season_for_url.split('-')[0]
                request_url_short = f'https://zq.titan007.com/jsData/matchResult/{short_season}/{url_league_part}.js?version={version}'
                yield scrapy.Request(
                    url=request_url_short,
                    callback=self.parse,
                    headers={'User-Agent': self.get_random_user_agent()},
                    meta={
                        'league_id': league_id_for_item,
                        'sub_league_id': sub_league_id_for_item,
                        'season_for_url': short_season,
                        'country_id': country_id_for_item_meta,
                        'season_format': 'short',  # 标记为短赛季格式
                        'dont_redirect': True,
                        'handle_httpstatus_list': [302]
                    },
                    errback=self.errback_handler
                )
            
    def download_logo(self, url, team_id, league_id):
        """下载Logo并存储到本地"""
        try:
            if not url or not league_id: # 确保 league_id 也存在
                self.logger.warning(f"下载Logo中止：URL ({url}) 或 league_id ({league_id}) 为空或无效。")
                return None
                
            # 解析URL
            parsed_url = urlparse(url)
            original_path = parsed_url.path
            original_file_name = os.path.basename(original_path)
            
            # 如果URL中没有扩展名，添加默认扩展名
            if not os.path.splitext(original_file_name)[1]:
                original_file_name = f"{original_file_name}.png" # 假设 .png
                
            # 重命名为有意义的文件名
            new_file_name = f"team_{team_id}_{original_file_name}"
                
            # 动态构造存储目录和URL路径
            # 目标: ../../../oddluck_dev/backend/static/images/logos/league/{league_id}/teams/
            league_specific_logo_dir = os.path.join(self.LOCAL_LOGO_BASE_STORAGE_PATH, str(league_id), 'teams')
            # 目标: /static/images/logos/league/{league_id}/teams/
            league_specific_logo_url_dir = f"{self.LOCAL_LOGO_BASE_URL_PATH}/{str(league_id)}/teams"
            
            # 确保目录存在
            os.makedirs(league_specific_logo_dir, exist_ok=True)
            
            # 本地完整路径
            local_file_path = os.path.join(league_specific_logo_dir, new_file_name)
            
            # 只下载不存在的文件
            if not os.path.exists(local_file_path):
                headers = {
                    'User-Agent': self.get_random_user_agent(), # 保持随机User-Agent
                    'Referer': 'https://zq.titan007.com/' # 添加Referer头
                }
                response = requests.get(url, stream=True, timeout=10, headers=headers) # 添加headers
                if response.status_code == 200:
                    with open(local_file_path, 'wb') as f:
                        for chunk in response.iter_content(1024):
                            f.write(chunk)
                    self.logger.info(f"下载Logo成功: {url} -> {local_file_path}")
                else:
                    self.logger.error(f"下载Logo失败: {url}, 状态码: {response.status_code}")
                    if response.status_code == 442:
                        try:
                            self.logger.error(f"Logo下载 442 错误响应头: {response.headers}") # 记录响应头
                        except Exception as headers_err:
                            self.logger.error(f"记录442响应头时出错: {headers_err}")
                        try:
                            # 尝试以文本形式记录，如果响应是文本的话
                            self.logger.error(f"Logo下载 442 错误响应内容 (text): {response.text[:500]}") 
                        except Exception as text_err:
                            self.logger.error(f"记录442响应文本时出错: {text_err}")
                        try:
                            # 也可以尝试记录原始字节串，以防不是文本
                            self.logger.error(f"Logo下载 442 错误响应内容 (bytes): {response.content[:500]}")
                        except Exception as content_err:
                             self.logger.error(f"记录442响应字节串时出错: {content_err}")
                    return None # 确保在下载失败时返回 None
            else:
                self.logger.info(f"Logo已存在，跳过下载: {local_file_path}")
                
            # 返回相对URL路径
            return f"{league_specific_logo_url_dir}/{new_file_name}"
                
        except requests.exceptions.RequestException as req_e:
            self.logger.error(f"下载Logo {url} 时发生网络请求错误: {req_e}")
            return None
        except Exception as e:
            self.logger.error(f"下载Logo {url} 时发生未知错误: {str(e)}", exc_info=True)
            return None
            
    def parse(self, response):
        """解析球队信息"""
        meta = response.meta
        league_id = meta.get('league_id')
        sub_league_id = meta.get('sub_league_id')
        season_for_item = meta.get('season_for_url') # 获取传递过来的赛季信息
        country_id_from_meta = meta.get('country_id') # 获取 country_id (将在后续步骤中填充meta)
        
        if not league_id:
            self.logger.error(f"解析错误: 缺少 'league_id'，无法解析球队信息。URL: {response.url}")
            return
        if not season_for_item: # 确保赛季信息也存在
            self.logger.error(f"解析错误: 缺少 'season_for_url'，无法为球队信息确定赛季。URL: {response.url}")
            return
            
        try:
            js_content = response.text
            
            arr_team_match = re.search(r'var arrTeam\s*=\s*(\[.*?\]);', js_content, re.DOTALL)
            if not arr_team_match:
                self.logger.info(f"在响应中未找到 'arrTeam' 数据。这对于某些联赛/赛季可能是正常的。URL: {response.url}")
                return
                
            arr_team_str = arr_team_match.group(1)
            cleaned_str = arr_team_str.replace("'", '"') 
            cleaned_str = re.sub(r',\s*\]', ']', cleaned_str) 
            cleaned_str = re.sub(r'//.*', '', cleaned_str) 
            
            try:
                arr_team_data = json.loads(cleaned_str)
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析错误: {str(e)}. URL: {response.url}")
                self.logger.debug(f"发生JSON错误的 cleaned_str (前500字符): {cleaned_str[:500]}")
                return
                
            if not isinstance(arr_team_data, list):
                self.logger.warning(f"'arrTeam' 解析后不是一个列表。URL: {response.url}. 数据类型: {type(arr_team_data)}")
                return

            team_count = 0
            # 实际 arrTeam 结构: [[team_id, name_s, name_c, name_e, "", logo_path, 0], ...]
            for team_data_entry in arr_team_data:
                # team_data_entry 直接是 [team_id, name_s, ...] 这样的列表
                if isinstance(team_data_entry, list) and len(team_data_entry) >= 6: # 确保至少有6个元素以安全访问索引5 (logo)
                    team_item = TeamItem()
                    try:
                        # team_id 在源数据中可能是数字或字符串，数据库是 VARCHAR(50)
                        # 直接使用字符串形式，或者如果确定是数字型字符串，可以先转为字符串
                        team_id_val = str(team_data_entry[0]).strip() 
                        if not team_id_val:
                            self.logger.error(f"team_id 为空或无效. URL: {response.url}. 跳过此球队: {team_data_entry}")
                            continue
                        team_item['team_id'] = team_id_val # 保持为字符串，以匹配数据库VARCHAR类型
                    except IndexError:
                        self.logger.error(f"解析team_id时发生IndexError. URL: {response.url}. 数据: {team_data_entry}. 跳过此球队.")
                        continue
                    except Exception as e: # 捕获其他可能的转换或访问错误
                        self.logger.error(f"处理team_id时发生未知错误: {e}. URL: {response.url}. 数据: {team_data_entry}. 跳过此球队.")
                        continue

                    team_item['team_name_simp'] = team_data_entry[1]
                    team_item['team_name_cant'] = team_data_entry[2]
                    team_item['team_name_en'] = team_data_entry[3]
                    # team_data_entry[4] 是空字符串 "", 可以忽略

                    team_item['league_id'] = int(league_id) 
                    team_item['sub_league_id'] = int(sub_league_id) if sub_league_id and sub_league_id != 'None' else None
                    team_item['season'] = season_for_item
                    team_item['country_id'] = country_id_from_meta # 使用从 meta 传来的 country_id

                    # Logo 路径在索引 5
                    logo_path_segment_raw = team_data_entry[5]
                    if logo_path_segment_raw: 
                        logo_path_segment = str(logo_path_segment_raw).strip()
                        if logo_path_segment:
                            # TEAM_LOGO_BASE_URL is 'https://zq.titan007.com/Image/team/'
                            # logo_path_segment is 'images/...' or 'images/team_id/...'
                            # 拼接后形成 'https://zq.titan007.com/Image/team/images/...'
                            logo_url = f"{self.TEAM_LOGO_BASE_URL}{logo_path_segment}"
                            # 调用 download_logo 时需要 league_id (从meta获取的那个主联赛ID)
                            if league_id: # 确保 league_id (从meta获取的，字符串类型) 有效
                                team_item['team_logo'] = self.download_logo(logo_url, team_item['team_id'], league_id)
                            else:
                                self.logger.warning(f"无法下载球队 {team_item['team_id']} 的logo，因为 league_id 为空。")
                                team_item['team_logo'] = None
                        else:
                            team_item['team_logo'] = None
                    else:
                        team_item['team_logo'] = None
                        
                    team_item['created_at'] = datetime.now()
                    team_item['updated_at'] = datetime.now()
                    
                    yield team_item
                    team_count += 1
                else:
                    self.logger.warning(f"arrTeam 条目格式不正确或元素不足 (预期列表且长度>=6): {team_data_entry}. URL: {response.url}")
            
            if team_count > 0:
                self.logger.info(f"成功从URL {response.url} 提取并处理了 {team_count} 个球队。")
            else:
                self.logger.info(f"未能从URL {response.url} 提取任何球队数据 (arrTeam数据为空或所有条目格式不符)。")

        except Exception as e:
            self.logger.error(f"解析球队信息时发生未预料的错误: {str(e)}. URL: {response.url}", exc_info=True)
            
    def errback_handler(self, failure):
        """处理请求失败的情况"""
        request = failure.request
        self.logger.error(f"请求失败: {request.url}, 错误: {str(failure.value)}")
        
    def closed(self, reason):
        """爬虫关闭时的回调"""
        self.logger.info(f"爬虫关闭，原因: {reason}")
        if self.task_id:
            self.logger.info(f"任务ID: {self.task_id} 完成") 