import os
import sys
import requests
import json
from datetime import datetime
import logging
import time

class Spider<PERSON>ogger:
    """爬虫日志更新工具类，用于向Django后端API发送状态更新"""
    
    def __init__(self, task_id=None):
        """
        初始化日志记录器
        :param task_id: 任务ID，如果为None则不进行日志记录
        """
        self.task_id = task_id
        self.api_url = 'http://localhost:8000/api/spider-logs'
        self.logger = logging.getLogger('SpiderLogger')
        
        # 检查任务是否存在
        if task_id:
            try:
                response = requests.get(f'{self.api_url}/{task_id}/status/')
                if response.status_code == 200:
                    self.logger.info(f'成功连接到任务ID: {task_id}')
                    # 如果任务状态为暂停(paused)，将其更新为运行中(running)
                    data = response.json()
                    if data.get('status') == 'paused':
                        self.update_status(status='running', message='任务已恢复运行')
                else:
                    self.logger.error(f'任务ID {task_id} 不存在')
                    self.task_id = None
            except Exception as e:
                self.logger.error(f'连接任务API失败: {str(e)}')
    
    def update_status(self, progress=None, status=None, message=None):
        """
        更新爬虫任务状态
        :param progress: 进度(0-100)
        :param status: 状态(running, completed, failed, paused)
        :param message: 状态消息
        """
        if not self.task_id:
            return False
            
        update_data = {}
        if progress is not None:
            update_data['progress'] = progress
        if status is not None:
            update_data['status'] = status
        if message is not None:
            update_data['message'] = message
            
        if not update_data:
            return False
            
        try:
            response = requests.patch(
                f'{self.api_url}/{self.task_id}/',
                json=update_data
            )
            if response.status_code == 200:
                self.logger.debug(f'状态更新成功: {update_data}')
                return True
            else:
                self.logger.error(f'状态更新失败: {response.status_code} {response.text}')
                return False
        except Exception as e:
            self.logger.error(f'状态更新异常: {str(e)}')
            return False
    
    def add_log(self, message):
        """
        添加日志内容，不更新状态
        :param message: 日志消息
        """
        if not self.task_id or not message:
            return False
            
        try:
            # 获取当前日志内容
            response = requests.get(f'{self.api_url}/{self.task_id}/status/')
            if response.status_code != 200:
                self.logger.error(f'获取日志内容失败: {response.status_code}')
                return False
                
            data = response.json()
            log_content = data.get('log_content', '')
            
            # 添加时间戳和新消息
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            new_log = f"{log_content}\n[{timestamp}] {message}" if log_content else f"[{timestamp}] {message}"
            
            # 更新日志内容
            update_response = requests.patch(
                f'{self.api_url}/{self.task_id}/',
                json={'log_content': new_log}
            )
            
            if update_response.status_code == 200:
                self.logger.debug(f'日志添加成功: {message}')
                return True
            else:
                self.logger.error(f'日志添加失败: {update_response.status_code}')
                return False
        except Exception as e:
            self.logger.error(f'日志添加异常: {str(e)}')
            return False
    
    def check_if_terminated(self):
        """
        检查任务是否被终止或暂停
        :return: 如果任务被暂停或终止，返回True，否则返回False
        """
        if not self.task_id:
            return False
            
        try:
            response = requests.get(f'{self.api_url}/{self.task_id}/status/', timeout=3)
            if response.status_code == 200:
                data = response.json()
                status = data.get('status')
                message = data.get('message', '')
                log_content = data.get('log_content', '')
                
                # 如果状态为failed，说明任务被终止
                if status == 'failed':
                    self.logger.info(f'任务已被用户终止 (状态: {status})，收到的信息: {message}')
                    return True
                
                # 如果状态为paused，说明任务被暂停，应该立即返回True以停止爬虫
                if status == 'paused':
                    self.logger.info(f'任务已被用户暂停 (状态: {status})，收到的信息: {message}')
                    return True
            else:
                self.logger.warning(f'获取任务状态失败: {response.status_code}')
        except Exception as e:
            self.logger.error(f'检查任务状态时出错: {str(e)}')
            
        return False
    
    def complete(self, success=True, message=None):
        """
        完成爬虫任务
        :param success: 是否成功完成
        :param message: 完成消息
        """
        status = 'completed' if success else 'failed'
        progress = 100
        
        if not message:
            message = '爬虫任务成功完成' if success else '爬虫任务失败'
            
        self.add_log(message)
        return self.update_status(
            status=status,
            progress=progress,
            message=message
        ) 