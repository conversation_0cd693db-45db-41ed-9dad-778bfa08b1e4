import scrapy
import json
import re
import time
import random
from datetime import datetime
from urllib.parse import urljoin, urlparse
from ..items import MatchI<PERSON>, OddsItem
from scrapy import Spider, Request
import logging
import os
import sys
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import ast
import pytz

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.append(project_root)

class MatchSpider(Spider):
    name = 'match'
    allowed_domains = ['titan007.com']
    
    # 五大联赛ID映射
    LEAGUE_IDS = {
        '36': '英超',
        '34': '意甲', 
        '31': '西甲',
        '8': '德甲',
        '11': '法甲'
    }

    # 联赛名称到ID的映射
    LEAGUE_NAMES = {
        '英超': '36',
        '意甲': '34',
        '西甲': '31',
        '德甲': '8',
        '法甲': '11'
    }
    
    # 子联赛ID映射（如果有的话）
    SUB_LEAGUE_IDS = {
        '意甲': '2948'
    }

    # 博彩公司ID映射
    BOOKMAKER_IDS = {
        '281': 'Bet365',
        '115': 'William Hill',
        '82': 'Ladbrokes',
    }
    
    custom_settings = {
        'CONCURRENT_REQUESTS': 1,
        'DOWNLOAD_DELAY': 8,
        'RETRY_TIMES': 5,
        'RETRY_HTTP_CODES': [500, 502, 503, 504, 522, 524, 408, 429, 403],
    }
    
    def __init__(self, league='英超', season='2024-2025', round=None, sub_league_id=None, task_id=None, *args, **kwargs):
        """
        初始化爬虫参数
        :param league: 联赛名称（默认：英超）或联赛ID
        :param season: 赛季（默认：2024-2025）
        :param round: 指定轮次（可选）
        :param sub_league_id: 子联赛ID（可选，如果不提供且联赛有子联赛，会自动设置）
        :param task_id: 任务ID（用于更新爬虫状态）
        """
        super(MatchSpider, self).__init__(*args, **kwargs)
        
        # 保存任务ID
        self.task_id = task_id
        
        # 初始化任务日志记录器
        if task_id:
            try:
                from ..utils import SpiderLogger
                self.task_logger = SpiderLogger(task_id)
                self.logger.info(f'已初始化任务日志记录器，任务ID: {task_id}')
            except Exception as e:
                self.logger.error(f'初始化任务日志记录器失败: {str(e)}')
                self.task_logger = None
        else:
            self.task_logger = None
        
        # 判断输入的是联赛名称还是联赛ID
        if league in self.LEAGUE_NAMES:
            # 输入的是联赛名称
            self.league_name = league
            self.league = self.LEAGUE_NAMES[league]
            self.logger.info(f'通过联赛名称 "{league}" 找到联赛ID: {self.league}')
        elif league in self.LEAGUE_IDS:
            # 输入的是联赛ID
            self.league = league
            self.league_name = self.LEAGUE_IDS[league]
            self.logger.info(f'使用联赛ID: {league}，对应联赛: {self.league_name}')
        else:
            # 未知的联赛，使用默认值
            self.league_name = '英超'
            self.league = '36'
            self.logger.warning(f'未知的联赛 "{league}"，使用默认联赛: 英超(36)')
        
        self.season = season
        self.target_round = round

        # 自动设置子联赛ID（如果有）
        if not sub_league_id and self.league_name in self.SUB_LEAGUE_IDS:
            self.sub_league_id = self.SUB_LEAGUE_IDS[self.league_name]
            self.logger.info(f'自动设置 {self.league_name} 的子联赛ID: {self.sub_league_id}')
        else:
            self.sub_league_id = sub_league_id or ''
        
        self.match_ids = set()  # 添加一个集合来存储需要处理的比赛ID
        self.processed_match_count = 0
        self.total_match_count = 0
        
        # 用于记录任务进度
        self.start_time = datetime.now()

        self.logger.info(f'爬虫初始化完成:')
        self.logger.info(f'联赛: {self.league_name}(ID:{self.league})')
        self.logger.info(f'赛季: {self.season}')
        self.logger.info(f'目标轮次: {self.target_round if self.target_round else "全部轮次"}')
        self.logger.info(f'子联赛ID: {self.sub_league_id if self.sub_league_id else "无"}')
        
        if self.task_id:
            self.logger.info(f'任务ID: {self.task_id}')
            self.update_task_status(0, f'爬虫初始化完成，准备获取{self.league_name} {self.season}赛季数据')
        
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ]

    def update_task_status(self, progress, message=''):
        """更新任务状态"""
        if not self.task_id:
            return
            
        try:
            # 调用Django API更新任务状态
            log_entry = message
            url = f"http://localhost:8000/api/spider-logs/{self.task_id}/update-status/"
            data = {
                'progress': progress,
                'message': message,
                'log_entry': log_entry
            }
            
            # 使用requests库发送POST请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(url, json=data, headers=headers, timeout=5)
            
            if response.status_code == 200:
                self.logger.debug(f"更新任务状态成功: {progress}% - {message}")
            else:
                self.logger.warning(f"更新任务状态返回错误: HTTP {response.status_code}, {response.text}")
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {str(e)}")
            # 异常不应影响爬虫的正常运行，所以这里只记录错误而不抛出异常

    def get_random_user_agent(self):
        return random.choice(self.user_agents)
    
    def generate_urls(self):
        """根据配置生成所有需要爬取的URL"""
        urls = []
        version = time.strftime("%Y%m%d%H", time.localtime())
        
        # 检查是否有子联赛
        if self.sub_league_id:
            # 有子联赛，生成子联赛URL
            urls.append({
                'url': f'https://zq.titan007.com/jsData/matchResult/{self.season}/s{self.league}_{self.sub_league_id}.js?version={version}',
                'league_id': self.league,
                'sub_league_id': self.sub_league_id
            })
        else:
            # 没有子联赛，生成主联赛URL
            urls.append({
                'url': f'https://zq.titan007.com/jsData/matchResult/{self.season}/s{self.league}.js?version={version}',
                'league_id': self.league,
                'sub_league_id': ''
            })
        
        return urls
    
    def start_requests(self):
        """开始请求"""
        self.logger.info(f'开始爬取 {self.season} 赛季 {self.league_name} 的比赛数据')
        self.update_task_status(5, f'开始爬取 {self.season} 赛季 {self.league_name} 的比赛数据')
        
        for url_info in self.generate_urls():
            self.logger.info(f'请求URL: {url_info["url"]}')
            self.update_task_status(10, f'正在获取联赛数据: {url_info["url"]}')
            yield Request(
                url=url_info['url'],
                callback=self.parse_match,
                headers={
                    'User-Agent': self.get_random_user_agent(),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                meta={
                    'league_id': url_info['league_id'],
                    'sub_league_id': url_info['sub_league_id']
                },
                dont_filter=True
            )

    def get_odds_url_with_selenium(self, match_id):
        """使用 Selenium 获取赔率 URL"""
        odds_list_url = f'https://1x2.titan007.com/oddslist/{match_id}.htm'
        odds_url = None
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument(f'user-agent={self.get_random_user_agent()}')
        
        try:
            # !!! 指定手动下载的 ChromeDriver 路径 (假设在项目根目录) !!!
            # 修正路径计算：需要上溯三层目录到项目根目录
            chromedriver_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'chromedriver.exe')
            driver = webdriver.Chrome(service=Service(executable_path=chromedriver_path), options=chrome_options) # 使用指定路径
            
            # 访问页面
            driver.get(odds_list_url)
            
            # 等待页面加载完成
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待一段时间让 JavaScript 执行完成
            time.sleep(5)
            
            # 获取页面源码
            page_source = driver.page_source
            
            # 使用正则表达式查找赔率 URL
            url_pattern = f'https://1x2d\.titan007\.com/{match_id}\.js\?r=[0-9]+'
            match = re.search(url_pattern, page_source)
            
            if match:
                odds_url = match.group(0)
                self.logger.info(f'找到目标赔率URL: {odds_url}')
            else:
                # 尝试从 script 标签中查找
                script_elements = driver.find_elements(By.TAG_NAME, "script")
                for script in script_elements:
                    src = script.get_attribute("src") or ""
                    if '1x2d.titan007.com' in src and f'{match_id}.js' in src:
                        odds_url = src
                        self.logger.info(f'从script标签中找到目标赔率URL: {odds_url}')
                        break
                        
                if not odds_url:
                    # 尝试执行 JavaScript 来获取 URL
                    try:
                        js_code = f'''
                        var scripts = document.getElementsByTagName('script');
                        for(var i = 0; i < scripts.length; i++) {{
                            var src = scripts[i].src;
                            if(src.includes('1x2d.titan007.com') && src.includes('{match_id}.js')) {{
                                return src;
                            }}
                        }}
                        return null;
                        '''
                        odds_url = driver.execute_script(js_code)
                        if odds_url:
                            self.logger.info(f'从JavaScript执行中找到目标赔率URL: {odds_url}')
                    except Exception as e:
                        self.logger.error(f"执行JavaScript时出错: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"获取赔率URL时出错: {str(e)}")
        finally:
            try:
                driver.quit()
            except:
                pass
        
        if not odds_url:
            self.logger.warning(f"未找到比赛ID {match_id} 的赔率URL")
            
        return odds_url

    def process_match_odds(self, match_item):
        """处理比赛赔率数据"""
        match_id = match_item["match_id"]
        
        try:
            # 先检查任务是否被暂停或终止
            if hasattr(self, 'task_logger') and self.task_logger:
                if self.task_logger.check_if_terminated():
                    self.logger.info(f'任务已被用户暂停或终止，跳过处理比赛ID {match_id} 的赔率数据')
                    return None
            
            # 使用 Selenium 获取赔率 URL
            odds_url = self.get_odds_url_with_selenium(match_id)
            
            if odds_url:
                self.logger.info(f'自动获取比赛ID {match_id} 的赔率URL: {odds_url}')
                return Request(
                    url=odds_url,
                    callback=self.parse_odds,
                    meta={
                        'match_id': match_id
                    },
                    dont_filter=True
                )
            else:
                self.logger.warning(f'未找到比赛ID {match_id} 的赔率URL')
                
        except Exception as e:
            self.logger.error(f'获取赔率URL时出错: {str(e)}')
        
        return None

    async def parse_match(self, response):
        """解析比赛信息"""
        try:
            content = response.text
            league_id = response.meta.get('league_id', '')
            sub_league_id = response.meta.get('sub_league_id', '')
            logger = self.logger # 定义 logger
            
            # 检查任务是否被终止
            if hasattr(self, 'task_logger') and self.task_logger and self.task_logger.check_if_terminated():
                logger.info('任务已被用户终止，停止处理')
                return
            
            # 获取通过 -a 传递的目标 match_id 参数
            target_id_to_crawl = getattr(self, 'target_match_id', None)
            if target_id_to_crawl:
                logger.info(f"目标爬取赛事ID已设置: {target_id_to_crawl}")
            else:
                logger.info("未设置目标爬取赛事ID，将处理所有赛事。")
            
            # 获取当前轮次
            current_round = None
            
            # 如果有子联赛ID，优先从arrSubLeague中获取轮次信息
            if sub_league_id:
                sub_league_match = re.search(r'var\s+arrSubLeague\s*=\s*\[(.*?)\];', content, re.DOTALL)
                if sub_league_match:
                    sub_league_data_str = sub_league_match.group(1)
                    # 使用更安全的解析方式处理可能的JS数组格式
                    try:
                        sub_league_data = ast.literal_eval(f'[{sub_league_data_str}]')
                        if len(sub_league_data) > 6:
                            current_round = sub_league_data[6]
                            logger.info(f'从子联赛数据中获取当前轮次: {current_round}')
                    except Exception as e_sub:
                        logger.warning(f"解析 arrSubLeague 失败: {e_sub}")
            
            # 如果没有子联赛或未找到轮次信息，则从arrLeague中获取
            if not current_round:
                league_match = re.search(r'var\s+arrLeague\s*=\s*\[(.*?)\];', content, re.DOTALL)
                if league_match:
                    league_data_str = league_match.group(1)
                    try:
                        league_data = ast.literal_eval(f'[{league_data_str}]')
                        if len(league_data) > 8:
                            current_round = league_data[8]
                            logger.info(f'从联赛数据中获取当前轮次: {current_round}')
                    except Exception as e_league:
                        logger.warning(f"解析 arrLeague 失败: {e_league}")
            
            if not current_round:
                logger.warning(f'未找到当前轮次信息，跳过处理')
                return
            
            # 解析比赛信息
            matches_pattern = r'jh\["R_(\d+)"\]\s*=\s*\[(.*?)\];'
            
            # 计算总比赛数量
            total_matches = {}
            total_count = 0
            # 存储所有解析到的比赛数据
            all_parsed_matches = []
            
            for round_num, matches_str in re.findall(matches_pattern, content):
                # !!! 添加轮次过滤 !!!
                if self.target_round and round_num != self.target_round:
                    # logger.debug(f"跳过轮次: {round_num} (目标轮次: {self.target_round})")
                    continue # 如果设置了目标轮次且当前轮次不匹配，则跳过
                
                # 如果匹配或未设置目标轮次，则处理当前轮次
                logger.info(f"找到目标轮次 {round_num}，开始解析数据...")
                processed_count_in_round = 0
                total_in_round = 0
                
                try:
                    # !!! 添加日志，打印原始 matches_str !!!
                    logger.info(f"尝试解析轮次 {round_num} 的原始数据 (前500字符): {matches_str[:500]}")
                    
                    # !!! 加强预处理 !!!
                    # 替换 JS null 为 Python None
                    processed_matches_str = matches_str.replace('null', 'None') 
                    # 替换 JS 中可能出现的 ,, 为 ,None, (处理缺失值)
                    processed_matches_str = processed_matches_str.replace(',,', ',None,') 
                    # 再次替换可能产生的 ,None,None, 情况 (如果原本是 ,,,)
                    processed_matches_str = processed_matches_str.replace(',None,None,', ',None,None,') # 这个可能不需要，但以防万一
                    # 替换 [, 为 [None, 和 ,] 为 ,None] (处理开头/结尾的缺失值)
                    processed_matches_str = processed_matches_str.replace('[,', '[None,')
                    processed_matches_str = processed_matches_str.replace(',]', ',None]')
                    # 你可能还需要根据具体错误添加其他替换，例如 undefined
                    
                    logger.info(f"预处理后轮次 {round_num} 的数据 (前500字符): {processed_matches_str[:500]}")
                    
                    matches_data = ast.literal_eval(f'[{processed_matches_str}]')
                    # !!! 修改：直接获取比赛数量 !!!
                    total_in_round = len(matches_data)
                    # total_count += total_in_round # 更新总数（可能只处理一轮，在循环内更新）
                    logger.info(f"轮次 {round_num}: 成功解析，找到 {total_in_round} 场比赛。")
                    
                    # --- 直接在这里处理解析出的比赛数据 --- 
                    match_item = None # 初始化以防循环为空
                    # !!! 修改：直接遍历比赛列表 !!!
                    # step = 16 # 不再需要 step
                    # for i in range(0, len(matches_data), step):
                    for match_data in matches_data:
                        # if i + step > len(matches_data):
                        #     logger.warning(f"轮次 {round_num} 比赛数据长度不足，跳过末尾部分。索引: {i}")
                        #     break
                        # match_data = matches_data[i : i + step] # 直接使用循环变量 match_data
                        
                        # !!! 添加单场比赛数据长度检查 !!!
                        # 假设每场比赛至少需要 16 个字段来获取基本信息
                        if not isinstance(match_data, list) or len(match_data) < 16:
                            logger.warning(f"轮次 {round_num}: 单场比赛数据格式错误或字段不足 ({len(match_data if isinstance(match_data, list) else [])} < 16)，跳过。数据: {str(match_data)[:100]}...")
                            continue
                            
                        try:
                            match_item = MatchItem()
                            current_match_id = str(match_data[0]) # 假设比赛ID在第一个位置
                            
                            # !!! 目标 ID 过滤 !!!
                            if target_id_to_crawl and current_match_id != target_id_to_crawl:
                                continue # 如果设置了目标ID且当前ID不匹配，则跳过
                            
                            # 如果匹配或未设置目标，则继续处理
                            logger.info(f"正在处理比赛ID: {current_match_id}")
                            
                            match_item['match_id'] = current_match_id
                            match_item['league_id'] = league_id
                            match_item['league_name'] = self.league_name
                            match_item['season'] = self.season
                            match_item['home_team_id'] = str(match_data[4])
                            match_item['away_team_id'] = str(match_data[5])
                            
                            # --- 修正时间解析和字段赋值 --- 
                            match_time_str = str(match_data[3])
                            try:
                                naive_dt = datetime.strptime(match_time_str, '%Y-%m-%d %H:%M')
                                tz = pytz.timezone('Asia/Shanghai')
                                match_item['match_time'] = tz.localize(naive_dt)
                            except Exception as time_e:
                                logger.error(f"解析比赛 {current_match_id} 时间戳 '{match_time_str}' 失败: {time_e}")
                                match_item['match_time'] = None
                                    
                            # 状态在索引 2, 假设 -1 是完场, 其他是数字状态码
                            try:
                                match_item['status'] = int(match_data[2]) if match_data[2] is not None else None
                            except (ValueError, IndexError) as status_e:
                                logger.error(f"解析比赛 {current_match_id} 状态失败: {status_e}. 原始值: {match_data[2] if len(match_data)>2 else 'N/A'}")
                                match_item['status'] = None
                               
                            # 解析全场比分 (索引 6)
                            full_score_str = match_data[6]
                            if isinstance(full_score_str, str) and '-' in full_score_str:
                                try:
                                    hs, aws = full_score_str.split('-')
                                    match_item['home_score'] = hs.strip()
                                    match_item['away_score'] = aws.strip()
                                except ValueError:
                                    logger.warning(f"比赛 {current_match_id}: 无法解析全场比分 '{full_score_str}'")
                                    match_item['home_score'] = None
                                    match_item['away_score'] = None
                            else:
                                match_item['home_score'] = None
                                match_item['away_score'] = None
                               
                            # 解析半场比分 (索引 7)
                            half_score_str = match_data[7]
                            if isinstance(half_score_str, str) and '-' in half_score_str:
                                try:
                                    hhs, ahs = half_score_str.split('-')
                                    match_item['home_half_score'] = hhs.strip()
                                    match_item['away_half_score'] = ahs.strip()
                                except ValueError:
                                    logger.warning(f"比赛 {current_match_id}: 无法解析半场比分 '{half_score_str}'")
                                    match_item['home_half_score'] = None
                                    match_item['away_half_score'] = None
                            else:
                                match_item['home_half_score'] = None
                                match_item['away_half_score'] = None
                               
                            # 轮次来自外层循环变量 round_num
                            try:
                                match_item['round'] = int(round_num)
                            except ValueError:
                                logger.warning(f"比赛 {current_match_id}: 轮次 '{round_num}' 不是有效整数")
                                match_item['round'] = None
                               
                            # group 信息不确定，暂时设为 None
                            match_item['group'] = None # match_data[15] if match_data[15] is not None else None 
                            
                            yield match_item
                            total_count += 1 # 在成功 yield MatchItem 后增加总计数
                            processed_count_in_round += 1
                        
                            # !!! 修正：将赔率处理移入 try 块内部 !!!
                            # 处理赔率数据
                            odds_request = self.process_match_odds(match_item)
                            if odds_request:
                                yield odds_request
                            
                                # 如果是目标爬取模式，处理完一个就结束
                                if target_id_to_crawl:
                                    logger.info(f"已处理目标赛事ID: {target_id_to_crawl}，结束处理轮次 {round_num}。")
                                    break # 跳出内层比赛循环
                                
                        except (ValueError, IndexError, TypeError) as e:
                            logger.error(f"处理比赛记录 (match_id: {match_data[0] if len(match_data)>0 else '未知'}) 时出错: {e}. 数据: {match_data}")
                        except Exception as e_match:
                            logger.error(f"处理比赛记录 (match_id: {match_data[0] if len(match_data)>0 else '未知'}) 时发生未知错误: {e_match}. 数据: {match_data}")
                            import traceback
                            logger.error(traceback.format_exc())
                            
                    # --- 处理结束 --- 
                    # 如果是目标轮次爬取 或 目标ID爬取，处理完这一轮(或这一个)就结束
                    if self.target_round or target_id_to_crawl:
                        logger.info(f"已完成目标轮次 {round_num} 或目标赛事的处理。")
                        break # 跳出外层轮次循环
                        
                except Exception as e_parse:
                    logger.error(f"解析轮次 {round_num} 的比赛数据时出错: {e_parse}")
                    logger.error(f"导致错误的轮次 {round_num} 的原始数据 (前500字符): {matches_str[:500]}")
                    if self.target_round:
                         logger.error(f"目标轮次 {self.target_round} 解析失败，停止爬虫。")
                         break
            
            # logger.info(f'共找到 {total_count} 场比赛分布在 {len(total_matches)} 个轮次中') # 这行现在可能不准确了
            logger.info("比赛列表解析阶段完成。")
            # 更新进度信息
            # ... (更新进度的逻辑可能需要调整，因为不一定处理所有比赛) ...
                
        except Exception as e:
            logger.error(f'解析比赛列表时发生严重错误: {str(e)}')
            import traceback
            logger.error(traceback.format_exc())
            
            self.update_task_status(
                self.processed_match_count * 100 // (self.total_match_count or 1), 
                f'处理数据时发生错误: {str(e)}'
            )
            
            if not response.meta.get("retried", False):
                yield Request(
                    response.url,
                    callback=self.parse_match,
                    meta={**response.meta, "retried": True},
                    dont_filter=True
                )

    def parse_odds(self, response):
        """解析赔率信息"""
        try:
            match_id = response.meta['match_id']
            content = response.text
            
            self.logger.info(f'正在解析比赛ID: {match_id} 的赔率数据')
            
            # 定义目标博彩公司ID
            target_bookmakers = {
                '281': 'Bet365',
                '82': '立博',
                '115': '威廉希尔'
            }
            
            # 解析var game数据
            game_pattern = r'var\s+game\s*=\s*Array\("(.*?)"\);'
            game_match = re.search(game_pattern, content)
            
            if game_match:
                self.logger.info('成功匹配var game数据')
                game_data = game_match.group(1).split('","')
                
                # 遍历所有数据项，查找目标博彩公司
                for data in game_data:
                    if '|' not in data:
                        continue
                        
                    odds_info = data.split('|')
                    if len(odds_info) < 2:
                        continue
                        
                    bookmaker_id = odds_info[0]
                    odds_detail_id = odds_info[1]
                    
                    # 检查是否是目标博彩公司
                    if bookmaker_id in target_bookmakers:
                        self.logger.info(f'找到目标博彩公司: {target_bookmakers[bookmaker_id]}')
                        self.logger.info(f'对应的欧赔详细数据ID: {odds_detail_id}')
                        
                        # 解析var gameDetail数据
                        game_detail_pattern = r'var\s+gameDetail\s*=\s*Array\("(.*?)"\);'
                        game_detail_match = re.search(game_detail_pattern, content)
                        
                        if game_detail_match:
                            game_details = game_detail_match.group(1).split('","')
                            
                            # 查找对应的赔率详情
                            for detail in game_details:
                                if detail.startswith(f"{odds_detail_id}^"):
                                    odds_data = detail.split('^')[1]
                                    odds_changes = odds_data.split(';')
                                    
                                    self.logger.info(f'找到赔率变化历史数据，共 {len(odds_changes)} 条记录')
                                    
                                    # 处理每一条赔率变化记录
                                    for change in odds_changes:
                                        if not change.strip():
                                            continue
                                        
                                        parts = change.split('|')
                                        if len(parts) >= 4:
                                            try:
                                                odds_item = {
                                                    'match_id': match_id,
                                                    'odds_detail_id': odds_detail_id,
                                                    'bookmaker_id': bookmaker_id,
                                                    'home_win': float(parts[0]),
                                                    'draw': float(parts[1]),
                                                    'away_win': float(parts[2]),
                                                    'update_time': parts[3]
                                                }
                                                
                                                # 标准化update_time格式为PostgreSQL可接受的时间戳格式
                                                try:
                                                    if parts[3].isdigit():
                                                        # 处理Unix时间戳
                                                        timestamp = int(parts[3])
                                                        dt_object = datetime.fromtimestamp(timestamp)
                                                        odds_item['update_time'] = dt_object.strftime('%Y-%m-%d %H:%M:%S')
                                                        self.logger.info(f'转换Unix时间戳: {parts[3]} -> {odds_item["update_time"]}')
                                                    elif re.match(r'^[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$', parts[3]):
                                                        # 处理"MM-DD HH:MM"格式
                                                        current_year = datetime.now().year
                                                        time_str = f"{current_year}-{parts[3][:5]} {parts[3][6:]}"
                                                        dt_object = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                                                        odds_item['update_time'] = dt_object.strftime('%Y-%m-%d %H:%M:%S')
                                                        self.logger.info(f'转换短日期格式: {parts[3]} -> {odds_item["update_time"]}')
                                                except Exception as e:
                                                    self.logger.error(f'标准化时间格式失败: {str(e)}')
                                                
                                                self.logger.info(f'创建赔率记录: {odds_item}')
                                                self.logger.info(f'赔率记录详情 - 比赛ID: {match_id}, 赔率详情ID: {odds_detail_id}, 博彩公司: {target_bookmakers[bookmaker_id]}')
                                                self.logger.info(f'赔率值 - 主胜: {odds_item["home_win"]}, 平局: {odds_item["draw"]}, 客胜: {odds_item["away_win"]}')
                                                self.logger.info(f'更新时间(标准化格式): {odds_item["update_time"]}')
                                                yield odds_item
                                                
                                            except (ValueError, IndexError) as e:
                                                self.logger.error(f'处理赔率数值时出错: {str(e)}')
                                                self.logger.error(f'赔率数据: {parts}')
                                    break
            else:
                self.logger.error('未找到var game数据')
                
        except Exception as e:
            self.logger.error(f'解析赔率数据时出错: {str(e)}')
            if not response.meta.get("retried", False):
                yield Request(
                    response.url,
                    callback=self.parse_odds,
                    meta={**response.meta, "retried": True},
                    dont_filter=True
                )
                
    def closed(self, reason):
        """爬虫关闭时调用"""
        if self.task_id:
            # 计算总耗时
            duration = datetime.now() - self.start_time
            minutes, seconds = divmod(duration.seconds, 60)
            
            # 根据关闭原因决定任务是成功还是失败
            is_success = reason == 'finished'
            status = '已完成' if is_success else '异常终止'
            
            # 更新最终状态
            message = f"爬虫{status}！处理了{self.processed_match_count}场比赛，耗时{minutes}分{seconds}秒"
            progress = 100 if is_success else 90
            
            # 记录到爬虫日志
            self.logger.info(f"爬虫{status}，原因: {reason}")
            self.logger.info(f"总共处理了 {self.processed_match_count} 场比赛")
            self.logger.info(f"总耗时: {minutes}分{seconds}秒")
            
            # 尝试直接通过API完成任务
            try:
                url = f"http://localhost:8000/api/spider-logs/{self.task_id}/complete/"
                data = {
                    'success': is_success,
                    'message': message
                }
                headers = {'Content-Type': 'application/json'}
                response = requests.post(url, json=data, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    self.logger.info(f"成功更新任务完成状态: {is_success}")
                else:
                    # 如果完成API不存在，使用常规状态更新
                    self.update_task_status(progress, message)
            except Exception as e:
                self.logger.error(f"通过complete API更新状态失败: {str(e)}")
                # 回退到常规状态更新
                self.update_task_status(progress, message)