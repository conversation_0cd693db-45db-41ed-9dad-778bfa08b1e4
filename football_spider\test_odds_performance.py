#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
赔率处理性能测试脚本
测试不同批处理大小下的赔率处理性能
"""

import os
import sys
import time
import psycopg2
from datetime import datetime
import argparse
import random
import uuid

# 数据库连接参数
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'dbname': 'oddluck',
    'user': 'postgres',
    'password': '888'
}

def connect_db():
    """连接到数据库"""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            dbname=DB_CONFIG['dbname'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        sys.exit(1)

def generate_test_data(match_ids, num_records=100):
    """生成测试用的赔率数据"""
    test_data = []
    bookmaker_ids = ['281', '82', '115']  # Bet365, 立博, 威廉希尔
    
    for _ in range(num_records):
        match_id = random.choice(match_ids)
        bookmaker_id = random.choice(bookmaker_ids)
        odds_detail_id = str(random.randint(10000, 99999))
        update_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 生成随机赔率
        home_win = round(random.uniform(1.5, 4.0), 2)
        draw = round(random.uniform(2.0, 5.0), 2)
        away_win = round(random.uniform(1.5, 6.0), 2)
        
        # 创建赔率记录
        odds_item = {
            'match_id': match_id,
            'odds_detail_id': odds_detail_id,
            'bookmaker_id': bookmaker_id,
            'home_win': home_win,
            'draw': draw,
            'away_win': away_win,
            'update_time': update_time
        }
        
        test_data.append(odds_item)
    
    return test_data

def get_match_ids(conn, limit=10):
    """从数据库获取一些真实的比赛ID"""
    match_ids = []
    with conn.cursor() as cur:
        cur.execute("SELECT match_id FROM matches ORDER BY created_at DESC LIMIT %s", (limit,))
        for row in cur.fetchall():
            match_ids.append(row[0])
    
    # 如果数据库中没有足够的比赛ID，生成一些随机ID
    while len(match_ids) < limit:
        match_ids.append(str(uuid.uuid4())[:8])
    
    return match_ids

def process_odds_batch(conn, odds_batch):
    """批量处理赔率数据"""
    batch_size = len(odds_batch)
    if batch_size == 0:
        return 0
        
    print(f"批量处理 {batch_size} 条赔率数据")
    
    try:
        with conn.cursor() as cur:
            # 准备批量插入的参数
            values_list = []
            args_list = []
            
            for item in odds_batch:
                values_list.append("(%s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
                args_list.extend([
                    item['match_id'], item['odds_detail_id'], item['bookmaker_id'],
                    item['home_win'], item['draw'], item['away_win'], item['update_time']
                ])
            
            # 构建批量插入SQL
            sql = f"""
                INSERT INTO odds (
                    match_id, odds_detail_id, bookmaker_id,
                    home_win, draw, away_win, update_time,
                    created_at, updated_at
                ) VALUES {', '.join(values_list)}
                ON CONFLICT (match_id, odds_detail_id, bookmaker_id, update_time) 
                DO UPDATE SET
                    home_win = EXCLUDED.home_win,
                    draw = EXCLUDED.draw,
                    away_win = EXCLUDED.away_win,
                    updated_at = CURRENT_TIMESTAMP;
            """
            
            # 执行批量插入
            cur.execute(sql, args_list)
            conn.commit()
            
            return batch_size
            
    except Exception as e:
        print(f"批量处理赔率数据时出错: {str(e)}")
        conn.rollback()
        return 0

def process_odds_single(conn, item):
    """单条处理赔率数据"""
    try:
        with conn.cursor() as cur:
            sql = """
                INSERT INTO odds (
                    match_id, odds_detail_id, bookmaker_id,
                    home_win, draw, away_win, update_time,
                    created_at, updated_at
                ) VALUES (
                    %(match_id)s, %(odds_detail_id)s, %(bookmaker_id)s,
                    %(home_win)s, %(draw)s, %(away_win)s, %(update_time)s,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                ON CONFLICT (match_id, odds_detail_id, bookmaker_id, update_time) 
                DO UPDATE SET
                    home_win = EXCLUDED.home_win,
                    draw = EXCLUDED.draw,
                    away_win = EXCLUDED.away_win,
                    updated_at = CURRENT_TIMESTAMP;
            """
            
            cur.execute(sql, item)
            conn.commit()
            
            return 1
            
    except Exception as e:
        print(f"处理单条赔率数据时出错: {str(e)}")
        conn.rollback()
        return 0

def run_test(batch_sizes=[1, 10, 50, 100], num_records=1000):
    """运行性能测试"""
    print(f"开始测试 {num_records} 条赔率记录的处理性能，测试批处理大小: {batch_sizes}")
    
    conn = connect_db()
    match_ids = get_match_ids(conn)
    test_data = generate_test_data(match_ids, num_records)
    
    results = []
    
    # 对每个批处理大小进行测试
    for batch_size in batch_sizes:
        print(f"\n测试批处理大小: {batch_size}")
        
        # 复制测试数据，避免交叉影响
        data_copy = test_data.copy()
        
        # 开始计时
        start_time = time.time()
        
        # 处理数据
        processed = 0
        if batch_size == 1:
            # 单条处理模式
            for item in data_copy:
                processed += process_odds_single(conn, item)
        else:
            # 批处理模式
            for i in range(0, len(data_copy), batch_size):
                batch = data_copy[i:i+batch_size]
                processed += process_odds_batch(conn, batch)
        
        # 结束计时
        elapsed_time = time.time() - start_time
        
        # 记录结果
        result = {
            'batch_size': batch_size,
            'processed': processed,
            'time': elapsed_time,
            'rate': processed / elapsed_time if elapsed_time > 0 else 0
        }
        results.append(result)
        
        print(f"处理 {processed} 条记录，耗时 {elapsed_time:.2f} 秒，速率 {result['rate']:.2f} 条/秒")
    
    # 输出结果比较
    print("\n性能比较:")
    print(f"{'批次大小':<10} {'处理记录数':<10} {'耗时(秒)':<10} {'速率(条/秒)':<15}")
    print("-" * 45)
    
    for result in results:
        print(f"{result['batch_size']:<10} {result['processed']:<10} {result['time']:.2f}{'秒':<7} {result['rate']:.2f}{'条/秒':<10}")
    
    # 计算最佳批处理大小
    best_result = max(results, key=lambda x: x['rate'])
    print(f"\n最佳批处理大小: {best_result['batch_size']}，速率: {best_result['rate']:.2f} 条/秒")
    
    # 关闭连接
    conn.close()

def main():
    parser = argparse.ArgumentParser(description='赔率处理性能测试工具')
    parser.add_argument('--records', type=int, default=500, help='测试的记录数量')
    parser.add_argument('--batch-sizes', type=str, default='1,20,50,100', help='要测试的批处理大小，用逗号分隔')
    
    args = parser.parse_args()
    
    # 解析批处理大小
    batch_sizes = [int(x.strip()) for x in args.batch_sizes.split(',')]
    
    # 运行测试
    run_test(batch_sizes, args.records)

if __name__ == "__main__":
    main() 