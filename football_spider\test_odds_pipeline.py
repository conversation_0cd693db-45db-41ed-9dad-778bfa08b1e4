import logging
import psycopg2
from psycopg2.extras import DictCursor
from datetime import datetime, timedelta
import uuid
import os
import sys

# --- 配置日志 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 添加项目根目录到 sys.path ---
# 确保能找到 football_spider 包中的模块
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(project_root, '..')) # Assume script is in football_spider dir, need parent dir
sys.path.insert(0, project_root) # Add the current dir as well

# 假设 pipelines.py 和 incremental_pipeline.py 在 football_spider 目录下
try:
    from football_spider.incremental_pipeline import IncrementalUpdatePipeline
    logger.info("成功导入 IncrementalUpdatePipeline")
except ImportError as e:
    logger.error(f"无法导入 IncrementalUpdatePipeline: {e}")
    # Try importing relative to the package if run via `python -m`
    try:
        from incremental_pipeline import IncrementalUpdatePipeline
        logger.info("Imported IncrementalUpdatePipeline using relative path")
    except ImportError as e_rel:
         logger.error(f"无法通过相对路径导入 IncrementalUpdatePipeline: {e_rel}")
         logger.error("请确保此脚本位于 football_spider 项目的根目录下，或者调整 sys.path。")
         sys.exit(1)

# --- 数据库连接配置 (从 settings.py 获取) ---
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'dbname': 'oddluck',
    'user': 'postgres',
    'password': '888'  # !!! 请确保这里是你的数据库密码 !!!
}

# --- Mock Spider 对象 ---
class MockSpider:
    name = "test_odds_spider"
    task_id = None # _process_odds 不直接使用 task_id

# --- 准备测试数据 ---
# !!! 重要: 请根据你的数据库实际情况修改以下值 !!!

# 1. 准备一个确定【存在】于数据库 odds 表中的记录的关键信息
#    你需要去数据库 odds 表中找到一条真实的记录
EXISTING_MATCH_ID = '2591189'  # 替换成你数据库中存在的 match_id
EXISTING_ODDS_DETAIL_ID = '142136348' # 替换成对应的 odds_detail_id
EXISTING_BOOKMAKER_ID = '82'       # 替换成对应的 bookmaker_id
# 替换成这条记录在数据库中【精确】的 update_time (需要是 datetime 对象)
# 例如: datetime(2024, 4, 3, 10, 30, 15)
# 你可能需要先查询数据库得到这个精确时间
EXISTING_UPDATE_TIME = datetime(2025, 3, 16, 18, 6, 0) # <--- !!! 必须替换为真实精确时间 (datetime 对象) !!!

existing_odds_item = {
    'match_id': EXISTING_MATCH_ID,
    'odds_detail_id': EXISTING_ODDS_DETAIL_ID,
    'bookmaker_id': EXISTING_BOOKMAKER_ID,
    'home_win': 1.80, # 这些值不影响存在性判断，但需要有
    'draw': 3.50,
    'away_win': 4.00,
    'update_time': EXISTING_UPDATE_TIME
}

# 2. 准备一个确定【不存】在于数据库中的新赔率记录
#    确保 match_id 存在于 matches 表中，但其他组合是新的
#    如果上面的 EXISTING_MATCH_ID 无效，这里也要替换
NEW_MATCH_ID = EXISTING_MATCH_ID # 可以复用上面存在的 match_id
new_odds_item = {
    'match_id': NEW_MATCH_ID,
    'odds_detail_id': str(uuid.uuid4()), # 使用UUID确保唯一性
    'bookmaker_id': '281', # 可以是任意 bookmaker_id
    'home_win': 1.95,
    'draw': 3.65,
    'away_win': 4.15,
    # 使用一个稍微未来的时间，确保与现有记录不同
    'update_time': datetime.now() + timedelta(seconds=10)
}

# --- 测试主函数 ---
def run_test():
    logger.info("开始测试 IncrementalUpdatePipeline._process_odds 方法...")

    # 检查测试数据是否已填写
    if EXISTING_MATCH_ID == 'REPLACE_ME' or EXISTING_ODDS_DETAIL_ID == 'REPLACE_ME' \
       or EXISTING_BOOKMAKER_ID == 'REPLACE_ME' or EXISTING_UPDATE_TIME is None:
       logger.error("❌ 测试失败：请先在此脚本中填写真实的 EXISTING_* 测试数据！")
       return

    pipeline = None
    conn = None
    cur = None
    mock_spider = MockSpider()

    try:
        # 1. 创建 Pipeline 实例
        pipeline = IncrementalUpdatePipeline(**DB_CONFIG)
        logger.info("IncrementalUpdatePipeline 实例已创建")

        # 2. 建立数据库连接 (供 Pipeline 使用)
        logger.info(f"尝试连接数据库: {DB_CONFIG['dbname']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}")
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor(cursor_factory=DictCursor)
        logger.info("数据库连接成功")

        # 3. 将连接和游标设置给 Pipeline 实例 (模拟 open_spider)
        pipeline.conn = conn
        pipeline.cur = cur

        # 4. 测试处理【已存在】的赔率项
        logger.info("\n--- 测试【已存在】的赔率项 ---")
        logger.info(f"输入 Item: {existing_odds_item}")
        result_existing = pipeline._process_odds(existing_odds_item.copy(), mock_spider) # 使用副本
        logger.info(f"输出 Result: {result_existing}")
        if result_existing is None:
            logger.info("✅ 预期结果: 返回 None (正确)")
        else:
            logger.error("❌ 预期结果: 返回 None (失败)")

        # 5. 测试处理【新】赔率项
        logger.info("\n--- 测试【新】赔率项 ---")
        logger.info(f"输入 Item: {new_odds_item}")
        # 确保 new_odds_item 的 match_id 有效
        if new_odds_item['match_id'] == 'REPLACE_ME':
             logger.warning("注意：新赔率项的 match_id 仍为 'REPLACE_ME'，测试可能不准确。")
        result_new = pipeline._process_odds(new_odds_item.copy(), mock_spider) # 使用副本
        logger.info(f"输出 Result: {result_new}")
        if result_new is not None and isinstance(result_new, dict):
            logger.info("✅ 预期结果: 返回 Item 字典 (正确)")
        else:
            logger.error("❌ 预期结果: 返回 Item 字典 (失败)")

        # 6. 检查统计计数 (可选)
        logger.info("\n--- 检查统计计数 ---")
        logger.info(f"Pipeline 统计: {pipeline.stats}")
        expected_new = 1
        expected_unchanged = 1
        if pipeline.stats['odds']['new'] == expected_new and pipeline.stats['odds']['unchanged'] == expected_unchanged:
             logger.info(f"✅ 预期统计: 新增={expected_new}, 无变化={expected_unchanged} (正确)")
        else:
             logger.error(f"❌ 预期统计: 新增={expected_new}, 无变化={expected_unchanged} (失败)")


    except psycopg2.OperationalError as db_err:
         logger.error(f"数据库连接或操作错误: {db_err}")
         logger.error("请检查数据库服务是否运行，以及DB_CONFIG中的连接参数是否正确。")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
    finally:
        logger.info("\n测试结束，关闭资源...")
        # 不需要调用 pipeline.close_spider()，因为我们手动管理连接
        if cur:
            cur.close()
        if conn:
            # 如果在 Pipeline 中有未提交的事务，回滚以防万一
            try:
                conn.rollback()
            except Exception:
                pass
            conn.close()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    run_test() 