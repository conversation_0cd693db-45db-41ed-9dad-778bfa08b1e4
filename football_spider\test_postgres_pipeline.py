import logging
import psycopg2
from psycopg2.extras import DictCursor
from datetime import datetime, timezone, timedelta
import pytz  # 用于处理时区

# 模拟 Scrapy 设置和 Spider 对象
from collections import namedtuple
Settings = namedtuple('Settings', ['POSTGRES_HOST', 'POSTGRES_PORT', 'POSTGRES_DBNAME', 'POSTGRES_USER', 'POSTGRES_PASSWORD', 'POSTGRES_ODDS_BATCH_ENABLE', 'POSTGRES_ODDS_BATCH_SIZE'])
Spider = namedtuple('Spider', ['settings'])

# 导入需要测试的 Pipeline 和数据库连接池初始化函数
from football_spider.pipelines import PostgreSQLPipeline
from football_spider.db_pool import init_db_pool

# --- 配置区域 ---
# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置 (!!! 请根据你的实际情况修改 !!!)
DB_CONFIG = {
    'POSTGRES_HOST': 'localhost',
    'POSTGRES_PORT': 5432,
    'POSTGRES_DBNAME': 'oddluck',
    'POSTGRES_USER': 'postgres',
    'POSTGRES_PASSWORD': '888',  # <--- !!! 务必确认或修改密码 !!!
    'POSTGRES_ODDS_BATCH_ENABLE': False, # 设为 False 以测试单条处理逻辑, 设为 True 测试批处理
    'POSTGRES_ODDS_BATCH_SIZE': 5 # 如果启用批处理，设置批处理大小
}

# 测试数据 (!!! 请根据你的数据库实际情况修改 'EXISTING_*' 的值 !!!)
# 确保这条记录在你的数据库中确实存在
EXISTING_RECORD_DATA = {
    'match_id': '2591189',  # 使用你提供的 match_id (字符串)
    'odds_detail_id': '141993792', # 使用你提供的 odds_detail_id (字符串)
    'bookmaker_id': '115',   # 使用你提供的 bookmaker_id (字符串)
    # 使用你提供的 update_time，并用 pytz 设置正确的时区 (+0800 -> Asia/Shanghai)
    'update_time': datetime(2025, 3, 10, 2, 51, 0, tzinfo=pytz.timezone('Asia/Shanghai')),
    'home_win': 1.35, # 保留示例值，或根据你的记录修改
    'draw': 4.95,  # 保留示例值，或根据你的记录修改
    'away_win': 9.6,   # 保留示例值，或根据你的记录修改
}

# 这条记录不应该存在于数据库中 (脚本会尝试先删除它以确保)
# 使用一个很可能不存在的 future 时间戳
NEW_RECORD_DATA = {
    'match_id': EXISTING_RECORD_DATA['match_id'], # 使用相同的 match_id (字符串)
    'odds_detail_id': EXISTING_RECORD_DATA['odds_detail_id'], # 使用相同的 detail_id (字符串)
    'bookmaker_id': EXISTING_RECORD_DATA['bookmaker_id'],   # 使用相同的 bookmaker_id (字符串)
    'update_time': datetime.now(pytz.utc) + timedelta(days=365), # 一年后的时间，确保是新的
    'home_win': 2.10,
    'draw': 3.10,
    'away_win': 4.10,
}
# --- 配置区域结束 ---

def get_settings():
    """创建模拟的 Scrapy Settings 对象"""
    return Settings(
        POSTGRES_HOST=DB_CONFIG['POSTGRES_HOST'],
        POSTGRES_PORT=DB_CONFIG['POSTGRES_PORT'],
        POSTGRES_DBNAME=DB_CONFIG['POSTGRES_DBNAME'],
        POSTGRES_USER=DB_CONFIG['POSTGRES_USER'],
        POSTGRES_PASSWORD=DB_CONFIG['POSTGRES_PASSWORD'],
        POSTGRES_ODDS_BATCH_ENABLE=DB_CONFIG['POSTGRES_ODDS_BATCH_ENABLE'],
        POSTGRES_ODDS_BATCH_SIZE=DB_CONFIG['POSTGRES_ODDS_BATCH_SIZE']
    )

def get_record_count(cursor: DictCursor, item: dict) -> int:
    """查询数据库中特定赔率记录的数量"""
    sql = """
        SELECT COUNT(*) FROM odds
        WHERE match_id = %(match_id)s
          AND odds_detail_id = %(odds_detail_id)s
          AND bookmaker_id = %(bookmaker_id)s
          AND update_time = %(update_time)s
    """
    try:
        cursor.execute(sql, item)
        return cursor.fetchone()['count']
    except Exception as e:
        logger.error(f"查询记录数量时出错: {e}")
        raise

def delete_record(cursor: DictCursor, item: dict):
    """从数据库删除特定赔率记录"""
    sql = """
        DELETE FROM odds
        WHERE match_id = %(match_id)s
          AND odds_detail_id = %(odds_detail_id)s
          AND bookmaker_id = %(bookmaker_id)s
          AND update_time = %(update_time)s
    """
    try:
        cursor.execute(sql, item)
        logger.info(f"尝试删除记录 (如果存在): {item['match_id']}, {item['bookmaker_id']}, {item['update_time']}")
        return cursor.rowcount # 返回删除的行数
    except Exception as e:
        logger.error(f"删除记录时出错: {e}")
        raise

def insert_record(cursor: DictCursor, item: dict):
    """手动插入一条记录，用于确保测试前提条件"""
    sql = """
        INSERT INTO odds (
            match_id, odds_detail_id, bookmaker_id,
            home_win, draw, away_win, update_time,
            created_at, updated_at
        ) VALUES (
            %(match_id)s, %(odds_detail_id)s, %(bookmaker_id)s,
            %(home_win)s, %(draw)s, %(away_win)s, %(update_time)s,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        ON CONFLICT (match_id, odds_detail_id, bookmaker_id, update_time)
        DO NOTHING; -- 即使这里也用 DO NOTHING，避免因重复运行脚本导致错误
    """
    try:
        cursor.execute(sql, item)
        logger.info(f"尝试插入前提记录 (如果不存在): {item['match_id']}, {item['bookmaker_id']}, {item['update_time']}")
        return cursor.rowcount # 返回影响的行数
    except Exception as e:
        logger.error(f"插入前提记录时出错: {e}")
        raise

def run_test():
    """执行测试"""
    logger.info("--- 开始 PostgreSQLPipeline ON CONFLICT 测试 ---")
    # settings = get_settings() # 不再使用 namedtuple
    db_pool = None
    pipeline = None
    conn = None
    cur = None # <--- 初始化 cur 为 None

    try:
        # 1. 初始化数据库连接池
        logger.info("初始化数据库连接池...")
        # db_pool = init_db_pool(settings) # <--- 修改：直接传递 DB_CONFIG 字典
        db_pool = init_db_pool(DB_CONFIG)
        if not db_pool:
            logger.error("数据库连接池初始化失败！")
            return

        # 获取一个连接用于设置和验证
        conn = db_pool.pool.getconn()
        conn.autocommit = True # 确保设置和验证操作立即生效
        cur = conn.cursor(cursor_factory=DictCursor)
        logger.info("数据库连接成功.")

        # 2. 准备测试环境
        logger.info("--- 准备测试环境 ---")
        # 确保 EXISTING_RECORD 存在
        logger.info(f"确保 EXISTING_RECORD 存在...")
        insert_record(cur, EXISTING_RECORD_DATA)

        # 确保 NEW_RECORD 不存在
        logger.info(f"确保 NEW_RECORD 不存在...")
        delete_record(cur, NEW_RECORD_DATA)

        # 获取初始状态
        initial_existing_count = get_record_count(cur, EXISTING_RECORD_DATA)
        initial_new_count = get_record_count(cur, NEW_RECORD_DATA)
        logger.info(f"初始状态: EXISTING_RECORD 数量 = {initial_existing_count}, NEW_RECORD 数量 = {initial_new_count}")
        if initial_existing_count == 0:
             logger.warning("警告: EXISTING_RECORD 在准备阶段后仍然不存在，请检查配置和数据库！")
        if initial_new_count > 0:
             logger.warning("警告: NEW_RECORD 在准备阶段后仍然存在，请检查删除逻辑或数据库！")


        # 3. 初始化 Pipeline
        logger.info("--- 初始化 Pipeline ---")
        # mock_spider = Spider(settings=DB_CONFIG) # 不再需要 mock_spider
        # 尝试使用 from_settings 类方法初始化 Pipeline，这是更标准的 Scrapy 方式
        # 它应该会自己处理数据库连接池的创建
        try:
            pipeline_settings = get_settings() # 使用 namedtuple 模拟 Scrapy settings
            pipeline = PostgreSQLPipeline.from_settings(pipeline_settings)
            # 如果 from_settings 成功，db_pool 应该由 Pipeline 内部管理，这里不需要单独创建/传递
            # 我们可能需要调整 open_spider/close_spider 的调用方式，或者它们可能不再需要
            # open_spider 通常需要一个 spider 对象，我们创建一个简单的 mock
            mock_spider_for_open = namedtuple('Spider', ['name'])('test_spider')
            pipeline.open_spider(mock_spider_for_open) # 调用 open_spider 传入一个简单的 spider mock
        except AttributeError:
            # 如果 from_settings 不存在，回退到直接调用 __init__ (尽管不标准)
            # 这需要 PostgreSQLPipeline 的 __init__ 签名与 TypeError 提示的一致
            logger.warning("PostgreSQLPipeline 没有 from_settings 方法，尝试直接初始化 (可能不标准)")
            try:
                 # 需要提供 __init__ 期望的所有参数
                 # 修改：传递 DB_CONFIG 字典给 settings 参数，因为它有 .get() 方法
                 pipeline = PostgreSQLPipeline(
                     host=DB_CONFIG['POSTGRES_HOST'],
                     port=DB_CONFIG['POSTGRES_PORT'],
                     dbname=DB_CONFIG['POSTGRES_DBNAME'],
                     user=DB_CONFIG['POSTGRES_USER'],
                     password=DB_CONFIG['POSTGRES_PASSWORD'],
                     settings=DB_CONFIG # <--- 修改：传递字典而不是 namedtuple
                 )
                 # 这种情况下，可能也需要调用 open_spider
                 mock_spider_for_open = namedtuple('Spider', ['name'])('test_spider')
                 pipeline.open_spider(mock_spider_for_open)
            except TypeError as te:
                 logger.error(f"尝试直接初始化 Pipeline 失败: {te}")
                 logger.error("请检查 PostgreSQLPipeline 的 __init__ 方法签名是否与预期一致。")
                 raise # 重新抛出错误，测试无法继续
        except Exception as e:
            logger.error(f"初始化 Pipeline 时发生其他错误: {e}")
            raise
        
        if not pipeline:
            logger.error("Pipeline 初始化失败，测试无法继续")
            return

        logger.info(f"Pipeline 初始化完成. Batch mode: {hasattr(pipeline, 'odds_batch_enable') and pipeline.odds_batch_enable}")


        # 4. 执行处理
        logger.info("--- 执行 Pipeline 处理 ---")
        logger.info("处理 EXISTING_RECORD (预期: DO NOTHING)...")
        # process_item 需要 spider 参数，传递我们之前创建的 mock
        pipeline.process_item(EXISTING_RECORD_DATA.copy(), mock_spider_for_open)

        logger.info("处理 NEW_RECORD_1 (预期: INSERT)...")
        pipeline.process_item(NEW_RECORD_DATA.copy(), mock_spider_for_open)

        logger.info("处理 NEW_RECORD_2 (与 NEW_RECORD_1 重复, 预期: DO NOTHING)...")
        pipeline.process_item(NEW_RECORD_DATA.copy(), mock_spider_for_open)

        # 如果启用了批处理，需要手动触发批处理（或关闭 spider）
        # 检查 pipeline 是否真的有 odds_batch_enable 属性
        batch_enabled = hasattr(pipeline, 'odds_batch_enable') and pipeline.odds_batch_enable
        if batch_enabled:
            logger.info("批处理模式启用，调用 close_spider 刷新最后的批次...")
            pipeline.close_spider(mock_spider_for_open)
        else:
             logger.info("非批处理模式，无需手动刷新.")


        # 5. 验证结果
        logger.info("--- 验证结果 ---")
        # 短暂等待，以防数据库操作有微小延迟 (尤其是在批处理模式下)
        import time
        time.sleep(1)

        final_existing_count = get_record_count(cur, EXISTING_RECORD_DATA)
        final_new_count = get_record_count(cur, NEW_RECORD_DATA)
        logger.info(f"最终状态: EXISTING_RECORD 数量 = {final_existing_count}, NEW_RECORD 数量 = {final_new_count}")

        # 断言检查
        test_passed = True
        if final_existing_count != initial_existing_count:
            logger.error(f"测试失败! EXISTING_RECORD 数量从 {initial_existing_count} 变为 {final_existing_count} (预期不变)")
            test_passed = False
        else:
            logger.info("EXISTING_RECORD 数量验证通过 (预期不变).")

        # 对于新记录，预期是插入1条
        expected_new_count = initial_new_count + 1
        if final_new_count != expected_new_count:
             logger.error(f"测试失败! NEW_RECORD 数量从 {initial_new_count} 变为 {final_new_count} (预期为 {expected_new_count})")
             test_passed = False
        else:
             logger.info(f"NEW_RECORD 数量验证通过 (预期增加1条至 {expected_new_count}).")

        if test_passed:
            logger.info("--- 测试成功 ---")
        else:
            logger.error("--- 测试失败 ---")


        # 6. 清理环境 (可选但推荐)
        logger.info("--- 清理测试环境 ---")
        logger.info(f"删除测试中插入的 NEW_RECORD...")
        deleted_count = delete_record(cur, NEW_RECORD_DATA)
        logger.info(f"清理完成，删除了 {deleted_count} 条 NEW_RECORD.")

    except (Exception, psycopg2.DatabaseError) as error:
        logger.error(f"测试过程中发生错误: {error}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
    finally:
        # 关闭 Pipeline (如果已初始化)
        if pipeline:
             try:
                 # 确保即使测试中出错，也尝试关闭 spider 以释放资源（如批处理）
                 if not (hasattr(pipeline, 'odds_batch_enable') and pipeline.odds_batch_enable):
                     pipeline.close_spider(mock_spider_for_open) # 确保 spider mock 传递给 close_spider
                 logger.info("Pipeline 已关闭.")
             except Exception as e:
                 logger.error(f"关闭 Pipeline 时出错: {e}")

        # 关闭数据库连接和连接池
        # 如果 Pipeline 使用 from_settings 创建，它可能内部管理连接池
        # 我们需要确保连接被正确关闭。这里保留显式关闭逻辑以防万一。
        if cur:
            cur.close()
        if conn:
            # 检查 db_pool 是否真的被 Pipeline 内部创建和管理
            # 如果是，则不应直接操作外部创建的 pool
            if 'db_pool' in locals() and db_pool is not None:
                # 检查 pool 是否是 Pipeline 内部的 pool (如果可访问)
                # if hasattr(pipeline, 'db_pool') and db_pool is pipeline.db_pool:
                #      logger.info("连接池由 Pipeline 管理，跳过外部关闭连接.")
                # else:
                try:
                     # 尝试释放连接回我们手动创建的池（如果测试回退到手动创建池）
                     if hasattr(db_pool, 'pool') and hasattr(db_pool.pool, 'putconn'):
                          db_pool.pool.putconn(conn)
                          logger.info("数据库连接已释放回外部池.")
                     else:
                          conn.close()
                          logger.info("数据库连接已关闭 (无池).")
                except Exception as e_conn:
                    logger.error(f"释放/关闭连接时出错: {e_conn}")
            else:
                try:
                    conn.close()
                    logger.info("数据库连接已关闭.")
                except Exception as e_conn_close:
                     logger.error(f"关闭孤立连接时出错: {e_conn_close}")

        if 'db_pool' in locals() and db_pool is not None:
            # 检查 pool 是否由 pipeline 管理
            # if not (hasattr(pipeline, 'db_pool') and db_pool is pipeline.db_pool):
            try:
                 db_pool.close()
                 logger.info("外部数据库连接池已关闭.")
            except Exception as e_pool_close:
                logger.error(f"关闭外部连接池时出错: {e_pool_close}")
            # else:
            #     logger.info("连接池由 Pipeline 管理，跳过外部关闭.")

        logger.info("--- 测试结束 ---")


if __name__ == "__main__":
    run_test() 