#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试时间比较逻辑
"""

import logging
import sys
import datetime
import re
import dateutil.parser
import time
from datetime import datetime, timezone

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_datetime(time_value):
    """
    将各种可能的时间格式解析为datetime对象
    :param time_value: 时间值（字符串或datetime）
    :return: datetime对象或None（如果解析失败）
    """
    if time_value is None:
        return None
        
    # 如果已经是datetime对象，处理时区
    if isinstance(time_value, datetime):
        # 如果有时区信息，转换为UTC时间，移除时区信息
        if time_value.tzinfo is not None:
            return time_value.replace(tzinfo=None)
        return time_value
        
    # 如果是字符串，尝试解析
    if isinstance(time_value, str):
        # 尝试多种格式
        formats = [
            '%Y-%m-%d %H:%M:%S',     # 2025-03-22 16:41:13
            '%Y-%m-%d %H:%M',        # 2025-03-22 16:41
            '%Y-%m-%d',              # 2025-03-22
        ]
        
        # 先尝试标准格式
        for fmt in formats:
            try:
                return datetime.strptime(time_value, fmt)
            except ValueError:
                continue
        
        # 处理带时区的格式
        try:
            # 尝试ISO格式（包含时区）
            dt = dateutil.parser.parse(time_value)
            # 移除时区信息，以便与其他时间比较
            return dt.replace(tzinfo=None)
        except:
            pass
            
        # 处理短日期格式（MM-DD HH:MM）
        if re.match(r'^[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$', time_value):
            try:
                # 添加当前年份
                current_year = datetime.now().year
                time_with_year = f"{current_year}-{time_value[:5]} {time_value[6:]}"
                return datetime.strptime(time_with_year, '%Y-%m-%d %H:%M')
            except:
                pass
                
        # 如果是纯数字，尝试作为Unix时间戳处理
        if re.match(r'^\d+$', time_value):
            try:
                # 使用UTC时间，避免时区问题
                dt = datetime.fromtimestamp(int(time_value), tz=timezone.utc)
                # 移除时区信息
                return dt.replace(tzinfo=None)
            except:
                pass
        
        # 记录无法解析的时间
        logger.warning(f"无法解析的时间格式: {time_value}")
        return None
    
    # 其他类型，尝试转为字符串后解析
    return parse_datetime(str(time_value))

def is_same_time(time1, time2, max_diff_seconds=60):
    """
    比较两个时间是否相同，允许最大差异为max_diff_seconds秒
    """
    dt1 = parse_datetime(time1)
    dt2 = parse_datetime(time2)
    
    # 如果有一个无法解析，则认为不同
    if dt1 is None or dt2 is None:
        logger.error(f"时间比较错误: 时间1 = {time1}({dt1}), 时间2 = {time2}({dt2})")
        return False
    
    try:
        diff = abs((dt1 - dt2).total_seconds())
        logger.info(f"时间差异: {diff}秒, 时间1 = {dt1}, 时间2 = {dt2}")
        return diff <= max_diff_seconds
    except Exception as e:
        logger.error(f"比较时间时出错: {e}, 时间1 = {time1}({dt1}), 时间2 = {time2}({dt2})")
        return False

def test_parse_time():
    """测试时间解析功能"""
    test_times = [
        '2025-03-22 16:41:13',
        '2025-03-22 16:41',
        '2025-03-22',
        '03-22 16:41',
        '1679483473',  # Unix时间戳（2023-03-22 16:11:13）
        '2025-03-22 16:41:13+08:00',  # 带时区的时间
        'invalid_time',  # 无效时间
        None,
        datetime.now(),
    ]
    
    for t in test_times:
        result = parse_datetime(t)
        logger.info(f"解析时间 '{t}' 的结果: {result}")

def test_time_comparison():
    """测试时间比较功能"""
    # 测试相同或相近的时间
    test_pairs = [
        ('2025-03-22 16:41:13', '2025-03-22 16:41:13'),  # 完全相同
        ('2025-03-22 16:41:13', '2025-03-22 16:41:50'),  # 差异小于60秒
        ('2025-03-22 16:41:13', '2025-03-22 16:42:20'),  # 差异大于60秒
        ('2025-03-22 16:41:13', '2025-03-22 16:41:13+08:00'),  # 带时区的比较
        ('1679483473', '2023-03-22 11:11:13'),  # Unix时间戳与正确的字符串时间比较
    ]
    
    for t1, t2 in test_pairs:
        result = is_same_time(t1, t2)
        logger.info(f"比较 '{t1}' 和 '{t2}': {'相同' if result else '不同'}")
        
    # 详细检查时间戳转换
    timestamp = '1679483473'
    dt_str = '2023-03-22 11:11:13'
    
    ts_parsed = parse_datetime(timestamp)
    dt_parsed = parse_datetime(dt_str)
    
    if ts_parsed and dt_parsed:
        diff = abs((ts_parsed - dt_parsed).total_seconds())
        logger.info(f"时间戳 '{timestamp}' 转换为: {ts_parsed}")
        logger.info(f"字符串时间 '{dt_str}' 转换为: {dt_parsed}")
        logger.info(f"差异: {diff}秒, 是否相同: {diff <= 60}")
    else:
        logger.error(f"时间戳转换测试失败: {timestamp} -> {ts_parsed}, {dt_str} -> {dt_parsed}")

def main():
    logger.info("开始测试时间解析和比较功能")
    
    test_parse_time()
    test_time_comparison()
    
    # 添加延迟确保所有日志都输出
    time.sleep(1)
    
    logger.info("测试完成")

if __name__ == "__main__":
    main() 