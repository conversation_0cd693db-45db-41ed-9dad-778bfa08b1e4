#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证增量更新功能是否正常工作的脚本
可以在爬虫执行前后运行，比较数据表的记录数变化
检查是否存在match_id引用异常的情况
"""

import psycopg2
from psycopg2.extras import DictCursor
import argparse
import sys
import logging
import os
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_db_connection(host='localhost', port=5432, dbname='oddluck', user='postgres', password='888'):
    """连接数据库"""
    try:
        logger.info(f"正在连接数据库... Host: {host}, Port: {port}, Database: {dbname}")
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password,
            connect_timeout=10
        )
        logger.info("数据库连接成功")
        return conn
    except Exception as e:
        logger.error(f"连接数据库时出错: {str(e)}")
        return None

def check_table_counts(conn):
    """检查各表记录数量"""
    tables = ['leagues', 'teams', 'matches', 'odds']
    results = {}
    
    with conn.cursor() as cur:
        for table in tables:
            cur.execute(f"SELECT COUNT(*) FROM {table}")
            count = cur.fetchone()[0]
            results[table] = count
            logger.info(f"表 {table} 包含 {count} 条记录")
    
    return results

def check_match_ids_consistency(conn):
    """检查match_id的一致性"""
    with conn.cursor(cursor_factory=DictCursor) as cur:
        # 检查是否存在赔率表中引用了不存在的match_id
        cur.execute("""
            SELECT o.match_id, COUNT(*) as odds_count
            FROM odds o
            LEFT JOIN matches m ON o.match_id = m.match_id
            WHERE m.match_id IS NULL
            GROUP BY o.match_id
            ORDER BY odds_count DESC
            LIMIT 10
        """)
        invalid_refs = cur.fetchall()
        
        if invalid_refs:
            logger.error(f"发现 {len(invalid_refs)} 个无效的match_id引用")
            for row in invalid_refs:
                logger.error(f"无效的match_id: {row['match_id']}, 引用次数: {row['odds_count']}")
        else:
            logger.info("未发现无效的match_id引用，数据一致性良好")
        
        # 检查每个比赛的赔率记录数量分布
        cur.execute("""
            SELECT m.match_id, COUNT(*) as odds_count
            FROM matches m
            LEFT JOIN odds o ON m.match_id = o.match_id
            GROUP BY m.match_id
            ORDER BY odds_count DESC
            LIMIT 10
        """)
        match_odds_counts = cur.fetchall()
        
        logger.info("赔率数量最多的前10场比赛:")
        for row in match_odds_counts:
            logger.info(f"Match ID: {row['match_id']}, 赔率记录数: {row['odds_count']}")
        
        # 检查是否有重复的赔率记录
        cur.execute("""
            SELECT match_id, odds_detail_id, bookmaker_id, update_time, COUNT(*) as count
            FROM odds
            GROUP BY match_id, odds_detail_id, bookmaker_id, update_time
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 10
        """)
        duplicate_odds = cur.fetchall()
        
        if duplicate_odds:
            logger.error(f"发现 {len(duplicate_odds)} 组重复的赔率记录")
            for row in duplicate_odds:
                logger.error(f"重复记录: match_id={row['match_id']}, odds_detail_id={row['odds_detail_id']}, "
                           f"bookmaker_id={row['bookmaker_id']}, update_time={row['update_time']}, 数量: {row['count']}")
        else:
            logger.info("未发现重复的赔率记录")

def save_state(count_results, output_file='db_state.txt'):
    """保存当前数据库状态"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"记录时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        for table, count in count_results.items():
            f.write(f"{table}: {count}\n")
    logger.info(f"数据库状态已保存到 {output_file}")

def compare_states(prev_state_file='db_state.txt'):
    """比较当前状态与之前保存的状态"""
    if not os.path.exists(prev_state_file):
        logger.warning(f"找不到之前的状态文件: {prev_state_file}")
        return
        
    prev_counts = {}
    try:
        with open(prev_state_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[1:]  # 跳过时间戳行
            for line in lines:
                parts = line.strip().split(': ')
                if len(parts) == 2:
                    prev_counts[parts[0]] = int(parts[1])
    except Exception as e:
        logger.error(f"读取之前的状态文件时出错: {str(e)}")
        return
        
    conn = get_db_connection()
    if not conn:
        return
        
    try:
        current_counts = check_table_counts(conn)
        
        logger.info("=" * 50)
        logger.info("数据库记录数变化:")
        logger.info("-" * 50)
        logger.info(f"{'表名':<10} {'之前':<10} {'之后':<10} {'变化':<10} {'有效性'}")
        logger.info("-" * 50)
        
        for table in ['leagues', 'teams', 'matches', 'odds']:
            prev = prev_counts.get(table, 0)
            curr = current_counts.get(table, 0)
            diff = curr - prev
            # 简单估计变化的有效性（这里只是示例，实际可能需要更复杂的逻辑）
            validity = "正常" if (table != 'odds' and diff <= 100) or (table == 'odds' and diff <= 1000) else "需检查"
            
            logger.info(f"{table:<10} {prev:<10} {curr:<10} {'+' + str(diff) if diff > 0 else diff:<10} {validity}")
        
        logger.info("=" * 50)
        
        # 检查match_id一致性
        check_match_ids_consistency(conn)
        
    finally:
        conn.close()

def main():
    parser = argparse.ArgumentParser(description='验证增量更新功能')
    parser.add_argument('--save', action='store_true', help='保存当前数据库状态')
    parser.add_argument('--compare', action='store_true', help='比较当前状态与之前保存的状态')
    parser.add_argument('--file', default='db_state.txt', help='状态文件路径')
    
    args = parser.parse_args()
    
    if args.save:
        conn = get_db_connection()
        if conn:
            try:
                counts = check_table_counts(conn)
                save_state(counts, args.file)
            finally:
                conn.close()
    elif args.compare:
        compare_states(args.file)
    else:
        # 默认行为：只检查数据库状态
        conn = get_db_connection()
        if conn:
            try:
                check_table_counts(conn)
                check_match_ids_consistency(conn)
            finally:
                conn.close()

if __name__ == "__main__":
    main()