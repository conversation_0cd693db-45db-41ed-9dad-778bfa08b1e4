# OddLuck 足球赛事网站

这是一个使用Vue.js和Django构建的足球赛事信息展示网站。网站展示从football_spider爬虫项目获取的足球赛事数据，提供实时赔率更新和比赛信息。

## 项目结构

```
oddluck/
├── football_spider/      # 爬虫项目
│   ├── database/        # 数据库相关
│   └── football_spider/ # 爬虫源代码
├── oddluck_dev/         # 网站项目
│   ├── backend/        # Django后端
│   │   ├── api/       # REST API应用
│   │   └── backend/   # Django项目配置
│   ├── frontend/      # Vue.js前端
│   │   ├── src/      # 源代码
│   │   └── public/   # 静态资源
│   └── oddluck_venv/  # Python虚拟环境
```

## 技术栈

### 前端
- Vue 3
- TypeScript
- Vite
- Tailwind CSS
- FullCalendar
- Font Awesome
- v-calendar（日期选择器）

### 后端
- Django 4.2+
- Django REST Framework
- PostgreSQL
- psutil（进程管理）

### 爬虫
- Scrapy
- SQLAlchemy
- PostgreSQL
- Selenium（浏览器自动化）
- Playwright（浏览器自动化）

## 启动项目
- **快捷启动**
```bash
cd oddluck_dev
python start_runserver.py (同步的 WSGI 服务器)
python start_daphne.py (支持异步的 ASGI 服务器)
```
- **一般启动**
```bash
# 后端
cd backend
python manage.py runserver
# 前端
cd oddluck_dev\frontend
npm run dev
```

## 开发环境设置

### 后端设置
1. 创建虚拟环境：
```bash
python -m venv 你的虚拟环境名
```

2. 进入项目目录并激活虚拟环境：
```bash
# Windows
cd oddluck_dev
.\oddluck_venv\Scripts\activate
cd ..
cd ..
cd backend（接启动项目的后端）
# Linux/Mac
source 你的虚拟环境名/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 运行数据库迁移（自动创建Django 的认证系统，详看setting.py的INSTALLED_APPS）：
```bash
cd backend
python manage.py migrate
```
假设你的'api'应用中的模型有变更尚未创建迁移文件，需要先生成迁移文件，然后再运行数据库迁移（每当你修改模型后，都需要创建并应用迁移）
```bash
cd backend
python manage.py makemigrations api
python manage.py migrate
```

5. 关于嵌入模型：
- **使用HuggingFace模型BAAI/bge-large-zh-v1.5**
```bash
cd oddluck_dev
.\oddluck_venv\Scripts\activate
cd backend\llm_analysis
python download_model.py
```
- **通过Ollama使用模型nomic-embed-text或BAAI/bge-large-zh-v1.5**
```bash
ollama serve
ollama pull nomic-embed-text
echo 'FROM BAAI/bge-large-zh-v1.5' > Modelfile
ollama create bge-large-zh -f Modelfile
```
- **测试Ollama模型**
```bash
cd oddluck_dev
.\oddluck_venv\Scripts\activate
cd backend\llm_analysis
python test_ollama.py
```
- **通过以下方式设置Hugging Face的缓存目录**
```bash
# 设置环境变量HF_HOME指定缓存路径
# Windows
set HF_HOME=D:\my_cache\huggingface
# Linux/Mac
export HF_HOME=/path/to/cache/huggingface
```

```bash
# 在代码中设置环境变量（在导入相关库之前）
import os
os.environ['HF_HOME'] = '/path/to/cache/huggingface'
```

### 前端设置

1. 安装依赖：
```bash
cd oddluck_dev\frontend
npm install
```

### 爬虫设置

1. 创建并激活虚拟环境（可选）

2. 安装依赖：
```bash
cd football_spider
pip install -r requirements.txt
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
# 国内源
https://pypi.tuna.tsinghua.edu.cn/simple
https://mirrors.aliyun.com/pypi/simple/
https://pypi.mirrors.ustc.edu.cn/simple/
https://pypi.douban.com/simple/
https://pypi.hustunique.com/
```

3. 运行爬虫：
```bash
cd football_spider
scrapy crawl [spider_name]
```

## 数据库

项目使用PostgreSQL数据库，主要数据模型包括：

- **leagues**（联赛信息）
  - 联赛名称、ID、子联赛ID
  - 国家/地区
  - 赛季信息
  - 轮次信息

- **teams**（球队信息）
  - 球队ID、名称、英文名称
  - 所属联赛
  - 球队Logo

- **matches**（比赛信息）
  - 比赛ID、时间
  - 主客队
  - 比分（全场、半场）
  - 比赛状态、轮次

- **odds**（赔率信息）
  - 机构ID
  - 赔率类型（主胜、平局、客胜）
  - 赔率数值
  - 更新时间

## 功能特性

- **赛事展示**
  - 日历式赛事浏览
  - 按日期筛选比赛
  - 联赛分类过滤

- **赔率查看**
  - 实时赔率更新
  - 多家博彩公司对比
  - 历史赔率走势

- **用户体验**
  - 响应式设计，适配移动设备
  - 优化的UI交互（如日期选择器、联赛筛选）
  - 实时数据加载和刷新

- **爬虫管理**
  - 通过Web界面启动/停止爬虫
  - 爬虫运行状态监控
  - 爬取进度实时显示

## 环境要求

- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Windows/Linux/macOS

## 注意事项

- 所有数据获取相关操作必须在football_spider目录下进行
- 所有网站开发相关操作必须在oddluck_dev目录下进行
- 必须在oddluck_venv虚拟环境中安装依赖
- 运行命令时需要分步执行，避免使用&&连接符
- 数据库连接信息应通过环境变量或.env文件配置，避免硬编码 