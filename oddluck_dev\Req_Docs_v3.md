# 任务
构建一个关于足球赛事的网站。

# 前端
## 技术栈
### 框架
- vue.js
- Tailwind CSS
### 需求
#### 内容
**导航栏**
- logo（OddLuck）
- 赛事
**内容页**
- 赛事日历组件，FullCalendar库
- 排序，按联赛，按时间
- 每场比赛信息
    - 开赛时间
    - 球队徽章（SVG格式保证清晰度）
    - 球队名称
    - 比分
    - 欧赔（不跳转，直接在右边展开，过渡动效）
        - 博彩机构
        - 初始欧赔
        - 即时欧赔
        - 最终欧赔（若无则显示暂无）
#### UI
- 优雅、美观、趣味
- 响应式
#### 风格
**视觉设计**
- 主色调建议采用球队主题色（如红蓝搭配），辅助色使用灰色平衡视觉
- 图标系统使用Font Awesome的足球相关图标（如fa-futbol）
- 文字排版：标题使用粗衬线字体（如Playfair Display），正文用无衬线字体（如Open Sans）
**动效设计**
- 页面过渡使用0.3s的ease-in-out缓动函数
- 下拉菜单添加抛物线轨迹展开动画增强趣味性

# 后端
## 技术栈
### 框架
- Python
- Django
### 数据库
- PostgreSQL
### 数据源
- 来源于数据库oddluck（表模型见football_spider\database\schema.sql）