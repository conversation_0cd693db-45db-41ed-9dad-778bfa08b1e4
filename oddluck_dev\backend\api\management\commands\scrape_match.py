from django.core.management.base import BaseCommand, CommandError
from scheduler.jobs import trigger_scrape # 导入我们之前写的触发函数
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Manually triggers scraping for a specific match ID via Scrapyd.'

    def add_arguments(self, parser):
        parser.add_argument('match_id', type=int, help='The ID of the match to scrape')

    def handle(self, *args, **options):
        match_id = options['match_id']
        self.stdout.write(f"Attempting to trigger scrape for match ID: {match_id}")
        logger.info(f"Manual command: Attempting to trigger scrape for match ID: {match_id}")

        # 直接调用触发函数
        success = trigger_scrape(match_id)

        if success:
            self.stdout.write(self.style.SUCCESS(f"Successfully triggered scrape task for match ID: {match_id} via Scrapyd."))
            logger.info(f"Manual command: Successfully triggered scrape task for match ID: {match_id}")
        else:
            self.stdout.write(self.style.ERROR(f"Failed to trigger scrape task for match ID: {match_id} via Scrapyd. Check logs."))
            logger.error(f"Manual command: Failed to trigger scrape task for match ID: {match_id}")
            # 可以选择抛出 CommandError 来表示命令失败
            # raise CommandError(f"Failed to trigger scrape task for match ID: {match_id}")