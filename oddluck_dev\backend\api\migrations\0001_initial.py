# Generated by Django 5.0.4 on 2025-05-25 00:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Continent',
            fields=[
                ('continent_id', models.IntegerField(primary_key=True, serialize=False)),
                ('continent_name', models.CharField(max_length=50)),
                ('continent_name_en', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '大洲',
                'verbose_name_plural': '大洲',
                'db_table': 'continents',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='SpiderLog',
            fields=[
                ('task_id', models.Char<PERSON>ield(max_length=64, primary_key=True, serialize=False, verbose_name='任务ID')),
                ('spider_type', models.Char<PERSON>ield(choices=[('continent', '大洲爬虫'), ('country', '国家爬虫'), ('league', '联赛爬虫'), ('cup', '杯赛爬虫'), ('team', '球队爬虫'), ('match', '比赛爬虫')], max_length=20, verbose_name='爬虫类型')),
                ('league', models.CharField(blank=True, max_length=50, null=True, verbose_name='联赛/杯赛')),
                ('season', models.CharField(blank=True, max_length=50, null=True, verbose_name='赛季')),
                ('round', models.CharField(blank=True, max_length=10, null=True, verbose_name='轮次/阶段')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('paused', '已暂停'), ('completed', '已完成'), ('failed', '失败')], default='pending', max_length=20, verbose_name='状态')),
                ('progress', models.IntegerField(default=0, verbose_name='进度')),
                ('message', models.CharField(blank=True, max_length=255, verbose_name='消息')),
                ('log_content', models.TextField(blank=True, verbose_name='日志内容')),
                ('started_at', models.DateTimeField(auto_now_add=True, verbose_name='开始时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
            ],
            options={
                'verbose_name': '爬虫日志',
                'verbose_name_plural': '爬虫日志',
                'db_table': 'spider_logs',
                'ordering': ['-started_at'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('team_id', models.CharField(max_length=50, unique=True)),
                ('team_name_simp', models.CharField(max_length=100)),
                ('team_name_cant', models.CharField(blank=True, max_length=100, null=True)),
                ('team_name_en', models.CharField(blank=True, max_length=100, null=True)),
                ('team_logo', models.CharField(blank=True, max_length=200, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '球队',
                'verbose_name_plural': '球队',
                'db_table': 'teams',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('country_id', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('country_name', models.CharField(max_length=100)),
                ('country_logo', models.CharField(blank=True, max_length=200, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('continent', models.ForeignKey(db_column='continent_id', on_delete=django.db.models.deletion.CASCADE, to='api.continent')),
            ],
            options={
                'verbose_name': '国家',
                'verbose_name_plural': '国家',
                'db_table': 'countries',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Cup',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('cup_id', models.CharField(max_length=50)),
                ('cup_short_name', models.CharField(max_length=100)),
                ('cup_official_name_simp', models.CharField(blank=True, max_length=200, null=True)),
                ('cup_official_name_cant', models.CharField(blank=True, max_length=200, null=True)),
                ('cup_official_name_en', models.CharField(blank=True, max_length=200, null=True)),
                ('cup_logo', models.CharField(blank=True, max_length=200, null=True)),
                ('match_type', models.CharField(max_length=50)),
                ('current_season', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('continent', models.ForeignKey(blank=True, db_column='continent_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='api.continent')),
                ('country', models.ForeignKey(blank=True, db_column='country_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='api.country')),
            ],
            options={
                'verbose_name': '杯赛',
                'verbose_name_plural': '杯赛',
                'db_table': 'cups',
                'managed': True,
                'unique_together': {('cup_id', 'current_season')},
            },
        ),
        migrations.CreateModel(
            name='League',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('league_id', models.CharField(max_length=50)),
                ('sub_league_id', models.CharField(blank=True, max_length=50, null=True)),
                ('league_type', models.CharField(max_length=50)),
                ('league_short_name', models.CharField(max_length=100)),
                ('league_official_name_simp', models.CharField(blank=True, max_length=200, null=True)),
                ('league_official_name_cant', models.CharField(blank=True, max_length=200, null=True)),
                ('league_official_name_en', models.CharField(blank=True, max_length=200, null=True)),
                ('league_logo', models.CharField(blank=True, max_length=200, null=True)),
                ('available_seasons', models.TextField(blank=True, null=True)),
                ('current_season', models.CharField(max_length=50)),
                ('total_rounds', models.IntegerField(blank=True, null=True)),
                ('current_round', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('continent', models.ForeignKey(blank=True, db_column='continent_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='api.continent')),
                ('country', models.ForeignKey(blank=True, db_column='country_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='api.country')),
            ],
            options={
                'verbose_name': '联赛',
                'verbose_name_plural': '联赛',
                'db_table': 'leagues',
                'managed': True,
                'unique_together': {('league_id', 'current_season', 'sub_league_id')},
            },
        ),
        migrations.CreateModel(
            name='Match',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('match_id', models.CharField(max_length=50, unique=True)),
                ('sub_league_id', models.CharField(blank=True, max_length=50, null=True)),
                ('season', models.CharField(max_length=50)),
                ('round', models.CharField(blank=True, max_length=50, null=True)),
                ('match_time', models.DateTimeField(blank=True, null=True)),
                ('full_score', models.CharField(blank=True, max_length=50, null=True)),
                ('half_score', models.CharField(blank=True, max_length=50, null=True)),
                ('scraped_1d', models.BooleanField(default=False, verbose_name='已触发提前1天爬取')),
                ('scraped_6h', models.BooleanField(default=False, verbose_name='已触发提前6小时爬取')),
                ('scraped_2h', models.BooleanField(default=False, verbose_name='已触发提前2小时爬取')),
                ('scraped_15m', models.BooleanField(default=False, verbose_name='已触发提前15分钟爬取')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cup_ref', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='api.cup', verbose_name='所属杯赛')),
                ('league_ref', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='api.league', verbose_name='所属联赛')),
                ('away_team', models.ForeignKey(db_column='away_team_id', on_delete=django.db.models.deletion.CASCADE, related_name='away_matches', to='api.team', to_field='team_id')),
                ('home_team', models.ForeignKey(db_column='home_team_id', on_delete=django.db.models.deletion.CASCADE, related_name='home_matches', to='api.team', to_field='team_id')),
            ],
            options={
                'verbose_name': '比赛',
                'verbose_name_plural': '比赛',
                'db_table': 'matches',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Odds',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('odds_detail_id', models.CharField(max_length=50)),
                ('bookmaker_id', models.CharField(max_length=50)),
                ('home_win', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('draw', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('away_win', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('update_time', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('match', models.ForeignKey(db_column='match_id', on_delete=django.db.models.deletion.CASCADE, to='api.match', to_field='match_id')),
            ],
            options={
                'verbose_name': '赔率',
                'verbose_name_plural': '赔率',
                'db_table': 'odds',
                'managed': True,
                'unique_together': {('match', 'odds_detail_id', 'bookmaker_id', 'update_time')},
            },
        ),
        migrations.AddConstraint(
            model_name='match',
            constraint=models.CheckConstraint(check=models.Q(models.Q(('cup_ref__isnull', True), ('league_ref__isnull', False)), models.Q(('cup_ref__isnull', False), ('league_ref__isnull', True)), _connector='OR'), name='match_belongs_to_league_or_cup'),
        ),
    ]
