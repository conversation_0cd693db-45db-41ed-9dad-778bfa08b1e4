from django.db import models
from django.utils import timezone

# Create your models here.

class Continent(models.Model):
    continent_id = models.IntegerField(primary_key=True)
    continent_name = models.CharField(max_length=50)
    continent_name_en = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'continents'
        managed = True
        verbose_name = "大洲"
        verbose_name_plural = "大洲"

    def __str__(self):
        return self.continent_name

class Country(models.Model):
    country_id = models.CharField(max_length=50, primary_key=True)
    country_name = models.CharField(max_length=100)
    country_logo = models.CharField(max_length=200, null=True, blank=True)
    continent = models.ForeignKey(Continent, on_delete=models.CASCADE, db_column='continent_id')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'countries'
        managed = True
        verbose_name = "国家"
        verbose_name_plural = "国家"

    def __str__(self):
        return self.country_name

class League(models.Model):
    id = models.AutoField(primary_key=True)
    league_id = models.CharField(max_length=50) # Natural key part 1
    sub_league_id = models.CharField(max_length=50, null=True, blank=True) # Natural key part 3
    league_type = models.CharField(max_length=50) # e.g., "1,0" (main league), "1,1" (sub league)
    league_short_name = models.CharField(max_length=100)
    league_official_name_simp = models.CharField(max_length=200, null=True, blank=True)
    league_official_name_cant = models.CharField(max_length=200, null=True, blank=True)
    league_official_name_en = models.CharField(max_length=200, null=True, blank=True)
    league_logo = models.CharField(max_length=200, null=True, blank=True)
    available_seasons = models.TextField(null=True, blank=True)
    current_season = models.CharField(max_length=50) # Natural key part 2
    total_rounds = models.IntegerField(null=True, blank=True)
    current_round = models.IntegerField(null=True, blank=True)
    continent = models.ForeignKey(Continent, on_delete=models.CASCADE, db_column='continent_id', null=True, blank=True)
    country = models.ForeignKey(Country, on_delete=models.CASCADE, db_column='country_id', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'leagues'
        managed = True
        # Unique constraint based on natural key derived from source data
        unique_together = ('league_id', 'current_season', 'sub_league_id') 
        verbose_name = "联赛"
        verbose_name_plural = "联赛"

    def __str__(self):
        name = self.league_short_name
        if self.sub_league_id:
            name += f" (Sub: {self.sub_league_id})"
        return f"{name} - {self.current_season}"

class Cup(models.Model):
    id = models.AutoField(primary_key=True)
    cup_id = models.CharField(max_length=50) # Natural key part 1
    cup_short_name = models.CharField(max_length=100)
    cup_official_name_simp = models.CharField(max_length=200, null=True, blank=True)
    cup_official_name_cant = models.CharField(max_length=200, null=True, blank=True)
    cup_official_name_en = models.CharField(max_length=200, null=True, blank=True)
    cup_logo = models.CharField(max_length=200, null=True, blank=True)
    match_type = models.CharField(max_length=50) # e.g., "2,0" (cup)
    current_season = models.CharField(max_length=50) # Natural key part 2
    continent = models.ForeignKey(Continent, on_delete=models.CASCADE, db_column='continent_id', null=True, blank=True)
    country = models.ForeignKey(Country, on_delete=models.CASCADE, db_column='country_id', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'cups'
        managed = True
        unique_together = ('cup_id', 'current_season')
        verbose_name = "杯赛"
        verbose_name_plural = "杯赛"
    
    def __str__(self):
        return f"{self.cup_short_name} - {self.current_season}"

class Team(models.Model):
    id = models.AutoField(primary_key=True)
    team_id = models.CharField(max_length=50, unique=True) # The persistent team identifier
    team_name_simp = models.CharField(max_length=100)
    team_name_cant = models.CharField(max_length=100, null=True, blank=True)
    team_name_en = models.CharField(max_length=100, null=True, blank=True)
    team_logo = models.CharField(max_length=200, null=True, blank=True)
    # Removed league/season specific fields - team entity is independent of current competition
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'teams'
        managed = True
        # Removed unique_together constraint involving league/season
        verbose_name = "球队"
        verbose_name_plural = "球队"

    def __str__(self):
        return self.team_name_simp

class Match(models.Model):
    id = models.AutoField(primary_key=True)
    match_id = models.CharField(max_length=50, unique=True) # Natural key from source
    
    # Link to either League or Cup
    league_ref = models.ForeignKey(
        League, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        verbose_name="所属联赛"
    )
    cup_ref = models.ForeignKey(
        Cup, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        verbose_name="所属杯赛"
    )
    
    # These fields provide context for the specific match instance
    sub_league_id = models.CharField(max_length=50, null=True, blank=True)
    season = models.CharField(max_length=50)
    round = models.CharField(max_length=50, null=True, blank=True)
    
    match_time = models.DateTimeField(null=True, blank=True)
    home_team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='home_matches', db_column='home_team_id', to_field='team_id')
    away_team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='away_matches', db_column='away_team_id', to_field='team_id')
    full_score = models.CharField(max_length=50, null=True, blank=True)
    half_score = models.CharField(max_length=50, null=True, blank=True)
    scraped_1d = models.BooleanField(default=False, verbose_name="已触发提前1天爬取")
    scraped_6h = models.BooleanField(default=False, verbose_name="已触发提前6小时爬取")
    scraped_2h = models.BooleanField(default=False, verbose_name="已触发提前2小时爬取")
    scraped_15m = models.BooleanField(default=False, verbose_name="已触发提前15分钟爬取")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'matches'
        managed = True
        verbose_name = "比赛"
        verbose_name_plural = "比赛"
        constraints = [
            models.CheckConstraint(
                check=models.Q(league_ref__isnull=False, cup_ref__isnull=True) | 
                      models.Q(league_ref__isnull=True, cup_ref__isnull=False),
                name='match_belongs_to_league_or_cup'
            )
        ]
    
    def __str__(self):
        competition = self.league_ref if self.league_ref else self.cup_ref
        competition_name = str(competition) if competition else "Unknown Competition"
        home_team_name = self.home_team.team_name_simp if self.home_team else "Unknown Home"
        away_team_name = self.away_team.team_name_simp if self.away_team else "Unknown Away"
        return f"{home_team_name} vs {away_team_name} ({competition_name} - {self.match_time})"

class Odds(models.Model):
    # Consider adding a simple AutoField primary key for easier admin/updates
    id = models.AutoField(primary_key=True) 
    match = models.ForeignKey(Match, on_delete=models.CASCADE, db_column='match_id', to_field='match_id')
    odds_detail_id = models.CharField(max_length=50) # Still unclear purpose - part of composite key?
    bookmaker_id = models.CharField(max_length=50)
    home_win = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    draw = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    away_win = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    update_time = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'odds'
        managed = True
        # Unique constraint represents the natural key from the source
        unique_together = ('match', 'odds_detail_id', 'bookmaker_id', 'update_time') 
        verbose_name = "赔率"
        verbose_name_plural = "赔率"

    def natural_key(self):
        # Avoid accessing match.match_id directly if match might not be loaded
        match_id_val = self.match.match_id if self.match else None 
        return (match_id_val, self.odds_detail_id, self.bookmaker_id, self.update_time)

    def __str__(self):
        match_id_val = self.match.match_id if self.match else "UnknownMatch"
        return f"{match_id_val}_{self.odds_detail_id}_{self.bookmaker_id}_{self.update_time}"

class SpiderLog(models.Model):
    """爬虫执行日志模型"""
    STATUS_CHOICES = (
        ('pending', '等待中'),
        ('running', '运行中'),
        ('paused', '已暂停'),
        ('completed', '已完成'),
        ('failed', '失败')
    )
    SPIDER_TYPE_CHOICES = (
        ('continent', '大洲爬虫'),
        ('country', '国家爬虫'),
        ('league', '联赛爬虫'),
        ('cup', '杯赛爬虫'), # Added cup spider
        ('team', '球队爬虫'),
        ('match', '比赛爬虫')
    )
    
    task_id = models.CharField('任务ID', max_length=64, primary_key=True)
    spider_type = models.CharField('爬虫类型', max_length=20, choices=SPIDER_TYPE_CHOICES)
    league = models.CharField('联赛/杯赛', max_length=50, blank=True, null=True) # Consider splitting or using FKs
    season = models.CharField('赛季', max_length=50, blank=True, null=True)
    round = models.CharField('轮次/阶段', max_length=10, blank=True, null=True)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField('进度', default=0)
    message = models.CharField('消息', max_length=255, blank=True)
    log_content = models.TextField('日志内容', blank=True)
    started_at = models.DateTimeField('开始时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    def complete(self, success=True):
        """完成爬虫任务，更新状态和完成时间"""
        self.status = 'completed' if success else 'failed'
        self.progress = 100 if success else self.progress
        self.completed_at = timezone.now()
        self.save()
    
    def update_progress(self, progress, message=''):
        """更新爬虫进度和消息"""
        self.progress = progress
        if message:
            self.message = message
        self.save()
    
    def add_log(self, log_entry):
        """添加日志条目"""
        timestamp = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        self.log_content += f"\n[{timestamp}] {log_entry}"
        self.save(update_fields=['log_content', 'updated_at'])
    
    class Meta:
        db_table = 'spider_logs'
        managed = True
        ordering = ['-started_at']
        verbose_name = "爬虫日志"
        verbose_name_plural = "爬虫日志"
