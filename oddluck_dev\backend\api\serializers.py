from rest_framework import serializers
from .models import Continent, Country, League, Cup, Team, Match, Odds, SpiderLog

class ContinentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Continent
        fields = '__all__'

class CountrySerializer(serializers.ModelSerializer):
    continent = ContinentSerializer(read_only=True)

    class Meta:
        model = Country
        fields = '__all__'

class LeagueSerializer(serializers.ModelSerializer):
    continent = ContinentSerializer(read_only=True)
    country = CountrySerializer(read_only=True)

    class Meta:
        model = League
        fields = ('id', 'league_id', 'sub_league_id', 'league_type', 'league_short_name',
                  'league_official_name_simp', 'league_official_name_cant', 'league_official_name_en',
                  'league_logo', 'available_seasons', 'current_season', 'total_rounds',
                  'current_round', 'continent', 'country', 'created_at', 'updated_at')

class CupSerializer(serializers.ModelSerializer):
    continent = ContinentSerializer(read_only=True)
    country = CountrySerializer(read_only=True)

    class Meta:
        model = Cup
        fields = ('id', 'cup_id', 'cup_short_name', 'cup_official_name_simp',
                  'cup_official_name_cant', 'cup_official_name_en', 'cup_logo',
                  'match_type', 'current_season', 'continent', 'country',
                  'created_at', 'updated_at')

class TeamSerializer(serializers.ModelSerializer):
    class Meta:
        model = Team
        fields = ('id', 'team_id', 'team_name_simp', 'team_name_cant',
                  'team_name_en', 'team_logo', 'created_at', 'updated_at')

class OddsSerializer(serializers.ModelSerializer):
    id = serializers.SerializerMethodField()

    class Meta:
        model = Odds
        fields = ('id', 'match_id', 'odds_detail_id', 'bookmaker_id',
                  'home_win', 'draw', 'away_win', 'update_time', 'created_at', 'updated_at')

    def get_id(self, obj):
        match_id_val = obj.match.match_id if hasattr(obj, 'match') and obj.match else getattr(obj, 'match_id', None)
        if isinstance(obj, dict):
             match_id_val = obj.get('match_id')
             odds_detail_id = obj.get('odds_detail_id')
             bookmaker_id = obj.get('bookmaker_id')
             update_time = obj.get('update_time')
             return f"{match_id_val}_{odds_detail_id}_{bookmaker_id}_{update_time}"
        return f"{match_id_val}_{obj.odds_detail_id}_{obj.bookmaker_id}_{obj.update_time}"

class MatchListSerializer(serializers.ModelSerializer):
    league = LeagueSerializer(source='league_ref', read_only=True, allow_null=True)
    cup = CupSerializer(source='cup_ref', read_only=True, allow_null=True)
    home_team = TeamSerializer(read_only=True)
    away_team = TeamSerializer(read_only=True)

    # 添加计算字段来统一处理联赛和杯赛的名称
    league_short_name = serializers.SerializerMethodField()

    def get_league_short_name(self, obj):
        """获取联赛或杯赛的简称"""
        if obj.league_ref:
            return obj.league_ref.league_short_name
        elif obj.cup_ref:
            return obj.cup_ref.cup_short_name
        else:
            return None

    class Meta:
        model = Match
        fields = ('id', 'match_id', 'league', 'cup', 'sub_league_id', 'season', 'round',
                  'match_time', 'home_team', 'away_team', 'full_score', 'half_score',
                  'league_short_name',  # 添加计算字段
                  'scraped_1d', 'scraped_6h', 'scraped_2h', 'scraped_15m',
                  'created_at', 'updated_at')

class MatchDetailSerializer(serializers.ModelSerializer):
    league = LeagueSerializer(source='league_ref', read_only=True, allow_null=True)
    cup = CupSerializer(source='cup_ref', read_only=True, allow_null=True)
    home_team = TeamSerializer(read_only=True)
    away_team = TeamSerializer(read_only=True)
    odds = OddsSerializer(source='odds_set', many=True, read_only=True)

    # 添加计算字段来统一处理联赛和杯赛的名称
    league_short_name = serializers.SerializerMethodField()

    def get_league_short_name(self, obj):
        """获取联赛或杯赛的简称"""
        if obj.league_ref:
            return obj.league_ref.league_short_name
        elif obj.cup_ref:
            return obj.cup_ref.cup_short_name
        else:
            return None

    class Meta:
        model = Match
        fields = ('id', 'match_id', 'league', 'cup', 'sub_league_id', 'season', 'round',
                  'match_time', 'home_team', 'away_team', 'full_score', 'half_score',
                  'league_short_name',  # 添加计算字段
                  'odds',
                  'scraped_1d', 'scraped_6h', 'scraped_2h', 'scraped_15m',
                  'created_at', 'updated_at')

class SpiderLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = SpiderLog
        fields = '__all__'