
from django_eventstream import send_event
import logging

logger = logging.getLogger(__name__)

def notify_match_update(match_id: str, update_type: str):
    """
    向 SSE 客户端发送比赛赔率更新通知。

    Args:
        match_id: 更新的比赛 ID。
        update_type: 更新类型 (例如 '15m', '2h', '6h', '1d')。
    """
    try:
        channel = 'match-updates' # 必须与 sse_views.py 中定义的一致
        event_type = 'odds_updated' # 必须与前端 EventListener 中使用的一致
        data = {'match_id': str(match_id), 'update_type': update_type}
        
        # 发送事件
        send_event(channel, event_type, data)
        
        logger.info(f"SSE event sent to channel '{channel}': type='{event_type}', data={data}")
    except Exception as e:
        # 在生产环境中，应该使用更健壮的错误处理
        logger.error(f"Failed to send SSE event for match {match_id}: {e}", exc_info=True)
