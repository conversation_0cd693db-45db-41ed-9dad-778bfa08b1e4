from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    ContinentViewSet, CountryViewSet, LeagueViewSet, CupViewSet, TeamViewSet, MatchViewSet, OddsViewSet, SpiderLogViewSet,
    NotifyMatchUpdateView
)

router = DefaultRouter()
router.register(r'continents', ContinentViewSet)
router.register(r'countries', CountryViewSet)
router.register(r'leagues', LeagueViewSet)
router.register(r'cups', CupViewSet)
router.register(r'teams', TeamViewSet)
router.register(r'matches', MatchViewSet)
router.register(r'odds', OddsViewSet)
router.register(r'spider-logs', SpiderLogViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('notify-match-update/', NotifyMatchUpdateView.as_view(), name='notify-match-update'),
] 