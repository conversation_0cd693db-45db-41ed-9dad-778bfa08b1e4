from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from .models import Continent, Country, League, Cup, Team, Match, Odds, SpiderLog
from .serializers import (
    ContinentSerializer, CountrySerializer, LeagueSerializer, CupSerializer,
    TeamSerializer, MatchListSerializer, MatchDetailSerializer,
    OddsSerializer, SpiderLogSerializer
)
from rest_framework.response import Response
from rest_framework.exceptions import NotFound
from rest_framework.decorators import action, permission_classes, authentication_classes
from rest_framework import status
from datetime import datetime, time
from django.utils.timezone import make_aware
from django.db import connection
import subprocess
import os
import uuid
import json
import logging
import re
import sys
import signal
import psutil
import time as time_module  # 重命名time模块导入，避免与datetime.time冲突
from django.conf import settings
from django.db.models import Q, F, Count, Max
from django.utils import timezone  # 添加timezone导入
from rest_framework.permissions import AllowAny
from rest_framework.authentication import SessionAuthentication
from rest_framework.permissions import IsAuthenticated
import threading  # Import threading
from rest_framework.views import APIView
from .sse_utils import notify_match_update # 导入我们之前的工具函数

logger = logging.getLogger(__name__)

# Create your views here.

# Helper function to calculate paths relative to this file or project root
# Assuming this file is at oddluck_dev/backend/api/views.py
# Project root is C:\Projects\oddluck
CURRENT_DIR = os.path.dirname(__file__)
BACKEND_DIR = os.path.abspath(os.path.join(CURRENT_DIR, '..')) # oddluck_dev/backend
ODDLUCK_DEV_DIR = os.path.abspath(os.path.join(BACKEND_DIR, '..')) # oddluck_dev
PROJECT_ROOT = os.path.abspath(os.path.join(ODDLUCK_DEV_DIR, '..')) # C:\Projects\oddluck

SPIDER_PROJECT_DIR = os.path.join(PROJECT_ROOT, 'football_spider')
SPIDER_VENV_PYTHON = os.path.join(SPIDER_PROJECT_DIR, 'spider_venv', 'Scripts', 'python.exe')

# Check if the calculated path exists (only on Windows for now)
if sys.platform == 'win32' and not os.path.exists(SPIDER_VENV_PYTHON):
    logger.error(f"CRITICAL: Spider venv python executable not found at calculated path: {SPIDER_VENV_PYTHON}")
    # You might want to raise an ImproperlyConfigured error or handle this case
    # For now, we'll log an error. The Popen call will likely fail later.

class CustomPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class OddsPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response({
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'count': self.page.paginator.count,
            'results': data
        })

    def paginate_queryset(self, queryset, request, view=None):
        self.page_size = self.get_page_size(request)
        if not self.page_size:
            return None

        paginator = self.django_paginator_class(queryset, self.page_size)
        page_number = request.query_params.get(self.page_query_param, 1)
        if page_number in self.last_page_strings:
            page_number = paginator.num_pages

        try:
            self.page = paginator.page(page_number)
        except Exception as exc:
            msg = self.invalid_page_message.format(
                page_number=page_number, message=str(exc)
            )
            raise NotFound(msg)

        if paginator.num_pages > 1 and self.template is not None:
            self.display_page_controls = True

        self.request = request
        return list(self.page)

# 应用认证和权限豁免装饰器到ContinentViewSet
@authentication_classes([])
@permission_classes([AllowAny])
class ContinentViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Continent.objects.all()
    serializer_class = ContinentSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['continent_id', 'continent_name', 'continent_name_en']
    pagination_class = CustomPagination

# 应用认证和权限豁免装饰器到CountryViewSet
@authentication_classes([])
@permission_classes([AllowAny])
class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['country_id', 'country_name', 'continent_id']
    pagination_class = CustomPagination

# 应用认证和权限豁免装饰器到LeagueViewSet
@authentication_classes([])
@permission_classes([AllowAny])
class LeagueViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = League.objects.all()
    serializer_class = LeagueSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['league_id', 'league_short_name', 'league_official_name_en', 'league_type', 'current_season', 'continent_id', 'country_id']
    pagination_class = CustomPagination

# New CupViewSet
@authentication_classes([])
@permission_classes([AllowAny])
class CupViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Cup.objects.all()
    serializer_class = CupSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['cup_id', 'cup_short_name', 'cup_official_name_en', 'match_type', 'current_season', 'continent_id', 'country_id']
    pagination_class = CustomPagination

# TeamViewSet updated
@authentication_classes([])
@permission_classes([AllowAny])
class TeamViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Team.objects.all()
    serializer_class = TeamSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['team_id', 'team_name_simp', 'team_name_en']
    pagination_class = CustomPagination

# MatchViewSet updated
@authentication_classes([])
@permission_classes([AllowAny])
class MatchViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Match.objects.select_related('league_ref', 'cup_ref', 'home_team', 'away_team').all()
    filter_backends = [DjangoFilterBackend]
    filterset_fields = {
        'league_ref': ['exact'],
        'cup_ref': ['exact'],
        'season': ['exact', 'in'],
        'round': ['exact', 'in'],
        'match_time': ['exact', 'date__exact', 'date__gte', 'date__lte', 'range'],
        'match_id': ['exact', 'in'],
        'home_team': ['exact'],
        'away_team': ['exact'],
        'home_team__team_id': ['exact', 'in'],
        'away_team__team_id': ['exact', 'in']
    }
    pagination_class = CustomPagination

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return MatchDetailSerializer
        return MatchListSerializer

    def get_object(self):
        """
        重写get_object方法，允许通过match_id字段查找比赛
        """
        # 获取URL中的ID参数
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        lookup_value = self.kwargs[lookup_url_kwarg]

        try:
            # 尝试用主键查找
            pk = int(lookup_value)
            obj = Match.objects.get(pk=pk)
            self.check_object_permissions(self.request, obj)
            return obj
        except (ValueError, Match.DoesNotExist):
            # 当主键查找失败时，尝试用match_id查找
            try:
                obj = Match.objects.get(match_id=lookup_value)
                self.check_object_permissions(self.request, obj)
                return obj
            except Match.DoesNotExist:
                # 两种方法都找不到时，返回404错误
                raise NotFound(f"未找到ID为{lookup_value}的比赛")

    @action(detail=True, methods=['get'], url_path='odds')
    def odds(self, request, pk=None):
        """获取特定比赛的赔率数据"""
        try:
            match = self.get_object()
            match_source_id = match.match_id

            odds = Odds.objects.filter(match_id=match_source_id).order_by('-update_time')

            serializer = OddsSerializer(odds, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"获取赔率数据时出错 (Match PK: {pk}): {e}", exc_info=True)
            return Response(
                {'error': f'获取赔率数据失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'], url_path='date/(?P<date>\d{4}-\d{2}-\d{2})')
    def by_date(self, request, date=None):
        """根据日期获取比赛，支持按国家和国际赛事筛选"""
        try:
            print(f"Fetching matches for date: {date}")

            # 获取查询参数
            country_id = request.query_params.get('country_id')
            international = request.query_params.get('international')

            print(f"Query params - country_id: {country_id}, international: {international}")

            # 将日期字符串转换为datetime对象
            date_obj = datetime.strptime(date, '%Y-%m-%d')
            print(f"Parsed date object: {date_obj}")

            # 获取该日期的比赛，考虑时区
            start_of_day = make_aware(datetime.combine(date_obj.date(), datetime.min.time()))
            end_of_day = make_aware(datetime.combine(date_obj.date(), datetime.max.time()))
            print(f"Query time range: {start_of_day} to {end_of_day}")

            # 检查原始查询集
            print(f"Original queryset count: {self.queryset.count()}")

            # 构建基础查询
            matches = self.queryset.filter(
                match_time__range=(start_of_day, end_of_day)
            )
            print(f"After date filter count: {matches.count()}")

            # 根据参数进行进一步筛选
            if country_id:
                # 按国家筛选：联赛或杯赛属于该国家
                matches = matches.filter(
                    Q(league_ref__country_id=country_id) |
                    Q(cup_ref__country_id=country_id)
                )
                print(f"After country filter count: {matches.count()}")
            elif international == 'true':
                # 国际赛事：只包含明确的国际杯赛
                # 首先筛选出所有杯赛比赛
                matches = matches.filter(cup_ref__isnull=False)
                print(f"After cup filter count: {matches.count()}")

                # 然后筛选国际赛事条件
                matches = matches.filter(
                    Q(cup_ref__country_id__isnull=True) |
                    Q(cup_ref__cup_short_name__icontains='欧冠') |
                    Q(cup_ref__cup_short_name__icontains='欧联') |
                    Q(cup_ref__cup_short_name__icontains='世界杯') |
                    Q(cup_ref__cup_short_name__icontains='欧洲杯') |
                    Q(cup_ref__cup_short_name__icontains='欧会杯') |
                    Q(cup_ref__cup_short_name__icontains='欧超杯') |
                    Q(cup_ref__cup_short_name__icontains='世俱杯') |
                    Q(cup_ref__cup_short_name__icontains='欧冠杯') |
                    Q(cup_ref__cup_short_name__icontains='欧联杯')
                )
                print(f"After international filter count: {matches.count()}")

            # 添加关联查询
            matches = matches.select_related(
                'league_ref', 'home_team', 'away_team', 'cup_ref'
            ).order_by('match_time')

            print(f"Final matches count: {matches.count()}")

            # 输出查询的SQL
            print(f"Query SQL: {matches.query}")

            # 输出每场比赛的详细信息
            for match in matches:
                print(f"Match details:")
                print(f"  ID: {match.match_id}")
                print(f"  Time: {match.match_time} (UTC)")
                home_team_name = match.home_team.team_name_simp if match.home_team else '未知主队'
                away_team_name = match.away_team.team_name_simp if match.away_team else '未知客队'
                print(f"  Teams: {home_team_name} vs {away_team_name}")

                # 处理联赛和杯赛的情况
                if match.league_ref:
                    print(f"  League: {match.league_ref.league_short_name}")
                elif match.cup_ref:
                    print(f"  Cup: {match.cup_ref.cup_short_name}")
                else:
                    print(f"  Competition: Unknown")

            # 序列化数据
            serializer = self.get_serializer(matches, many=True)
            data = serializer.data
            print(f"Serialized data: {data}")

            return Response(data)

        except ValueError as e:
            print(f"ValueError: {str(e)}")
            return Response(
                {'error': '无效的日期格式'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            print(f"Exception: {str(e)}")
            return Response(
                {'error': f'获取比赛数据失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# 应用认证和权限豁免装饰器到OddsViewSet
@authentication_classes([])
@permission_classes([AllowAny])
class OddsViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Odds.objects.all()
    serializer_class = OddsSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['match_id', 'bookmaker_id', 'update_time']
    pagination_class = OddsPagination

    def list(self, request, *args, **kwargs):
        """自定义列表查询，优先使用原始SQL提升性能"""
        # 获取查询参数
        match_id = request.query_params.get('match_id')
        bookmaker_id = request.query_params.get('bookmaker_id')

        # 使用原始SQL查询以提高性能
        if match_id:
            try:
                with connection.cursor() as cursor:
                    params = []
                    sql = """
                    SELECT "odds"."match_id", "odds"."odds_detail_id",
                           "odds"."bookmaker_id", "odds"."home_win",
                           "odds"."draw", "odds"."away_win",
                           "odds"."update_time", "odds"."created_at", "odds"."updated_at"
                    FROM "odds"
                    WHERE 1=1
                    """

                    if match_id:
                        sql += " AND match_id = %s"
                        params.append(match_id)

                    if bookmaker_id:
                        sql += " AND bookmaker_id = %s"
                        params.append(bookmaker_id)

                    sql += " ORDER BY update_time DESC"

                    cursor.execute(sql, params)

                    columns = [col[0] for col in cursor.description]
                    results = []
                    for row in cursor.fetchall():
                        results.append(dict(zip(columns, row)))

                    # 打印各家博彩公司的记录数统计
                    bookmaker_counts = {}
                    unique_bookmakers = set()
                    for item in results:
                        bookmaker_id = item['bookmaker_id']
                        unique_bookmakers.add(bookmaker_id)
                        bookmaker_counts[bookmaker_id] = bookmaker_counts.get(bookmaker_id, 0) + 1

                    print(f"SQL查询结果: {len(results)} 条记录")
                    print(f"包含的博彩公司: {unique_bookmakers}")
                    print(f"各博彩公司记录数: {bookmaker_counts}")

                    return Response({
                        'count': len(results),
                        'next': None,
                        'previous': None,
                        'results': results
                    })
            except Exception as e:
                print(f"SQL查询出错: {str(e)}")
                # 发生错误时回退到标准ORM查询
                pass

        # 标准DRF处理流程
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

# 应用认证和权限豁免装饰器到SpiderLogViewSet
@authentication_classes([])
@permission_classes([AllowAny])
class SpiderLogViewSet(viewsets.ModelViewSet):
    queryset = SpiderLog.objects.all()
    serializer_class = SpiderLogSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['spider_type', 'league', 'season', 'status']
    pagination_class = CustomPagination

    @action(detail=False, methods=['post'], url_path='start-continent-spider')
    def start_continent_spider(self, request):
        """启动大洲爬虫"""
        # 创建一个唯一的任务ID
        task_id = f"continent_{uuid.uuid4()}"

        # 创建爬虫日志记录
        log = SpiderLog.objects.create(
            task_id=task_id,
            spider_type='continent',
            status='running',
            progress=0,
            message='爬虫任务初始化中...',
            log_content=f'[{timezone.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动大洲爬虫任务\n'
        )

        # 启动爬虫进程
        try:
            # Determine the correct Python executable path
            python_executable = SPIDER_VENV_PYTHON if sys.platform == 'win32' and os.path.exists(SPIDER_VENV_PYTHON) else 'python'
            if python_executable == 'python':
                 log.add_log("[Warning] Using default 'python'. Spider venv path might be incorrect or non-existent.")
            else:
                 log.add_log(f"Using Python executable: {python_executable}")

            # 构造爬虫命令
            command = [
                python_executable, # Use the specific python from spider_venv
                '-m', 'scrapy', 'crawl', 'continent',
                '-a', f'task_id={task_id}'
            ]

            # 记录命令到日志
            log.add_log(f"执行命令: {' '.join(command)}")

            # 启动爬虫进程，使用Popen不阻塞当前请求
            # Ensure the CWD is the spider project directory
            process = subprocess.Popen(
                command,
                cwd=SPIDER_PROJECT_DIR, # Set cwd explicitly to the spider project dir
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            # 记录进程ID
            log.add_log(f"爬虫进程启动成功，PID: {process.pid}")
            log.message = "爬虫进程已启动，正在执行..."
            log.save()

            # --- Start Background Threads for Logging ---
            # Create daemon threads to read stdout and stderr without blocking
            stdout_thread = threading.Thread(
                target=stream_reader_thread,
                args=(process.stdout, log, '[stdout]'),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=stream_reader_thread,
                args=(process.stderr, log, '[stderr]'),
                daemon=True
            )
            stdout_thread.start()
            stderr_thread.start()
            # --- End Background Threads for Logging ---

            # 返回任务ID和初始状态
            return Response({
                'task_id': task_id,
                'status': 'running',
                'message': '爬虫进程已启动',
                'process_id': process.pid
            })

        except Exception as e:
            log.status = 'failed'
            log.message = f"启动爬虫失败: {str(e)}"
            log.add_log(f"错误: {str(e)}")
            log.save()
            return Response({
                'error': f'启动爬虫失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='start-country-spider')
    def start_country_spider(self, request):
        """启动国家爬虫"""
        # 创建一个唯一的任务ID
        task_id = f"country_{uuid.uuid4()}"

        # 创建爬虫日志记录
        log = SpiderLog.objects.create(
            task_id=task_id,
            spider_type='country',
            status='running',
            progress=0,
            message='爬虫任务初始化中...',
            log_content=f'[{timezone.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动国家爬虫任务\n'
        )

        # 启动爬虫进程
        try:
            # Determine the correct Python executable path
            python_executable = SPIDER_VENV_PYTHON if sys.platform == 'win32' and os.path.exists(SPIDER_VENV_PYTHON) else 'python'
            if python_executable == 'python':
                 log.add_log("[Warning] Using default 'python'. Spider venv path might be incorrect or non-existent.")
            else:
                 log.add_log(f"Using Python executable: {python_executable}")

            # 构造爬虫命令
            command = [
                python_executable, # Use the specific python from spider_venv
                '-m', 'scrapy', 'crawl', 'country',
                '-a', f'task_id={task_id}'
            ]

            # 记录命令到日志
            log.add_log(f"执行命令: {' '.join(command)}")

            # 启动爬虫进程，使用Popen不阻塞当前请求
            # Ensure the CWD is the spider project directory
            process = subprocess.Popen(
                command,
                cwd=SPIDER_PROJECT_DIR, # Set cwd explicitly to the spider project dir
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            # 记录进程ID
            log.add_log(f"爬虫进程启动成功，PID: {process.pid}")
            log.message = "爬虫进程已启动，正在执行..."
            log.save()

            # --- Start Background Threads for Logging ---
            # Create daemon threads to read stdout and stderr without blocking
            stdout_thread = threading.Thread(
                target=stream_reader_thread,
                args=(process.stdout, log, '[stdout]'),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=stream_reader_thread,
                args=(process.stderr, log, '[stderr]'),
                daemon=True
            )
            stdout_thread.start()
            stderr_thread.start()
            # --- End Background Threads for Logging ---

            # 返回任务ID和初始状态
            return Response({
                'task_id': task_id,
                'status': 'running',
                'message': '爬虫进程已启动',
                'process_id': process.pid
            })

        except Exception as e:
            log.status = 'failed'
            log.message = f"启动爬虫失败: {str(e)}"
            log.add_log(f"错误: {str(e)}")
            log.save()
            return Response({
                'error': f'启动爬虫失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='start-league-spider')
    def start_league_spider(self, request):
        """启动 LeagueSpider (只爬取联赛信息)"""
        league_ref_id = request.data.get('league_ref_id')
        season = request.data.get('season')

        if not league_ref_id or not season:
            return Response({'error': '需要 league_ref_id 和 season 参数'}, status=status.HTTP_400_BAD_REQUEST)

        task_id = str(uuid.uuid4())
        log_entry = SpiderLog.objects.create(
            task_id=task_id,
            spider_type='league',
            league=str(league_ref_id),
            season=season,
            status='pending'
        )
        serializer = SpiderLogSerializer(log_entry)

        cmd_args = [
            SPIDER_VENV_PYTHON,
            '-m', 'scrapy', 'crawl', 'league',
            '-a', f'league={league_ref_id}',
            '-a', f'season={season}',
            '-a', f'task_id={task_id}'
        ]

        try:
            process = subprocess.Popen(cmd_args, cwd=SPIDER_PROJECT_DIR,
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                     creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0)
            stdout_thread = threading.Thread(target=stream_reader_thread, args=(process.stdout, log_entry, '[STDOUT]'))
            stderr_thread = threading.Thread(target=stream_reader_thread, args=(process.stderr, log_entry, '[STDERR]'))
            stdout_thread.start()
            stderr_thread.start()
            log_entry.status = 'running'
            log_entry.save()
            logger.info(f"已启动 LeagueSpider 任务: {task_id} for League {league_ref_id}, Season {season}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            log_entry.status = 'failed'
            log_entry.message = f'启动爬虫失败: {str(e)}'
            log_entry.save()
            logger.error(f"启动 LeagueSpider 失败: {e}")
            return Response({'error': f'启动爬虫失败: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='start-cup-spider')
    def start_cup_spider(self, request):
        """启动 CupSpider"""
        cup_ref_id = request.data.get('cup_ref_id')
        season = request.data.get('season')

        if not cup_ref_id or not season:
            return Response({'error': '需要 cup_ref_id 和 season 参数'}, status=status.HTTP_400_BAD_REQUEST)

        task_id = str(uuid.uuid4())
        log_entry = SpiderLog.objects.create(
            task_id=task_id,
            spider_type='cup',
            league=str(cup_ref_id),
            season=season,
            status='pending'
        )
        serializer = SpiderLogSerializer(log_entry)

        cmd_args = [
            SPIDER_VENV_PYTHON,
            '-m', 'scrapy', 'crawl', 'cup',
            '-a', f'cup={cup_ref_id}',
            '-a', f'season={season}',
            '-a', f'task_id={task_id}'
        ]

        try:
            process = subprocess.Popen(cmd_args, cwd=SPIDER_PROJECT_DIR,
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                     creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0)
            stdout_thread = threading.Thread(target=stream_reader_thread, args=(process.stdout, log_entry, '[STDOUT]'))
            stderr_thread = threading.Thread(target=stream_reader_thread, args=(process.stderr, log_entry, '[STDERR]'))
            stdout_thread.start()
            stderr_thread.start()
            log_entry.status = 'running'
            log_entry.save()
            logger.info(f"已启动 CupSpider 任务: {task_id} for Cup {cup_ref_id}, Season {season}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            log_entry.status = 'failed'
            log_entry.message = f'启动爬虫失败: {str(e)}'
            log_entry.save()
            logger.error(f"启动 CupSpider 失败: {e}")
            return Response({'error': f'启动爬虫失败: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='start-match-spider')
    def start_match_spider(self, request):
        """启动 MatchSpider (处理比赛和赔率)"""
        competition_type = request.data.get('competition_type')
        competition_db_id = request.data.get('competition_db_id')
        source_competition_id = request.data.get('source_competition_id')
        source_sub_league_id = request.data.get('source_sub_league_id')
        season = request.data.get('season')
        target_match_source_id = request.data.get('target_match_source_id')

        if not season:
            return Response({'error': '需要 season 参数'}, status=status.HTTP_400_BAD_REQUEST)
        if not target_match_source_id and not competition_type:
             return Response({'error': '需要提供 competition_type 或 target_match_source_id'}, status=status.HTTP_400_BAD_REQUEST)
        if competition_type and competition_type not in ['league', 'cup']:
            return Response({'error': 'competition_type 必须是 league 或 cup'}, status=status.HTTP_400_BAD_REQUEST)
        if competition_type and not competition_db_id and not source_competition_id:
             return Response({'error': '需要提供 competition_db_id 或 source_competition_id'}, status=status.HTTP_400_BAD_REQUEST)

        task_id = str(uuid.uuid4())
        log_entry = SpiderLog.objects.create(
            task_id=task_id,
            spider_type='match',
            league=str(source_competition_id or target_match_source_id),
            season=season,
            status='pending'
        )
        serializer = SpiderLogSerializer(log_entry)

        cmd_args = [
            SPIDER_VENV_PYTHON,
            '-m', 'scrapy', 'crawl', 'match',
            '-a', f'season={season}',
            '-a', f'task_id={task_id}'
        ]
        if competition_type:
            cmd_args.extend(['-a', f'competition_type={competition_type}'])
        if competition_db_id:
            cmd_args.extend(['-a', f'competition_db_id={competition_db_id}'])
        if source_competition_id:
            cmd_args.extend(['-a', f'source_competition_id={source_competition_id}'])
        if source_sub_league_id:
            cmd_args.extend(['-a', f'source_sub_league_id={source_sub_league_id}'])
        if target_match_source_id:
             cmd_args.extend(['-a', f'target_match_source_id={target_match_source_id}'])

        try:
            process = subprocess.Popen(cmd_args, cwd=SPIDER_PROJECT_DIR,
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                     creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0)
            stdout_thread = threading.Thread(target=stream_reader_thread, args=(process.stdout, log_entry, '[STDOUT]'))
            stderr_thread = threading.Thread(target=stream_reader_thread, args=(process.stderr, log_entry, '[STDERR]'))
            stdout_thread.start()
            stderr_thread.start()
            log_entry.status = 'running'
            log_entry.save()
            logger.info(f"已启动 MatchSpider 任务: {task_id} for Season {season}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            log_entry.status = 'failed'
            log_entry.message = f'启动爬虫失败: {str(e)}'
            log_entry.save()
            logger.error(f"启动 MatchSpider 失败: {e}")
            return Response({'error': f'启动爬虫失败: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'], url_path='status')
    def get_task_status(self, request, pk=None):
        """获取爬虫任务状态"""
        try:
            log = self.get_object()
            serializer = self.get_serializer(log)
            return Response(serializer.data)
        except Exception as e:
            return Response({
                'error': f'获取任务状态失败: {str(e)}'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def pause(self, request, pk=None):
        """暂停爬虫任务"""
        spider_log = self.get_object()

        if spider_log.status != 'running':
            return Response({'error': '只能暂停正在运行的任务'}, status=status.HTTP_400_BAD_REQUEST)

        # 更新状态为暂停
        spider_log.status = 'paused'
        spider_log.message = '任务已暂停'
        spider_log.add_log('用户暂停了任务')
        spider_log.save()

        # 实际发送暂停信号到爬虫进程
        try:
            # 获取存储在日志中的PID
            pid_line = None
            log_lines = spider_log.log_content.split('\n')
            for line in log_lines:
                if 'PID:' in line:
                    pid_match = re.search(r'PID:\s*(\d+)', line)
                    if pid_match:
                        pid_line = pid_match.group(1)
                        break

            if pid_line:
                pid = int(pid_line)
                import os
                import signal
                import psutil

                # 检查进程是否存在
                if psutil.pid_exists(pid):
                    spider_log.add_log(f'正在暂停进程 PID: {pid}')

                    # 在Windows系统上使用psutil暂停进程，在类Unix系统上使用SIGSTOP信号
                    if sys.platform == 'win32':
                        try:
                            process = psutil.Process(pid)
                            process.suspend()
                            spider_log.add_log(f'已成功暂停进程 {pid}')
                        except psutil.NoSuchProcess:
                            spider_log.add_log(f'进程 {pid} 不存在，无法暂停')
                        except psutil.AccessDenied:
                            spider_log.add_log(f'没有权限暂停进程 {pid}，尝试使用其他方法')
                            # 如果没有权限，尝试使用taskkill（这会暂停整个进程树）
                            try:
                                # 正确的WMIC命令语法
                                result = subprocess.run(['WMIC', 'process', 'where', f'processid={pid}', 'call', 'suspend'],
                                                    capture_output=True, text=True, check=False)
                                if result.returncode == 0:
                                    spider_log.add_log(f'已成功使用WMIC暂停进程 {pid}')
                                else:
                                    spider_log.add_log(f'WMIC暂停进程失败: {result.stderr}')
                                    # 回退到taskkill方法尝试暂停
                                    subprocess.run(['taskkill', '/F', '/T', '/PID', str(pid)], check=False)
                                    spider_log.add_log(f'已尝试使用taskkill终止进程 {pid}')
                            except Exception as cmd_error:
                                spider_log.add_log(f'执行暂停命令时出错: {str(cmd_error)}')
                    else:
                        # 在Unix系统上使用SIGSTOP信号暂停进程
                        os.kill(pid, signal.SIGSTOP)
                        spider_log.add_log(f'已发送SIGSTOP信号暂停进程 {pid}')
                else:
                    spider_log.add_log(f'进程 {pid} 已不存在，无需暂停')
            else:
                spider_log.add_log(f'未找到进程PID信息，无法暂停爬虫进程')

        except Exception as e:
            spider_log.add_log(f'暂停爬虫进程时出错: {str(e)}')

        spider_log.save() # 保存暂停日志
        return Response({'status': 'paused'})

    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """恢复爬虫任务"""
        spider_log = self.get_object()

        # 允许从完成状态恢复，此时用户可能希望继续爬取
        if spider_log.status not in ['paused', 'completed', 'failed']:
            return Response({'error': '只能恢复已暂停或已完成的任务'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查日志内容是否包含任务完成的信息
        if spider_log.log_content and '爬虫任务完成' in spider_log.log_content:
            # 如果爬虫真的已经完成，则返回已完成状态
            spider_log.status = 'completed'
            spider_log.message = '爬虫任务已完成'
            spider_log.progress = 100
            spider_log.add_log('任务已经完成，无需恢复')
            spider_log.completed_at = timezone.now()
            spider_log.save()

            return Response({
                'status': 'completed',
                'message': '任务已经完成，无需恢复'
            })

        # 更新状态为运行中
        spider_log.status = 'running'
        spider_log.message = '任务已恢复运行'
        spider_log.add_log('用户恢复了任务')
        spider_log.save()

        # 实际发送恢复信号到爬虫进程
        try:
            # 获取存储在日志中的PID
            pid_line = None
            log_lines = spider_log.log_content.split('\n')
            for line in log_lines:
                if 'PID:' in line:
                    pid_match = re.search(r'PID:\s*(\d+)', line)
                    if pid_match:
                        pid_line = pid_match.group(1)
                        break

            if pid_line:
                pid = int(pid_line)
                import os
                import signal
                import psutil

                # 检查进程是否存在
                if psutil.pid_exists(pid):
                    spider_log.add_log(f'正在恢复进程 PID: {pid}')

                    # 在Windows系统上使用psutil恢复进程，在类Unix系统上使用SIGCONT信号
                    if sys.platform == 'win32':
                        try:
                            process = psutil.Process(pid)
                            process.resume()
                            spider_log.add_log(f'已成功恢复进程 {pid}')
                        except psutil.NoSuchProcess:
                            spider_log.add_log(f'进程 {pid} 不存在，无法恢复')
                        except psutil.AccessDenied:
                            spider_log.add_log(f'没有权限恢复进程 {pid}，尝试使用其他方法')
                            # 如果没有权限，尝试使用WMIC恢复进程
                            try:
                                # 正确的WMIC命令语法
                                result = subprocess.run(['WMIC', 'process', 'where', f'processid={pid}', 'call', 'resume'],
                                                    capture_output=True, text=True, check=False)
                                if result.returncode == 0:
                                    spider_log.add_log(f'已成功使用WMIC恢复进程 {pid}')
                                else:
                                    spider_log.add_log(f'WMIC恢复进程失败: {result.stderr}')
                                    # 提示用户可能需要手动重启
                                    spider_log.add_log(f'恢复进程失败，可能需要手动重启爬虫')
                            except Exception as cmd_error:
                                spider_log.add_log(f'执行恢复命令时出错: {str(cmd_error)}')
                    else:
                        # 在Unix系统上使用SIGCONT信号恢复进程
                        os.kill(pid, signal.SIGCONT)
                        spider_log.add_log(f'已发送SIGCONT信号恢复进程 {pid}')
                else:
                    spider_log.add_log(f'进程 {pid} 已不存在，无法恢复')

                    # 如果进程不存在但任务状态为暂停，可以考虑重新启动爬虫
                    if spider_log.status == 'paused':
                        spider_log.add_log(f'原进程已不存在，需要手动重新启动爬虫')
            else:
                spider_log.add_log(f'未找到进程PID信息，无法恢复爬虫进程')

        except Exception as e:
            spider_log.add_log(f'恢复爬虫进程时出错: {str(e)}')

        return Response({'status': 'running'})

    @action(detail=True, methods=['post'])
    def terminate(self, request, pk=None):
        """终止爬虫任务"""
        spider_log = self.get_object()

        if spider_log.status not in ['running', 'paused']:
            return Response({'error': '只能终止正在运行或已暂停的任务'}, status=status.HTTP_400_BAD_REQUEST)

        # 更新状态为失败
        spider_log.status = 'failed'
        spider_log.message = '任务被用户终止'
        spider_log.progress = 100  # 设置进度为100%，表示任务已结束
        spider_log.completed_at = timezone.now()
        spider_log.add_log('用户终止了任务')
        spider_log.save()

        # 实际发送终止信号到爬虫进程
        try:
            # 获取存储在日志中的PID
            pid_line = None
            log_lines = spider_log.log_content.split('\n')
            for line in log_lines:
                if 'PID:' in line:
                    pid_match = re.search(r'PID:\s*(\d+)', line)
                    if pid_match:
                        pid_line = pid_match.group(1)
                        break

            if pid_line:
                pid = int(pid_line)
                import os
                import signal
                import psutil

                # 检查进程是否存在
                if psutil.pid_exists(pid):
                    spider_log.add_log(f'正在终止进程 PID: {pid}')

                    # 在Windows系统上使用taskkill，在类Unix系统上使用os.kill
                    if sys.platform == 'win32':
                        try:
                            result = subprocess.run(['taskkill', '/F', '/T', '/PID', str(pid)],
                                                capture_output=True, text=True, check=False)
                            if result.returncode == 0:
                                spider_log.add_log(f'已成功使用taskkill终止进程树 {pid}')
                            else:
                                spider_log.add_log(f'taskkill终止进程失败: {result.stderr}')
                        except Exception as cmd_error:
                            spider_log.add_log(f'执行终止命令时出错: {str(cmd_error)}')
                    else:
                        try:
                            os.kill(pid, signal.SIGTERM)
                            spider_log.add_log(f'已发送SIGTERM信号到进程 {pid}')
                        except Exception as kill_error:
                            spider_log.add_log(f'发送SIGTERM信号时出错: {str(kill_error)}')

                    # 等待一会，然后检查进程是否已终止
                    try:
                        time_module.sleep(1)
                        if psutil.pid_exists(pid):
                            spider_log.add_log(f'进程 {pid} 未响应终止信号，正在强制终止')
                            if sys.platform == 'win32':
                                result = subprocess.run(['taskkill', '/F', '/T', '/PID', str(pid)],
                                                    capture_output=True, text=True, check=False)
                                spider_log.add_log(f'已尝试使用taskkill强制终止进程树 {pid}')
                            else:
                                try:
                                    os.kill(pid, signal.SIGKILL)
                                    spider_log.add_log(f'已发送SIGKILL信号强制终止进程 {pid}')
                                except Exception as kill_error:
                                    spider_log.add_log(f'发送SIGKILL信号时出错: {str(kill_error)}')
                    except Exception as wait_error:
                        spider_log.add_log(f'在等待进程终止时出错: {str(wait_error)}')
                else:
                    spider_log.add_log(f'进程 {pid} 已不存在，无需终止')
            else:
                spider_log.add_log(f'未找到进程PID信息，无法终止爬虫进程')

        except Exception as e:
            spider_log.add_log(f'终止爬虫进程时出错: {str(e)}')

        return Response({'status': 'terminated'})

    @action(detail=True, methods=['post'], url_path='update-status')
    def update_status(self, request, pk=None):
        """接收爬虫发送的状态更新"""
        try:
            spider_log = self.get_object()

            # 获取请求中的参数
            progress = request.data.get('progress', spider_log.progress)
            message = request.data.get('message', '')
            log_entry = request.data.get('log_entry', '')

            # 只有当爬虫正在运行或暂停时才接受状态更新
            if spider_log.status in ['running', 'paused']:
                # 更新爬虫日志对象
                spider_log.progress = progress
                if message:
                    spider_log.message = message

                # 添加日志条目
                if log_entry:
                    spider_log.add_log(log_entry)

                # 如果进度达到100% 并且状态是 running, 标记为已完成 (避免覆盖 failed 状态)
                if progress >= 100 and spider_log.status == 'running':
                    spider_log.status = 'completed'
                    spider_log.completed_at = timezone.now()

                spider_log.save()

                return Response({
                    'status': 'success',
                    'current_progress': spider_log.progress,
                    'current_status': spider_log.status
                })
            elif spider_log.status in ['completed', 'failed']:
                 # 如果任务已完成或失败，记录日志但忽略更新
                 logger.warning(f"忽略来自已完成/失败任务 {pk} 的日志更新: {log_entry}")
                 return Response({
                    'status': 'ignored',
                    'message': f'任务已完成或失败 (状态: {spider_log.status})，忽略日志更新。'
                 }, status=status.HTTP_200_OK) # 返回成功但表明已忽略
            else:
                # 其他未知状态
                logger.error(f'无法更新任务 {pk}，未知状态: {spider_log.status}')
                return Response({
                    'status': 'error',
                    'message': f'无法更新状态，当前状态为: {spider_log.status}'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'更新状态失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'], url_path='complete')
    def complete_task(self, request, pk=None):
        """完成爬虫任务，设置最终状态"""
        try:
            spider_log = self.get_object()

            # 获取请求中的参数
            success = request.data.get('success', True)  # 默认成功
            message = request.data.get('message', '')

            # 更新爬虫日志对象
            spider_log.status = 'completed' if success else 'failed'
            spider_log.progress = 100 if success else spider_log.progress
            if message:
                spider_log.message = message
                spider_log.add_log(message)

            # 记录完成时间
            spider_log.completed_at = timezone.now()
            spider_log.save()

            return Response({
                'status': 'success',
                'task_id': spider_log.task_id,
                'final_status': spider_log.status
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'标记任务完成失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Helper function to read stream in a separate thread
def stream_reader_thread(stream, log_entry, prefix):
    """Reads a stream line by line and appends to the log_entry.log_content."""
    try:
        for line in iter(stream.readline, ''):
            if not line:
                break
            try:
                # Ensure log_entry is refreshed from DB to avoid stale data if accessed elsewhere
                log_entry.refresh_from_db(fields=['log_content'])
                timestamp = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
                log_entry.log_content += f"\n[{timestamp}] {prefix} {line.strip()}"
                log_entry.save(update_fields=['log_content', 'updated_at'])
            except Exception as db_error:
                # Log DB errors but continue reading the stream
                logger.error(f"Error saving log for task {log_entry.task_id}: {db_error}")
        stream.close()
    except Exception as e:
        logger.error(f"Error reading stream for task {log_entry.task_id}: {e}")

# 新增：用于接收爬虫通知的 API 视图
class NotifyMatchUpdateView(APIView):
    """
    接收来自 Scrapy 爬虫的 POST 请求，
    触发 SSE 通知。
    """
    permission_classes = [AllowAny] # 允许任何来源的请求（因为是内部调用）
    authentication_classes = [] # 不需要认证

    def post(self, request, *args, **kwargs):
        match_id = request.data.get('match_id')
        update_type = request.data.get('update_type')

        if not match_id or not update_type:
            logger.warning(f"接收到无效的比赛更新通知: data={request.data}")
            return Response(
                {'error': '缺少 match_id 或 update_type 参数'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            logger.info(f"接收到爬虫通知: match_id={match_id}, update_type={update_type}")
            # 调用 SSE 工具函数发送事件
            notify_match_update(match_id=str(match_id), update_type=str(update_type))
            return Response({'status': 'notification sent'}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"处理爬虫通知时出错: {e}", exc_info=True)
            return Response(
                {'error': '发送 SSE 通知时服务器内部错误'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
