from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from auth_api.models import UserProfile


class Command(BaseCommand):
    help = '确保所有用户都有对应的档案记录'

    def handle(self, *args, **options):
        users = User.objects.all()
        created_count = 0
        existing_count = 0
        error_count = 0
        
        self.stdout.write(self.style.SUCCESS(f'开始处理 {users.count()} 个用户...'))
        
        for user in users:
            try:
                profile, created = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'notification_match': True,
                        'notification_odds': True,
                        'notification_analysis': False
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(self.style.SUCCESS(f'为用户 {user.username} 创建了新档案'))
                else:
                    existing_count += 1
                    self.stdout.write(f'用户 {user.username} 已有档案')
            except Exception as e:
                error_count += 1
                self.stdout.write(self.style.ERROR(f'处理用户 {user.username} 时出错: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS('='*50))
        self.stdout.write(self.style.SUCCESS(f'处理完成！创建了 {created_count} 个新档案，已有 {existing_count} 个档案，{error_count} 个错误。')) 