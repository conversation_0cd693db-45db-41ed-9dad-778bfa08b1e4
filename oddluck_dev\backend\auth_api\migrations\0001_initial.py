# Generated by Django 5.1.7 on 2025-03-28 17:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avatar', models.TextField(blank=True, help_text='Base64编码的头像图片', null=True)),
                ('bio', models.TextField(blank=True, help_text='用户个人简介', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('notification_match', models.BooleanField(default=True, help_text='比赛开始通知')),
                ('notification_odds', models.BooleanField(default=True, help_text='赔率变化通知')),
                ('notification_analysis', models.BooleanField(default=False, help_text='新分析通知')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
