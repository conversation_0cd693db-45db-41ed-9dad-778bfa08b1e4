from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver

# Create your models here.
class UserProfile(models.Model):
    """用户档案模型，存储用户的额外信息"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    avatar = models.TextField(blank=True, null=True, help_text='Base64编码的头像图片')
    bio = models.TextField(blank=True, null=True, help_text='用户个人简介')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    notification_match = models.BooleanField(default=True, help_text='比赛开始通知')
    notification_odds = models.BooleanField(default=True, help_text='赔率变化通知')
    notification_analysis = models.BooleanField(default=False, help_text='新分析通知')

    def __str__(self):
        return f"{self.user.username}的个人档案"

# 自动为新用户创建档案
@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    if hasattr(instance, 'profile'):
        instance.profile.save()
    else:
        UserProfile.objects.create(user=instance)
