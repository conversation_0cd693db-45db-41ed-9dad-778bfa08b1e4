from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from .views import (
    MyTokenObtainPairView, 
    RegisterView, 
    getRoutes, 
    getUserInfo,
    getUserProfile,
    updateUserProfile
)

urlpatterns = [
    path('', getRoutes, name='routes'),
    path('login/', MyTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('register/', RegisterView.as_view(), name='register'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('user/', getUserInfo, name='user_info'),
    path('profile/', getUserProfile, name='user_profile'),
    path('profile/update/', updateUserProfile, name='update_user_profile'),
] 