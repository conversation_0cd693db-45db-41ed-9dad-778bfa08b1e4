from django.shortcuts import render
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth.models import User
from django.utils import timezone

from .serializers import (
    MyTokenObtainPairSerializer, 
    RegisterSerializer, 
    UserSerializer,
    UserProfileSerializer
)
from .models import UserProfile

class MyTokenObtainPairView(TokenObtainPairView):
    """自定义登录视图，使用自定义序列化器"""
    serializer_class = MyTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        # 调用父类方法获取令牌
        response = super().post(request, *args, **kwargs)
        
        # 如果登录成功，更新最后登录时间
        if response.status_code == 200:
            username = request.data.get('username')
            try:
                user = User.objects.get(username=username)
                user.last_login = timezone.now()
                user.save(update_fields=['last_login'])
                print(f"已更新用户 {username} 的最后登录时间为 {user.last_login}")
            except User.DoesNotExist:
                print(f"找不到用户 {username}")
        
        return response

class RegisterView(APIView):
    """用户注册视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "用户注册成功，请登录"}, 
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def getRoutes(request):
    """获取API路由信息"""
    routes = [
        '/api/auth/login/',
        '/api/auth/register/',
        '/api/auth/token/refresh/',
        '/api/auth/user/',
        '/api/auth/profile/',
        '/api/auth/profile/update/'
    ]
    return Response(routes)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def getUserInfo(request):
    """获取当前登录用户信息"""
    user = request.user
    serializer = UserSerializer(user)
    return Response(serializer.data)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def getUserProfile(request):
    """获取当前登录用户的档案信息"""
    try:
        # 获取用户对象
        user = request.user
        
        # 强制创建用户档案如果不存在
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'notification_match': True,
                'notification_odds': True,
                'notification_analysis': False
            }
        )
        
        # 日志记录
        if created:
            print(f"Created new profile for user {user.username}")
            
        # 序列化档案数据
        serializer = UserProfileSerializer(profile)
        response_data = serializer.data
        
        # 添加用户模型中的最新信息
        response_data.update({
            'email': user.email,
            'username': user.username,
            'date_joined': user.date_joined,
            'last_login': user.last_login
        })
        
        return Response(response_data)
    except Exception as e:
        print(f"Error in getUserProfile: {str(e)}")
        # 返回一个最小化的响应，而不是错误
        return Response({
            "id": None,
            "username": request.user.username,
            "email": request.user.email,
            "avatar": None,
            "bio": "",
            "notification_match": True,
            "notification_odds": True,
            "notification_analysis": False,
            "created_at": None,
            "updated_at": None,
            "date_joined": request.user.date_joined,
            "updated_at": None
        })

@api_view(['PUT', 'PATCH'])
@permission_classes([permissions.IsAuthenticated])
def updateUserProfile(request):
    """更新当前登录用户的档案信息"""
    try:
        # 获取当前用户
        user = request.user
        
        # 如果请求中包含email字段，则更新User模型
        if 'email' in request.data:
            user.email = request.data['email']
            user.save()
            print(f"Updated email for user {user.username} to {user.email}")
            
        # 如果请求中包含username字段，则更新User模型的用户名
        if 'username' in request.data:
            # 确保用户名是唯一的
            new_username = request.data['username']
            if new_username != user.username and User.objects.filter(username=new_username).exists():
                return Response(
                    {"username": ["该用户名已被使用"]},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            old_username = user.username
            user.username = new_username
            user.save()
            print(f"Updated username from {old_username} to {new_username}")
        
        # 强制创建用户档案如果不存在
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'notification_match': True,
                'notification_odds': True,
                'notification_analysis': False
            }
        )
        
        # 日志记录
        if created:
            print(f"Created new profile for user {request.user.username} during update")
        
        # 使用部分更新模式，只更新提供的字段
        # 从请求数据中移除email和username字段，因为它们已在User模型中更新
        profile_data = request.data.copy()
        if 'email' in profile_data:
            profile_data.pop('email')
        if 'username' in profile_data:
            profile_data.pop('username')
            
        serializer = UserProfileSerializer(
            profile, 
            data=profile_data, 
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save()
            
            # 创建包含更新后的User信息和Profile信息的合并响应
            response_data = serializer.data
            response_data.update({
                'email': user.email,
                'username': user.username
            })
            
            return Response(response_data)
        else:
            print(f"Validation error in updateUserProfile: {serializer.errors}")
            return Response(
                serializer.errors, 
                status=status.HTTP_400_BAD_REQUEST
            )
    except Exception as e:
        print(f"Error in updateUserProfile: {str(e)}")
        return Response(
            {"error": f"更新用户档案失败: {str(e)}"}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
