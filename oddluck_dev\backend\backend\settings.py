"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 3.2.8.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

from pathlib import Path
import os
from dotenv import load_dotenv
from datetime import timedelta

# 加载.env文件中的环境变量
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env'))

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-rqckb9*sgh#f5+9z7vd@!_c9ids7#n2=)qwjr&+!1d#ep%c2j8')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', 'True') == 'True'

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_apscheduler',
    'rest_framework',
    'corsheaders',
    'django_filters',
    'rest_framework_simplejwt',
    'api',
    'llm_analysis',
    'auth_api',
    'knowledge_api',
    'scheduler',
    'django_eventstream',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Whitenoise settings
WHITENOISE_ALLOW_ALL_ORIGINS = True
WHITENOISE_ADD_HEADERS_FUNCTION = None

ROOT_URLCONF = 'backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'backend.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

# 检测是否是本地数据库
DB_HOST = os.getenv('DB_HOST', 'localhost')
IS_LOCAL_DB = DB_HOST == 'localhost' or DB_HOST == '127.0.0.1'

# 根据本地/云端设置正确的SSL模式
if IS_LOCAL_DB:
    SSL_MODE = os.getenv('LOCAL_SSL_MODE', 'disable')
else:
    SSL_MODE = os.getenv('SSL_MODE', 'require')

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'oddluck'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', '888'),  # 设置本地数据库密码
        'HOST': DB_HOST,
        'PORT': os.getenv('DB_PORT', '5432'),
        'OPTIONS': {
            'client_encoding': 'UTF8',
            'sslmode': SSL_MODE,  # 根据环境变量动态设置
        }
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

# 设置为亚洲/上海时区，与数据库时区一致
TIME_ZONE = 'Asia/Shanghai'

# 禁用时区转换，确保数据库时间与应用时间一致
USE_TZ = False

# 自定义日期和时间格式，确保一致性
DATE_FORMAT = 'Y-m-d'
DATETIME_FORMAT = 'Y-m-d H:i:s'
DATE_INPUT_FORMATS = ['%Y-%m-%d']
DATETIME_INPUT_FORMATS = ['%Y-%m-%d %H:%M:%S']

USE_I18N = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# 静态文件配置
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'
WHITENOISE_USE_FINDERS = True
WHITENOISE_ROOT = os.path.join(BASE_DIR, 'staticfiles')
WHITENOISE_INDEX_FILE = True

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True
# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:5173",  # Vue.js 开发服务器
# ]
CORS_ALLOW_CREDENTIALS = True
CORS_EXPOSE_HEADERS = ['Content-Type', 'X-CSRFToken']
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]
CORS_ALLOW_HEADERS = [
    'Accept',
    'Accept-Encoding',
    'Authorization',
    'Content-Type',
    'DNT',
    'Origin',
    'User-Agent',
    'X-CSRFToken',
    'X-Requested-With',
]

# Whitenoise settings
WHITENOISE_ALLOW_ALL_ORIGINS = True
WHITENOISE_ADD_HEADERS_FUNCTION = None

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'UNAUTHENTICATED_USER': None,  # 未认证用户设置为None，而不是AnonymousUser
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    # 自定义JSON序列化时的日期时间格式，与数据库保持一致
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S',
    'DATE_FORMAT': '%Y-%m-%d',
    'DATETIME_INPUT_FORMATS': ['%Y-%m-%d %H:%M:%S'],
    'DATE_INPUT_FORMATS': ['%Y-%m-%d'],
}

# JWT settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
}

# LLM配置
LLM_API_KEY = os.environ.get('LLM_API_KEY', '')
LLM_BASE_URL = os.environ.get('LLM_BASE_URL', 'https://api.siliconflow.cn/v1')
LLM_MODEL_NAME = os.environ.get('LLM_MODEL_NAME', 'deepseek-ai/DeepSeek-V3')

# Embedding配置 - 二选一配置方案

# 方案1: 通过HuggingFace本地模型获取嵌入向量
EMBEDDING_MODEL = 'BAAI/bge-large-zh-v1.5'
EMBEDDING_TYPE = 'huggingface'
EMBEDDING_DEVICE = 'cpu'
# 指定缓存目录，可选，不设置则使用默认缓存目录
# EMBEDDING_CACHE_DIR = os.path.join(BASE_DIR, 'models')
VECTOR_STORE_ENABLED = True

# 方案2: 通过Ollama获取嵌入向量（需要在本地安装并运行Ollama）
# EMBEDDING_MODEL = 'nomic-embed-text'  # 或者'BAAI/bge-large-zh-v1.5'（如果已导入Ollama）
# EMBEDDING_TYPE = 'ollama'
# OLLAMA_BASE_URL = 'http://localhost:11434'

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,  # 不禁用 Django 已有的日志记录器
    'formatters': { # 定义日志格式
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': { # 定义日志处理器
        'console': {
            'level': 'DEBUG',  # 处理 DEBUG 及以上级别的日志
            'class': 'logging.StreamHandler', # 输出到控制台
            'formatter': 'verbose' # 使用上面定义的 verbose 格式
        },
        # 你可以添加其他处理器，比如输出到文件
        # 'file': {
        #     'level': 'INFO',
        #     'class': 'logging.FileHandler',
        #     'filename': BASE_DIR / 'django.log', # 需要定义 BASE_DIR 或使用绝对路径
        #     'formatter': 'verbose',
        # },
    },
    'loggers': { # 定义日志记录器
        'django': { # 处理 Django 框架自身的日志
            'handlers': ['console'],
            'level': 'INFO', # Django 自身的日志级别可以设为 INFO 或 WARNING
            'propagate': False, # 防止日志向根记录器传递
        },
        'llm_analysis': { # 处理我们自定义的 llm_analysis 应用的日志
            'handlers': ['console'],
            'level': 'DEBUG', # 设置为 DEBUG 以捕获所有我们添加的日志
            'propagate': False,
        },
        'api': { # 处理我们自定义的 api 应用的日志 (如果需要)
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'scheduler': { # 添加 scheduler app 的 logger 配置
            'handlers': ['console'],
            'level': 'INFO',
        },
        'django_apscheduler': { # apscheduler 自身的日志
            'handlers': ['console'],
            'level': 'INFO',
        }
         # 可以为其他应用添加 logger
    },
    'root': { # 根记录器，捕获所有未被上面 logger 处理的日志
        'handlers': ['console'],
        'level': 'INFO', # 根记录器级别可以设为 INFO
    }
}

SCRAPYD_URL = 'http://localhost:6800/schedule.json'