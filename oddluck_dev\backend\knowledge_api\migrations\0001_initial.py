# Generated by Django 5.0.4 on 2025-05-25 00:52

import django.db.models.deletion
import knowledge_api.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to=settings.AUTH_USER_MODEL, verbose_name='创建用户')),
            ],
            options={
                'verbose_name': '知识库分类',
                'verbose_name_plural': '知识库分类',
                'db_table': 'knowledge_api_category',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KnowledgeFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='文件名称')),
                ('file', models.FileField(upload_to=knowledge_api.models.knowledge_file_path, verbose_name='文件')),
                ('size', models.PositiveIntegerField(verbose_name='文件大小(字节)')),
                ('upload_time', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('is_vectorized', models.BooleanField(default=False, verbose_name='是否已向量化')),
                ('vectorization_status', models.CharField(choices=[('pending', '等待处理'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '处理失败')], default='pending', max_length=20, verbose_name='向量化状态')),
                ('vectorization_error', models.TextField(blank=True, null=True, verbose_name='向量化错误信息')),
                ('vectorized_at', models.DateTimeField(blank=True, null=True, verbose_name='向量化完成时间')),
                ('vector_store_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='向量存储ID')),
                ('embedding_model', models.CharField(blank=True, max_length=100, null=True, verbose_name='嵌入模型')),
                ('chunks_count', models.PositiveIntegerField(default=0, verbose_name='文本分块数量')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='knowledge_api.category', verbose_name='所属分类')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_files', to=settings.AUTH_USER_MODEL, verbose_name='上传用户')),
            ],
            options={
                'verbose_name': '知识库文件',
                'verbose_name_plural': '知识库文件',
                'db_table': 'knowledge_api_knowledgefile',
                'ordering': ['-upload_time'],
            },
        ),
    ]
