from django.db import models
from django.conf import settings

class Category(models.Model):
    """知识库分类模型"""
    name = models.CharField(max_length=100, verbose_name="分类名称")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="创建用户", related_name="categories")

    class Meta:
        db_table = 'knowledge_api_category'
        verbose_name = "知识库分类"
        verbose_name_plural = "知识库分类"
        ordering = ['-created_at']

    def __str__(self):
        return self.name
    
    @property
    def file_count(self):
        return self.files.count()

def knowledge_file_path(instance, filename):
    # 文件将被上传到 MEDIA_ROOT/knowledge_files/user_<id>/<category_id>/<filename>
    return f'knowledge_files/user_{instance.user.id}/{instance.category.id}/{filename}'

class KnowledgeFile(models.Model):
    """知识库文件模型"""
    # 向量化状态选项
    STATUS_PENDING = 'pending'
    STATUS_PROCESSING = 'processing'
    STATUS_COMPLETED = 'completed'
    STATUS_FAILED = 'failed'
    STATUS_CHOICES = [
        (STATUS_PENDING, '等待处理'),
        (STATUS_PROCESSING, '处理中'),
        (STATUS_COMPLETED, '已完成'),
        (STATUS_FAILED, '处理失败'),
    ]
    
    name = models.CharField(max_length=255, verbose_name="文件名称")
    file = models.FileField(upload_to=knowledge_file_path, verbose_name="文件")
    size = models.PositiveIntegerField(verbose_name="文件大小(字节)")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="所属分类", related_name="files")
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="上传用户", related_name="knowledge_files")
    upload_time = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")
    
    # 向量化相关字段
    is_vectorized = models.BooleanField(default=False, verbose_name="是否已向量化")
    vectorization_status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES,
        default=STATUS_PENDING,
        verbose_name="向量化状态"
    )
    vectorization_error = models.TextField(blank=True, null=True, verbose_name="向量化错误信息")
    vectorized_at = models.DateTimeField(null=True, blank=True, verbose_name="向量化完成时间")
    vector_store_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="向量存储ID")
    embedding_model = models.CharField(max_length=100, blank=True, null=True, verbose_name="嵌入模型")
    chunks_count = models.PositiveIntegerField(default=0, verbose_name="文本分块数量")

    class Meta:
        db_table = 'knowledge_api_knowledgefile'
        verbose_name = "知识库文件"
        verbose_name_plural = "知识库文件"
        ordering = ['-upload_time']

    def __str__(self):
        return self.name 