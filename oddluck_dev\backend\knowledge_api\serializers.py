from rest_framework import serializers
from .models import Category, KnowledgeFile

class KnowledgeFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = KnowledgeFile
        fields = [
            'id', 'name', 'file', 'size', 'category', 'upload_time',
            'is_vectorized', 'vectorization_status', 'vectorization_error',
            'vectorized_at', 'vector_store_id', 'embedding_model', 'chunks_count'
        ]
        read_only_fields = [
            'id', 'upload_time', 'is_vectorized', 'vectorization_status', 
            'vectorization_error', 'vectorized_at', 'vector_store_id', 
            'embedding_model', 'chunks_count'
        ]

    def create(self, validated_data):
        # 获取当前用户并设置为文件所有者
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)

class CategorySerializer(serializers.ModelSerializer):
    file_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Category
        fields = ['id', 'name', 'created_at', 'updated_at', 'file_count']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        # 获取当前用户并设置为分类创建者
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)

class CategoryDetailSerializer(CategorySerializer):
    files = KnowledgeFileSerializer(many=True, read_only=True)
    
    class Meta(CategorySerializer.Meta):
        fields = CategorySerializer.Meta.fields + ['files'] 