from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import FileResponse
from django.shortcuts import get_object_or_404
import os
import logging
import threading
import datetime
from django.conf import settings
from django.utils import timezone
import signal
from functools import wraps

from .models import Category, KnowledgeFile
from .serializers import CategorySerializer, CategoryDetailSerializer, KnowledgeFileSerializer

logger = logging.getLogger(__name__)

class IsOwner(permissions.BasePermission):
    """
    自定义权限：只允许对象的所有者访问
    """
    def has_object_permission(self, request, view, obj):
        return obj.user == request.user

class CategoryViewSet(viewsets.ModelViewSet):
    """
    知识库分类视图集
    """
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated, IsOwner]

    def get_queryset(self):
        # 只返回当前用户创建的分类
        return Category.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        # 详情视图使用包含文件列表的序列化器
        if self.action == 'retrieve':
            return CategoryDetailSerializer
        return CategorySerializer
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class KnowledgeFileViewSet(viewsets.ModelViewSet):
    """
    知识库文件视图集
    """
    serializer_class = KnowledgeFileSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwner]

    def get_queryset(self):
        # 只返回当前用户上传的文件
        return KnowledgeFile.objects.filter(user=self.request.user)
    
    def create(self, request, *args, **kwargs):
        # 处理文件上传
        if 'file' not in request.FILES:
            return Response({'error': '请选择要上传的文件'}, status=status.HTTP_400_BAD_REQUEST)
        
        file_obj = request.FILES['file']
        category_id = request.data.get('category')
        
        if not category_id:
            return Response({'error': '请选择分类'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查文件类型
        file_extension = os.path.splitext(file_obj.name)[1].lower()
        allowed_extensions = ['.pdf', '.docx', '.xlsx', '.txt', '.md', '.html', '.pptx', '.epub', '.doc', '.xls', '.ppt']
        if file_extension not in allowed_extensions:
            return Response({'error': f'不支持的文件类型 {file_extension}，请上传以下格式：PDF、DOCX、XLSX、TXT、MD、HTML、PPTX、EPUB 等'}, 
                            status=status.HTTP_400_BAD_REQUEST)
        
        # 检查分类是否存在且属于当前用户
        try:
            category = Category.objects.get(id=category_id, user=request.user)
        except Category.DoesNotExist:
            return Response({'error': '分类不存在'}, status=status.HTTP_404_NOT_FOUND)
        
        # 创建文件记录
        knowledge_file = KnowledgeFile(
            name=file_obj.name,
            file=file_obj,
            size=file_obj.size,
            category=category,
            user=request.user,
            vectorization_status=KnowledgeFile.STATUS_PENDING
        )
        knowledge_file.save()
        
        # 启动异步向量化处理
        thread = threading.Thread(
            target=self._process_file_vectorization,
            args=(knowledge_file.id,)
        )
        thread.daemon = True
        thread.start()
        
        serializer = self.get_serializer(knowledge_file)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    def _process_file_vectorization(self, file_id):
        """异步处理文件向量化"""
        start_time = timezone.now()
        try:
            # 获取文件记录
            knowledge_file = KnowledgeFile.objects.get(id=file_id)
            
            # 更新状态为处理中
            knowledge_file.vectorization_status = KnowledgeFile.STATUS_PROCESSING
            knowledge_file.save()
            
            # 导入所需服务
            from llm_analysis.file_service import FileProcessingService
            from llm_analysis.vector_store import VectorStoreService
            from django.conf import settings
            
            # 记录使用的嵌入模型
            embedding_model = getattr(settings, 'EMBEDDING_MODEL', 'BAAI/bge-large-zh-v1.5')
            embedding_type = getattr(settings, 'EMBEDDING_TYPE', 'huggingface')
            embedding_device = getattr(settings, 'EMBEDDING_DEVICE', 'cpu')
            
            logger.info(f"使用嵌入模型: {embedding_model}, 类型: {embedding_type}, 设备: {embedding_device}")
            
            # 初始化服务
            file_service = FileProcessingService()
            vector_service = VectorStoreService()
            
            # 获取文件路径和类型
            file_path = knowledge_file.file.path
            file_type = os.path.splitext(knowledge_file.name)[1].lower().lstrip('.')
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            
            # 检查文件大小
            if file_size_mb > 50:  # 大于50MB的文件可能处理慢
                logger.warning(f"大文件警告: {file_path}, 大小: {file_size_mb:.2f}MB")
            
            # 定义超时装饰器
            def timeout(seconds):
                def decorator(func):
                    @wraps(func)
                    def wrapper(*args, **kwargs):
                        def handle_timeout(signum, frame):
                            raise TimeoutError(f"处理超时，文件可能过大或复杂 ({seconds}秒)")
                        
                        # 设置信号处理
                        if os.name != 'nt':  # 在非Windows系统上使用信号
                            original_handler = signal.signal(signal.SIGALRM, handle_timeout)
                            signal.alarm(seconds)
                        
                        try:
                            result = func(*args, **kwargs)
                        finally:
                            if os.name != 'nt':  # 在非Windows系统上恢复信号
                                signal.alarm(0)
                                signal.signal(signal.SIGALRM, original_handler)
                        
                        return result
                    return wrapper
                return decorator
            
            # 提取文本，设置较长的超时时间
            logger.info(f"开始处理文件: {file_path}, 类型: {file_type}")
            
            # 对大文件设置更长的超时时间
            timeout_seconds = 600 if file_size_mb > 10 else 300
            
            # 提取文本（Windows系统不使用信号超时）
            if os.name == 'nt':
                # Windows系统，不使用信号超时，直接调用
                content = file_service.extract_text(file_path, file_type)
            else:
                # 非Windows系统，使用信号超时
                @timeout(timeout_seconds)
                def extract_with_timeout():
                    return file_service.extract_text(file_path, file_type)
                
                content = extract_with_timeout()
            
            # 检查处理时间是否已经过长
            elapsed_time = (timezone.now() - start_time).total_seconds()
            if elapsed_time > 120:  # 超过2分钟
                logger.warning(f"文件处理时间较长: {elapsed_time:.2f}秒, 文件: {file_path}")
            
            # 分割文本
            text_chunks = file_service.split_text(content)
            chunks_count = len(text_chunks)
            logger.info(f"文件分割完成，共 {chunks_count} 个文本块")
            
            # 检查文本块数量
            if chunks_count > 1000:
                logger.warning(f"文本块数量过多: {chunks_count}, 文件: {file_path}")
            
            # 创建元数据
            metadata = [{
                'source': knowledge_file.name,
                'title': knowledge_file.name,
                'category': knowledge_file.category.name,
                'chunk_id': i
            } for i in range(chunks_count)]
            
            # 创建向量存储
            vector_store_id = vector_service.create_vector_store(text_chunks, metadata)
            
            # 更新知识库条目
            knowledge_file.vectorization_status = KnowledgeFile.STATUS_COMPLETED
            knowledge_file.is_vectorized = True
            knowledge_file.vector_store_id = vector_store_id
            knowledge_file.chunks_count = chunks_count
            knowledge_file.embedding_model = embedding_model
            knowledge_file.vectorized_at = timezone.now()
            knowledge_file.save()
            
            total_time = (timezone.now() - start_time).total_seconds()
            logger.info(f"文件向量化完成: {file_path}, store_id: {vector_store_id}, 耗时: {total_time:.2f}秒")
            
        except TimeoutError as timeout_err:
            logger.error(f"向量化处理超时: {str(timeout_err)}")
            try:
                # 更新状态为失败
                knowledge_file = KnowledgeFile.objects.get(id=file_id)
                knowledge_file.vectorization_status = KnowledgeFile.STATUS_FAILED
                knowledge_file.vectorization_error = f"文件处理超时。文件可能过大或格式复杂，请尝试分割文件或转换格式后重试。详细错误: {str(timeout_err)}"
                knowledge_file.save()
            except Exception as update_err:
                logger.error(f"更新失败状态时出错: {str(update_err)}")
        except Exception as e:
            logger.error(f"向量化处理失败: {str(e)}", exc_info=True)
            try:
                # 更新状态为失败
                knowledge_file = KnowledgeFile.objects.get(id=file_id)
                knowledge_file.vectorization_status = KnowledgeFile.STATUS_FAILED
                # 提供更具体的错误信息
                error_message = str(e)
                if "memory" in error_message.lower():
                    error_message = f"内存不足。文件过大，请尝试分割文件或使用更小的文件。详细错误: {error_message}"
                elif "timeout" in error_message.lower():
                    error_message = f"处理超时。文件处理耗时过长，请尝试分割文件或转换格式后重试。详细错误: {error_message}"
                elif "format" in error_message.lower() or "invalid" in error_message.lower():
                    error_message = f"文件格式错误。文件可能损坏或格式不支持，请检查文件并尝试转换格式。详细错误: {error_message}"
                knowledge_file.vectorization_error = error_message
                knowledge_file.save()
            except Exception as update_err:
                logger.error(f"更新失败状态时出错: {str(update_err)}")
    
    @action(detail=True, methods=['post'])
    def retry_vectorization(self, request, pk=None):
        """重试向量化处理"""
        file = self.get_object()
        
        # 检查当前状态
        if file.vectorization_status == KnowledgeFile.STATUS_PROCESSING:
            return Response({'error': '文件正在处理中，请稍后再试'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 重置状态
        file.vectorization_status = KnowledgeFile.STATUS_PENDING
        file.vectorization_error = None
        file.save()
        
        # 启动异步处理
        thread = threading.Thread(
            target=self._process_file_vectorization,
            args=(file.id,)
        )
        thread.daemon = True
        thread.start()
        
        return Response({'message': '已重新启动向量化处理'})
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """下载文件"""
        file = self.get_object()
        file_path = file.file.path
        
        if os.path.exists(file_path):
            response = FileResponse(open(file_path, 'rb'))
            response['Content-Disposition'] = f'attachment; filename="{file.name}"'
            return response
        
        return Response({'error': '文件不存在'}, status=status.HTTP_404_NOT_FOUND) 