"""
下载BAAI/bge-large-zh-v1.5模型到本地缓存
"""
import os
import logging
from sentence_transformers import SentenceTransformer
import sys

# 设置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_model():
    """下载指定的嵌入模型到本地缓存"""
    try:
        model_name = 'BAAI/bge-large-zh-v1.5'
        logger.info(f"开始下载模型: {model_name}")
        
        # 方法1: 使用sentence_transformers下载，会自动缓存模型
        logger.info("使用SentenceTransformer下载模型...")
        model = SentenceTransformer(model_name)
        logger.info(f"模型下载成功并初始化: {model_name}")
        
        # 获取模型缓存路径
        # 注意: HfFolder.get_cache_dir方法已弃用，使用环境变量方式获取
        cache_dir = os.environ.get("HF_HOME", os.path.join(os.path.expanduser("~"), ".cache", "huggingface"))
        logger.info(f"模型缓存目录: {cache_dir}")
        
        # 打印模型信息
        logger.info(f"模型维度: {model.get_sentence_embedding_dimension()}")
        
        return True
    except Exception as e:
        logger.error(f"下载模型失败: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("开始下载模型到本地...")
    success = download_model()
    logger.info(f"下载结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1) 