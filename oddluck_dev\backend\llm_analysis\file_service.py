import os
import logging
from typing import List, Dict, Any, Optional
import io

# 文件处理相关
import docx
import PyPDF2
import pandas as pd
from bs4 import BeautifulSoup
import ebooklib
from ebooklib import epub

# 文本分割相关
from langchain_text_splitters import RecursiveCharacterTextSplitter

logger = logging.getLogger(__name__)

class FileProcessingService:
    """文件处理服务类，用于解析不同格式的文件并提取文本内容"""
    
    SUPPORTED_EXTENSIONS = {
        'txt': 'text/plain',
        'pdf': 'application/pdf',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'md': 'text/markdown',
        'html': 'text/html',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'epub': 'application/epub+zip',
    }
    
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
    
    def get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        return filename.split('.')[-1].lower()
    
    def is_supported_file(self, filename: str) -> bool:
        """检查文件是否为支持的格式"""
        ext = self.get_file_extension(filename)
        return ext in self.SUPPORTED_EXTENSIONS
    
    def extract_text(self, file_path: str, file_type: str) -> str:
        """从文件中提取文本内容
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            
        Returns:
            str: 提取的文本内容
        """
        try:
            if file_type == 'txt':
                return self._extract_from_txt(file_path)
            elif file_type == 'pdf':
                return self._extract_from_pdf(file_path)
            elif file_type == 'docx':
                return self._extract_from_docx(file_path)
            elif file_type == 'md':
                return self._extract_from_txt(file_path)  # Markdown当作文本处理
            elif file_type == 'html':
                return self._extract_from_html(file_path)
            elif file_type == 'xlsx':
                return self._extract_from_xlsx(file_path)
            elif file_type == 'epub':
                return self._extract_from_epub(file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
        except Exception as e:
            logger.error(f"提取文件内容时出错: {str(e)}", exc_info=True)
            raise
    
    def _extract_from_txt(self, file_path: str) -> str:
        """从文本文件中提取内容"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            return file.read()
    
    def _extract_from_pdf(self, file_path: str) -> str:
        """从PDF文件中提取内容"""
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
        return text
    
    def _extract_from_docx(self, file_path: str) -> str:
        """从DOCX文件中提取内容"""
        doc = docx.Document(file_path)
        text = ""
        for para in doc.paragraphs:
            text += para.text + "\n"
        return text
    
    def _extract_from_html(self, file_path: str) -> str:
        """从HTML文件中提取内容"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            soup = BeautifulSoup(file.read(), 'html.parser')
            # 移除样式、脚本和其他不需要的标签
            for script in soup(["script", "style", "meta", "head"]):
                script.extract()
            return soup.get_text(separator="\n")
    
    def _extract_from_xlsx(self, file_path: str) -> str:
        """从Excel文件中提取内容"""
        df_list = pd.read_excel(file_path, sheet_name=None)
        text = ""
        for sheet_name, df in df_list.items():
            text += f"表格: {sheet_name}\n"
            text += df.to_string(index=False) + "\n\n"
        return text
    
    def _extract_from_epub(self, file_path: str) -> str:
        """从EPUB文件中提取内容"""
        book = epub.read_epub(file_path)
        text = ""
        for item in book.get_items():
            if item.get_type() == ebooklib.ITEM_DOCUMENT:
                soup = BeautifulSoup(item.get_content(), 'html.parser')
                text += soup.get_text(separator="\n") + "\n"
        return text
    
    def split_text(self, text: str) -> List[str]:
        """将文本分割成多个块
        
        Args:
            text: 要分割的文本
            
        Returns:
            List[str]: 分割后的文本块列表
        """
        return self.text_splitter.split_text(text)