import re
import time
import uuid
import json
import httpx
import logging
import asyncio # <--- Import asyncio
from datetime import datetime, timezone as dt_timezone, timedelta
from typing import Any, Dict, Generator, List, Optional, Tuple, AsyncGenerator # <--- Import AsyncGenerator
from asgiref.sync import sync_to_async # <--- 导入 sync_to_async

# Use pytz for reliable timezone handling
import pytz

from openai import AsyncOpenAI, OpenAIError # <--- Import AsyncOpenAI

from django.conf import settings
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
from django.db.models import F, Q, Count

from llm_analysis.vector_store import VectorStoreService
from api.models import Match, Odds, Team
from llm_analysis.models import OddsAnalysis, LLMModel
from knowledge_api.models import KnowledgeFile

logger = logging.getLogger(__name__)

class LLMService:
    """LLM分析服务类"""
    
    def __init__(self, llm_model: Optional[str] = None, base_url: Optional[str] = None, api_key: Optional[str] = None, request: Optional[Any] = None):
        """初始化LLM服务 (异步版本)"""
        self.request = request
        self.model_name = llm_model or settings.LLM_MODEL_NAME
        _base_url = base_url or settings.LLM_BASE_URL
        _api_key = api_key or settings.LLM_API_KEY

        if not _api_key:
            logger.error("LLM_API_KEY 未提供且未在settings中设置。")
            raise ValueError("缺少API密钥，无法初始化LLM服务")
        if not _base_url:
            logger.error("base_url 未提供且未在settings中设置。")
            raise ValueError("缺少API基础URL，无法初始化LLM服务")
            
        # 清理 base_url，确保它指向API的基础路径 (例如 /v1/)
        # 移除可能存在的 /chat/completions 后缀
        if _base_url.endswith('/chat/completions'):
            _cleaned_base_url = _base_url[:-len('/chat/completions')].rstrip('/')
            logger.info(f"原始 base_url '{_base_url}' 清理为 '{_cleaned_base_url}'")
            _base_url = _cleaned_base_url
        elif not _base_url.endswith('/'): # 确保以斜杠结尾，除非已经是根路径
             _base_url += '/'

        # 设置超时时间
        try:
            # 创建异步 httpx 客户端
            http_client = httpx.AsyncClient( # <--- Use AsyncClient
                timeout=360.0,  # 增加超时时间到 360 秒
                follow_redirects=True
            )
            
            # 初始化异步 OpenAI 客户端
            self.client = AsyncOpenAI( # <--- Use AsyncOpenAI
                api_key=_api_key, # 使用获取到的 api_key
                base_url=_base_url, # 使用清理后的 base_url
                max_retries=3,  # 设置重试次数
                http_client=http_client,  # <--- Pass AsyncClient
                default_headers={
                    "Content-Type": "application/json"
                }
            )
            
            # 初始化向量存储 (如果启用，增加对setting缺失的兼容)
            _vector_store_enabled = getattr(settings, 'VECTOR_STORE_ENABLED', False) # 默认为 False
            if not hasattr(settings, 'VECTOR_STORE_ENABLED'):
                 logger.warning("Django settings 中缺少 VECTOR_STORE_ENABLED 配置，默认禁用向量存储。请在 settings.py 中添加此配置 (True/False)。")
            self.vector_store = VectorStoreService() if _vector_store_enabled else None
            self.vector_store_enabled = _vector_store_enabled
            
            logger.info(f"异步 LLM 服务初始化完成，使用模型: {self.model_name}, Base URL: {_base_url}")
        except Exception as e:
            logger.error(f"初始化异步 LLM 客户端时出错: {str(e)}", exc_info=True)
            raise ValueError(f"初始化 LLM 服务失败: {str(e)}")

    @sync_to_async
    def _resolve_match_id_sync(self, match_id_param: str) -> str:
        # 这个函数包含原始的同步数据库调用逻辑
        # ... (原始 _resolve_match_id 的同步代码)
        # 首先检查参数是否直接是数字ID，且在Match表中存在
        try:
            matches = Match.objects.filter(match_id=match_id_param)
            if matches.exists():
                logger.info(f"通过match_id直接找到比赛: {match_id_param}")
                return match_id_param
        except Exception as e:
            logger.warning(f"直接通过match_id查找失败: {str(e)}")
        
        # 如果直接查找失败，查看这个ID是否已经在OddsAnalysis表中
        if OddsAnalysis.objects.filter(match_id=match_id_param).exists():
            logger.info(f"在OddsAnalysis表中直接找到match_id: {match_id_param}")
            return match_id_param
        
        # 所有方法都失败，返回原始参数，并发出警告
        logger.warning(f"未能解析比赛ID '{match_id_param}'，将尝试使用原始值。这可能导致错误。")
        return match_id_param
        
    async def _resolve_match_id(self, match_id_param: str) -> str:
        # 异步包装器，调用同步实现
        return await self._resolve_match_id_sync(match_id_param)

    async def _get_match_object(self, match_id_param: str) -> Optional[Match]:
        """异步获取 Match 对象"""
        resolved_match_id = await self._resolve_match_id(match_id_param)
        if not resolved_match_id:
            logger.error(f"无法解析 match_id: {match_id_param}")
            return None
        try:
            # 使用 select_related 预取关联对象，提高效率
            match_obj = await sync_to_async(Match.objects.filter(match_id=resolved_match_id).select_related('home_team', 'away_team', 'league_ref').first)()
            if not match_obj:
                 logger.error(f"无法找到比赛对象，match_id={resolved_match_id}")
                 return None
            return match_obj
        except Exception as e:
            logger.error(f"获取比赛对象时出错 (ID: {resolved_match_id}): {e}", exc_info=True)
            return None
    
    async def _get_latest_db_odds_prompt(self, match_id: str, match_info: dict, match_time_formatted: Optional[str]) -> str:
        """从数据库获取最新的赔率数据并格式化为提示字符串 (异步)"""
        odds_prompt_internal = f"比赛信息:\n{match_info.get('league', '未知联赛')}：{match_info.get('home_team', '未知主队')}(主队) vs {match_info.get('away_team', '未知客队')}(客队)"
        if match_time_formatted:
             odds_prompt_internal += f"\n比赛时间: {match_time_formatted}"
        odds_prompt_internal += "\n\n赔率数据:\n"

        try:
            # 按机构分组，按时间降序获取所有赔率记录
            odds_data_qs = Odds.objects.filter(match_id=match_id).order_by('bookmaker_id', '-update_time')
            odds_list = await sync_to_async(list)(odds_data_qs)

            if not odds_list:
                logger.warning(f"数据库中未找到 match_id 为 {match_id} 的赔率数据")
                return odds_prompt_internal + "暂无赔率数据\n"

            logger.info(f"从数据库找到 {len(odds_list)} 条赔率记录，将获取每个机构最新的数据")
            bookmakers = {} # 用于存储每个公司的所有赔率，以找到初始赔率
            latest_odds_by_bookmaker = {} # 用于存储每个公司的最新赔率

            # 分组并找到每个 bookmaker 的最新赔率
            for odd in odds_list:
                bk_id_str = str(odd.bookmaker_id) # 确保使用字符串ID
                if bk_id_str not in bookmakers:
                    bookmakers[bk_id_str] = []
                bookmakers[bk_id_str].append(odd)

                # 保留最新的赔率记录
                if bk_id_str not in latest_odds_by_bookmaker or odd.update_time > latest_odds_by_bookmaker[bk_id_str].update_time:
                   latest_odds_by_bookmaker[bk_id_str] = odd


            if not latest_odds_by_bookmaker:
                 logger.warning(f"无法为 match_id {match_id} 找到任何机构的最新赔率数据")
                 return odds_prompt_internal + "暂无最新赔率数据\n"


            # 格式化输出每个机构的初始赔率和最新赔率
            bookmaker_names = {'281': 'Bet365', '82': '立博', '115': '威廉希尔'}
            for bookmaker_id_str, latest_odds in latest_odds_by_bookmaker.items():
                bookmaker_name = bookmaker_names.get(bookmaker_id_str, f"公司{bookmaker_id_str}")
                odds_prompt_internal += f"\n{bookmaker_name} (ID:{bookmaker_id_str}):\n"

                # 查找该公司的初始赔率 (update_time 最早的)
                initial_odds = min(bookmakers.get(bookmaker_id_str, []), key=lambda x: x.update_time, default=None)

                if initial_odds:
                    odds_prompt_internal += f"  - 初始赔率 (更新时间: {initial_odds.update_time})\n"
                    odds_prompt_internal += f"    主胜: {initial_odds.home_win} 平: {initial_odds.draw} 客胜: {initial_odds.away_win}\n"

                # 仅当最新赔率与初始赔率不同时，才显示最新赔率
                if latest_odds and (not initial_odds or initial_odds.update_time != latest_odds.update_time):
                    odds_prompt_internal += f"  - 最新赔率 (更新时间: {latest_odds.update_time})\n"
                    odds_prompt_internal += f"    主胜: {latest_odds.home_win} 平: {latest_odds.draw} 客胜: {latest_odds.away_win}\n"
                elif initial_odds and initial_odds.update_time == latest_odds.update_time:
                     # 如果最新和初始是同一条，只显示初始赔率即可
                     pass

            return odds_prompt_internal

        except Exception as e:
            logger.error(f"获取或格式化最新数据库赔率时出错 (match_id={match_id}): {e}", exc_info=True)
            return odds_prompt_internal + "获取最新赔率数据时出错\n"

    async def async_prepare_match_analysis(self,
                                         match: Match, # 修改：接收 Match 对象
                                         is_post_match: bool,
                                         full_score: Optional[str],
                                         latest_db_odds_prompt: str) -> Tuple[List[Dict], str]: # 返回值简化
        """准备比赛分析所需的数据 (异步版本) - 重构以适应新逻辑

        Args:
            match: Match 对象
            is_post_match: 比赛是否已结束
            full_score: 完场比分 (如果 is_post_match 为 True)
            latest_db_odds_prompt: 从数据库获取的最新赔率提示

        Returns:
            Tuple:
                - 从知识库检索到的相关信息 (List[Dict])
                - 用于LLM分析的完整提示词 (str)
        """
        resolved_match_id = str(match.match_id)
        logger.info(f"开始异步准备 {'赛后复盘' if is_post_match else '赛前分析'} 数据, match_id={resolved_match_id}")

        # 准备基本信息用于知识库查询
        home_team_name = getattr(match.home_team, 'team_name_simp', '未知主队')
        away_team_name = getattr(match.away_team, 'team_name_simp', '未知客队')
        league_name = getattr(match.league, 'league_short_name', '未知联赛')

        # 异步准备LLM查询文本
        query_text = f"{league_name}: {home_team_name} vs {away_team_name} 赔率分析"

        # 异步搜索相似知识
        used_knowledge = []
        knowledge_prompt_text = "" # 用于构建最终 prompt 的知识部分
        logger.info(f"Checking vector store status before search. Enabled: {self.vector_store_enabled}, Instance: {self.vector_store is not None}") # <-- 添加：调用前检查状态
        similar_knowledge = [] # 初始化为空列表
        if self.vector_store_enabled:
            logger.info(f"向量存储已启用，异步搜索相关知识，查询文本: '{query_text}'")
            # 增加 limit 获取更多知识
            try:
                similar_knowledge = await self.search_similar_knowledge(query_text, request=self.request, limit=5)
                logger.info(f"Returned from search_similar_knowledge. Found {len(similar_knowledge)} items.") # <-- 添加：调用后日志

                if similar_knowledge:
                    knowledge_prompt_text = "\n\n【参考知识】\n"
                    logger.info(f"找到 {len(similar_knowledge)} 条相关知识:")
                    knowledge_count = 0
                    for item in similar_knowledge:
                        # 添加简单的相似度过滤
                        similarity = item.get('similarity', 1.0) # 默认相似度为1
                        if similarity < 0.3: # 过滤阈值，可调整
                            logger.info(f"  - 跳过低相似度知识: {item.get('title', '未知标题')} (相似度: {similarity:.2f})")
                            continue
                        knowledge_count += 1
                        logger.info(f"  - 使用知识: {item.get('title', '未知标题')} (相似度: {similarity:.2f})")
                        knowledge_prompt_text += f"《{item.get('title', '未知标题')}》内容：\n{item.get('content', '')}\n\n"
                        used_knowledge.append({ # 记录使用的知识
                            "source": item.get('title', '未知标题'), # 使用 title 作为 source
                            "content": item.get('content', '未知内容'),
                            "similarity": similarity
                        })
                    if knowledge_count == 0:
                        logger.info("所有找到的知识因相似度过低而被跳过")
                        knowledge_prompt_text = "\n\n【参考知识】\n无相关知识库内容。\n"

                else:
                    logger.info("未找到相关知识")
                    knowledge_prompt_text = "\n\n【参考知识】\n无相关知识库内容。\n"
            
            except Exception as search_err:
                 logger.error(f"Call to search_similar_knowledge failed: {search_err}", exc_info=True)
                 # similar_knowledge 保持为空列表
                 knowledge_prompt_text = "\n\n【参考知识】\n知识库搜索时发生错误。\n"

        else:
             logger.info("向量存储未启用, 跳过呼叫search_similar_knowledge.") # <-- 添加：跳过调用日志
             knowledge_prompt_text = "\n\n【参考知识】\n知识库未启用。\n"


        # 构建引用指令
        reference_instruction = ""
        knowledge_titles = [k['source'] for k in used_knowledge if k.get('source')]
        if knowledge_titles:
            references_str = ", ".join([f"《{title}》" for title in knowledge_titles])
            reference_instruction = f"""
            严格要求：你必须在分析中明确引用上述《参考知识》部分的专业理论和文档。这些资源包括：{references_str}。
            你必须：
            1. 在分析的每个部分开始时，先明确引用相关的知识文档，如"根据《欧赔核心思维》..."或"《欧洲赔率》理论指出..."
            2. 确保你的分析内容与引用的文档内容紧密相关，使用文档中的概念、术语和方法。
            3. 所有的专业判断、解读和最终结论都必须有明确的、引用的知识点作为支撑，不得凭空发挥。
            4. 在整个分析过程中，至少引用3次或以上不同的知识点或理论（如果知识库提供了足够多的内容）。
            注意：如果你未能严格遵守这些引用要求，你的分析将被视为缺乏专业依据且不合格。
            """
        else:
            reference_instruction = "由于未找到相关知识库内容，请基于你对欧赔和足球博彩的通用理解进行分析，并说明是基于通用经验。"

        # 最终构建完整的分析提示词 (根据 is_post_match)
        analysis_prompt = ""
        if is_post_match:
            # === 赛后复盘提示词 ===
            score_display = full_score if full_score else "暂未获取"
            # 注意赔率提示使用 latest_db_odds_prompt
            analysis_prompt = f"""你是一名顶级的足球赛事复盘专家，尤其擅长结合赛前赔率变化和赛后结果进行深度复盘。

            比赛信息:
            {league_name}：{home_team_name}(主队) vs {away_team_name}(客队)
            最终比分: {score_display}

            注意：下面是这场比赛赛前的赔率数据和相关的理论知识。请仔细分析这些信息，进行赛后复盘。

            【赛前赔率数据】
            {latest_db_odds_prompt}

            {knowledge_prompt_text}

            【复盘要求和流程】
            1. 【赔率回顾】：回顾赛前关键赔率节点（初赔、即赔的重要变动、终赔）与赛果 {score_display} 是否背离，如果是，则反思核心矛盾点。
            2. 【理论验证】：{reference_instruction.replace('分析中', '复盘中').replace('判断', '解释')} 运用《参考知识》中的理论，解释赛前的赔率变化如何解读才能符合最终赛果。
            3. 【机构意图复盘】：结合赛果 {score_display}，重新评估机构在赛前的操盘意图（例如是诱导、阻碍还是真实意图的体现）以及操盘是否成功，拆解机构策略。
            4. 【总结与修正】：从赛前分析、完整赔率和赛果中，修正理论与实战的偏差，得出核心误判和进阶策略。
            5. 复盘需逻辑清晰，论证充分，严格引用参考知识。

            【格式要求】
            1. 请使用中文回答，以专业、清晰的语言输出分析结果。
            2. 直接输出分析内容，不要使用markdown格式。
            3. 不要输出过程性提示。"""
            logger.info("已构建赛后复盘提示词")
        else:
            # === 赛前分析提示词（包含预测要求） ===
            # 注意赔率提示使用 latest_db_odds_prompt
            analysis_prompt = f"""你是一名有40年行业经验的专业足球赛事分析师以及有30年操盘经验的博彩机构资深操盘手，擅长解读各大博彩公司的赔率变化和盘口走势，需要根据知识库中的【参考知识】对用户提供的足球赛事的欧赔进行深度剖析，从中判断出机构对比赛走势的真实看法或倾向。你现在的任务需要严格按照以下要求进行:

            注意：下面提供的赔率数据和参考知识非常重要，请仔细分析赔率并严格按照参考知识中的理论和方法进行解读。如果你看到"暂无赔率数据"字样，请说明"由于您提供的比赛没有赔率数据,我无法对赔率进行专业分析"。

            【赔率数据】
            {latest_db_odds_prompt}

            {knowledge_prompt_text}

            【解读要求】
            1. 确认是否有赔率数据可供分析。如无数据，请直接说明无法分析。
            2. 如有数据，根据参考知识中的理论框架和方法，必须按照以下步骤分析：
                【解读初赔】：分析机构设置初始赔率的意图和市场偏好；
                【解读实时赔率变化】：分析实时赔率（即初赔后及终赔前的赔率调整阶段）的变化趋势及其背后的含义；
                【剖析机构意图】：比较不同博彩公司的赔率差异及判断机构真实意图；
                【关键点总结】：得出本场赛事的最优解选项（例如本场最优解为主胜或平或客胜），必须结合【参考知识】，有理有据，不得凭空捏造。
            3. {reference_instruction}
            4. 解读完成后，如需对结论进行验证则搜集基本面、消息面、资金流等资讯进行验证。
            5. 预测与建议: 基于以上所有分析，你必须给出明确的比赛结果预测（例如：预测主队胜、预测平局、预测客队胜），并基于此预测提供清晰的投注建议（例如：建议投注主胜）。如果当前赔率数据和分析确实无法支持明确的预测和建议，请明确说明这一点，并建议用户继续关注赔率变化，在数据更新后点击"继续生成"按钮获取进一步分析。
            6. 解读应保持中立客观，评估各种可能性，不要仅关注表面赔率，而应深入剖析欧赔背后的机构意图。
            7. 回答完整并且富有洞察力，避免不必要的重复。
            8. 整个解读流程结束后提醒用户，当即时赔率发生变化时应再次分析。

             【格式要求】
            1. 请使用中文回答，以专业、清晰的语言输出分析结果。
            2. 直接输出分析内容，不要使用markdown格式，例如，不要使用标题符号如#或##。
            3. 不要输出'正在处理'、'正在分析'或'正在解读'等过程性提示。
            """
            logger.info("已构建赛前分析（含预测/建议）提示词")

        # 记录完整的最终提示词
        logger.debug("--- BEGIN FULL ANALYSIS PROMPT ---")
        logger.debug(analysis_prompt)
        logger.debug("--- END FULL ANALYSIS PROMPT ---")
        logger.info(f"异步准备完成，最终生成的分析提示词 (长度): {len(analysis_prompt)}")
        # 返回值调整
        return used_knowledge, analysis_prompt

    async def _chat_completion(self, system_prompt: str, user_prompt: str) -> str:
        """执行LLM聊天补全请求
        
        Args:
            system_prompt: 系统提示
            user_prompt: 用户提示
            
        Returns:
            str: LLM生成的回复文本
        """
        try:
            logger.info(f"调用LLM API，使用模型: {self.model_name}")
            
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.5,
                max_tokens=4000
            )
            
            if not response or not hasattr(response, 'choices') or not response.choices:
                raise ValueError("API返回无效响应")
                
            result = response.choices[0].message.content
            logger.info(f"LLM API调用成功，生成内容长度: {len(result)}")
            
            return result
            
        except Exception as e:
            # 记录更详细的错误信息
            logger.error(f"LLM API 调用失败 ({type(e).__name__}): {str(e)}", exc_info=True)
            # 尝试获取更具体的错误详情 (如果可用)
            error_details = getattr(e, 'response', None)
            if error_details and hasattr(error_details, 'text'):
                logger.error(f"LLM API 响应内容: {error_details.text}")
            elif hasattr(e, 'body'): # 有些 httpx 错误可能把信息放在 body 里
                logger.error(f"LLM API 错误 Body: {getattr(e, 'body', 'N/A')}")

            raise ValueError(f"调用LLM服务失败 ({type(e).__name__}): {str(e)}") # 在异常信息中包含类型

    # 将同步的准备函数包装为异步函数
    # 注意：这仍然会在后台线程执行同步代码，但不会阻塞主事件循环
    # async_prepare_match_analysis = sync_to_async(_prepare_match_analysis, thread_sensitive=True) # <-- 这行是多余的，因为 async_prepare_match_analysis 现在是原生 async def
    # 将同步的保存/查询函数包装为异步函数
    async_check_recent_report_exists = sync_to_async(lambda match_id, threshold: OddsAnalysis.objects.filter(match_id=match_id, created_at__gte=threshold).exists(), thread_sensitive=True)
    async_create_analysis = sync_to_async(OddsAnalysis.objects.create, thread_sensitive=True)
    async_get_base_report = sync_to_async(OddsAnalysis.objects.get, thread_sensitive=True)
    # async_resolve_match_id = sync_to_async(_resolve_match_id, thread_sensitive=True) # <-- 这行也是多余的，因为 _resolve_match_id 已经是 async def

    async def analyze_match_odds_stream(self, match_id: str, extra_data: dict = None): # 修改为 async def
        """流式分析比赛赔率 (异步)"""
        start_time = time.time()
        full_analysis_text = """"""
        used_knowledge = []
        final_generation_type = 'initial' # 默认值

        try:
            logger.info(f"开始流式分析, match_id={match_id}")
            yield {"event": "status", "data": json.dumps({"message": "正在获取比赛信息..."})}

            # 1. 获取 Match 对象
            match_obj = await self._get_match_object(match_id)
            if not match_obj:
                raise ValueError(f"无法获取比赛对象，ID: {match_id}")
            resolved_match_id = str(match_obj.match_id)

            # 2. 检查比赛是否结束 (基于时间)
            is_post_match = False
            full_score = None
            if match_obj.match_time:
                 # USE_TZ is False, so timezone.now() is naive. Ensure match_time is also naive for comparison.
                 naive_match_time = match_obj.match_time
                 if timezone.is_aware(naive_match_time): # Safety check
                     logger.warning(f"Match time for {resolved_match_id} was aware despite USE_TZ=False. Making it naive.")
                     naive_match_time = timezone.make_naive(naive_match_time)

                 # Compare naive datetimes
                 if timezone.now() > naive_match_time + timedelta(minutes=120):
                     is_post_match = True
                     full_score = match_obj.full_score if match_obj.full_score and match_obj.full_score != '-' else None
                     logger.info(f"比赛 {resolved_match_id} 已结束 (基于时间)。准备赛后复盘。比分: {full_score or '未获取'}")
                     final_generation_type = 'post_match_replay'
                 else:
                     logger.info(f"比赛 {resolved_match_id} 未结束 (基于时间)。准备赛前分析。")
                     # 对于未结束的比赛，使用前端请求的类型
                     final_generation_type = extra_data.get('generation_type', 'initial') if extra_data else 'initial'
            else:
                logger.warning(f"比赛 {resolved_match_id} 没有有效的 match_time，无法判断是否结束，按未结束处理。")
                # 没有时间，按未结束处理，使用请求的类型
                final_generation_type = extra_data.get('generation_type', 'initial') if extra_data else 'initial'

            yield {"event": "status", "data": json.dumps({"message": f"正在准备{'赛后复盘' if is_post_match else '赛前分析'}数据..."})}

            # 3. 获取最新的数据库赔率 (无论赛前赛后都需要赛前赔率作为参考)
            match_info_dict = {
                "league": getattr(match_obj.league, 'league_short_name', '未知联赛'),
                "home_team": getattr(match_obj.home_team, 'team_name_simp', '未知主队'),
                "away_team": getattr(match_obj.away_team, 'team_name_simp', '未知客队')
            }
            match_time_formatted = None
            if match_obj.match_time:
                try:
                    aware_dt = timezone.make_aware(match_obj.match_time, timezone.get_default_timezone()) if timezone.is_naive(match_obj.match_time) else match_obj.match_time
                    match_time_formatted = aware_dt.strftime("%Y-%m-%d %H:%M %Z")
                except Exception: pass # Ignore formatting errors
            latest_db_odds_prompt = await self._get_latest_db_odds_prompt(resolved_match_id, match_info_dict, match_time_formatted)

            # 4. 准备分析数据和 Prompt
            # 调用重构后的 async_prepare_match_analysis
            used_knowledge, analysis_prompt = await self.async_prepare_match_analysis(
                match=match_obj,
                is_post_match=is_post_match,
                full_score=full_score,
                latest_db_odds_prompt=latest_db_odds_prompt
            )

            # 5. 处理 'continue' 模式的特殊 Prompt (如果不是强制赛后复盘)
            requested_generation_type = extra_data.get('generation_type', 'initial') if extra_data else 'initial'
            if not is_post_match and requested_generation_type == 'continue' and extra_data and 'previous_report' in extra_data:
                 logger.info("处理 'continue' 模式 (赛前)")
                 previous_report = extra_data['previous_report']
                 previous_analysis = previous_report.get('analysis_text', '')
                 previous_prompt_text = previous_report.get('prompt_text', '') # 获取旧 prompt 用于对比
                 
                 # 获取之前使用的知识库标题
                 previous_knowledge = previous_report.get('used_knowledge', [])
                 knowledge_titles = [k.get('source') for k in previous_knowledge if k.get('source')]
                 knowledge_reference = ""
                 if knowledge_titles:
                     references_str = ", ".join([f"《{title}》" for title in knowledge_titles])
                     knowledge_reference = f"请在分析中明确引用这些专业理论文档：{references_str}。每个关键分析步骤都应以'根据《文档名》...'或'《文档名》理论指出...'的方式开始。"
                 else:
                     knowledge_reference = "如果在您之前的分析中引用了任何知识文档，请在本次分析中继续使用相同的引用方式。"

                 # 尝试从旧 prompt 提取赔率部分，如果失败则忽略对比
                 previous_odds_section = ""
                 try:
                     odds_match = re.search(r"【赔率数据】(.*?)(\n【参考知识】|\Z)", previous_prompt_text, re.DOTALL)
                     if odds_match:
                         previous_odds_section = odds_match.group(1).strip()
                 except Exception as re_err:
                     logger.warning(f"Continue: 从旧 prompt 提取赔率部分时出错: {re_err}")

                 current_odds_section = latest_db_odds_prompt # 使用最新的DB赔率

                 # 简单对比赔率部分是否变化 (可能不完全精确但足够)
                 # 移除 '比赛信息' 和 '比赛时间' 部分再比较赔率数据本身
                 current_odds_data_only = re.sub(r"比赛信息:.*?\n(比赛时间:.*?\n)?\n赔率数据:\n", "", current_odds_section, flags=re.DOTALL).strip()
                 previous_odds_data_only = re.sub(r"比赛信息:.*?\n(比赛时间:.*?\n)?\n赔率数据:\n", "", previous_odds_section, flags=re.DOTALL).strip()

                 if previous_odds_section and previous_odds_data_only == current_odds_data_only:
                      analysis_prompt = f"""本次查询的赔率数据与您上一次 ({previous_report.get('created_at', '未知时间')}) 分析时的数据相同。\n\n为了进行有效的【继续分析】，请等待赔率更新后再试。"""
                      logger.warning("Continue 模式：赔率未变化，生成提示信息")
                      # 将 LLM 调用替换为直接发送提示信息
                      full_analysis_text = analysis_prompt
                      llm_call_successful = False # 标记为不需要保存
                 else:
                     time_warning = "" # 添加时间警告逻辑
                     if match_obj.match_time:
                        try:
                            # Ensure both datetimes are naive for comparison when USE_TZ=False
                            now_naive = timezone.now() # This is naive when USE_TZ=False
                            match_time_naive = match_obj.match_time
                            if timezone.is_aware(match_time_naive):
                                match_time_naive = timezone.make_naive(match_time_naive)
                            
                            time_diff = match_time_naive - now_naive # Calculate difference using naive datetimes
                            
                            time_diff_hours = time_diff.total_seconds() / 3600
                            time_diff_minutes = time_diff.total_seconds() / 60
                            if time_diff_hours <= 3 and time_diff_hours > 0: time_warning = f"请特别注意：当前距离比赛开始仅剩约 {time_diff_hours:.1f} 小时 ({int(time_diff_minutes)} 分钟)。"
                            elif time_diff_hours <= 0: time_warning = f"警告：比赛可能已经开始 (已过去 {-int(time_diff_minutes)} 分钟)。"
                        except Exception as time_err: logger.error(f"Continue(非流式)计算时间差出错: {time_err}")

                     analysis_prompt = f"""你曾经对以下比赛做过赔率分析：

                                    {match_info_dict['league']}：{match_info_dict['home_team']} vs {match_info_dict['away_team']}

                                    你的上一次分析结果是 (分析时间: {previous_report.get('created_at', '未知时间')})：
                                    {previous_analysis}

                                    现在，该比赛的【最新赔率数据】(从数据库获取)如下：
                                    {current_odds_section}

                                    {time_warning}

                                    请根据【最新的赔率变化】，继续分析以下几点：
                                    1. 赔率变化情况：对比上次分析，指出主要的赔率变化。
                                    2. 机构意图变化：最新赔率变化透露了什么新的信息或机构意图？{time_warning}
                                    3. 前后对比分析：新的赔率走势是否改变了你之前的判断？为什么？
                                    4. 更新后的预期：基于最新数据，你对比赛结果的最新预期是什么？
                                    5. 预测与建议: 基于以上所有分析和变化，你必须给出明确的比赛结果预测（胜/平/负）和投注建议。如果无法明确预测，请说明原因。
                                    
                                    【重要】：在分析过程中，请明确引用你之前分析中使用过的理论知识，使用"根据《欧赔核心思维》..."或"《欧洲赔率》理论指出..."等方式进行引用，{knowledge_reference}确保你的所有判断和结论都有理论依据。

                                    注意：这是对你之前分析的延续和更新，请聚焦于变化的部分。使用之前分析中可能存在的【参考知识】。"""
                     logger.info("已构建 'continue' 模式 (赛前) 的更新提示词")
                     llm_call_successful = True # 标记需要调用LLM
            else:
                 # 非 continue 模式 或 强制赛后模式
                 llm_call_successful = True # 标记需要调用LLM

            # --- LLM 调用和流式处理 ---
            if llm_call_successful:
                logger.info("准备调用LLM API")
                yield {"event": "status", "data": json.dumps({"message": "正在调用AI模型分析..."})}

                system_prompt = "" # System prompt 现在嵌入到 analysis_prompt 中
                user_prompt = analysis_prompt

                max_retries = 1
                retries = 0
                last_exception = None
                llm_call_successful = False # 重置，由下面成功执行决定

                while retries < max_retries:
                    try:
                        logger.info(f"尝试调用LLM API (第 {retries + 1} 次)")
                        stream_response_generator = self._async_chat_completion_stream(
                            system_prompt=system_prompt,
                            user_prompt=user_prompt,
                            stream=True
                        )
                        logger.info("LLM API 调用成功，开始处理流式响应")

                        content_received = False
                        async for chunk in stream_response_generator:
                            content = chunk.get('content', None)
                            finish_reason = chunk.get('finish_reason', None)

                            if content:
                                content_received = True
                                full_analysis_text += content
                                chunk_data = {"analysis": content}
                                yield {"event": "chunk", "data": json.dumps(chunk_data)}
                                await asyncio.sleep(0.01)

                            if finish_reason == 'error':
                                 logger.error(f"LLM 流处理内部错误: {content}")
                                 raise OpenAIError(f"LLM 流处理错误: {content}")
                            elif finish_reason:
                                 logger.info(f"LLM 流结束原因: {finish_reason}")
                                 break

                        if not content_received and finish_reason != 'error':
                            logger.warning("LLM API流式响应中未收到任何有效数据块。")

                        logger.info(f"LLM流处理完成，总分析长度: {len(full_analysis_text)}")
                        llm_call_successful = True # 标记LLM调用成功
                        break # 成功，跳出重试循环

                    except (httpx.TimeoutException, httpx.HTTPStatusError, OpenAIError, Exception) as e:
                        retries += 1
                        last_exception = e
                        log_level = logging.WARNING if retries < max_retries else logging.ERROR
                        logger.log(log_level, f"调用LLM API出错 (第 {retries}/{max_retries} 次): {type(e).__name__} - {str(e)}", exc_info=(log_level == logging.ERROR))

                        if retries >= max_retries:
                            logger.error(f"达到最大重试次数，LLM API调用最终失败: {str(e)}")
                            error_message = f"AI模型调用失败 ({type(e).__name__})"
                            if isinstance(e, httpx.HTTPStatusError) and e.response.status_code == 429: error_message = "AI模型请求过于频繁"
                            elif isinstance(e, httpx.TimeoutException): error_message = "AI模型响应超时"
                            yield {"event": "error", "data": json.dumps({"error": error_message, "step": "llm_call"})}
                            return
                        await asyncio.sleep(1)
            # else: # 如果 llm_call_successful 为 False (continue 模式且赔率未变)
            #     # 直接发送之前生成的提示信息
            #     yield {"event": "chunk", "data": json.dumps({"analysis": full_analysis_text})} # full_analysis_text 已被设为提示语


            # --- 保存分析结果 ---
            # 仅在LLM调用成功且生成了内容时保存
            if llm_call_successful and full_analysis_text.strip():
                logger.info("准备保存分析结果到数据库")

                analysis_to_save = OddsAnalysis(
                    match_id=resolved_match_id,
                    analysis_text=full_analysis_text,
                    prompt_text=analysis_prompt, # 保存最终使用的 prompt
                    llm_model=self.model_name,
                    used_knowledge=used_knowledge,
                    created_at=timezone.now(),
                    generation_type=final_generation_type # 使用前面确定的类型
                )

                # 处理 base_report 关联 (仅当不是赛后复盘且不是 initial 时)
                if final_generation_type not in ['initial', 'post_match_replay'] and extra_data and 'base_report_id' in extra_data:
                    base_report_id = extra_data.get('base_report_id')
                    if base_report_id:
                        try:
                            get_report = sync_to_async(OddsAnalysis.objects.get, thread_sensitive=True)
                            base_report_obj = await get_report(id=base_report_id)
                            analysis_to_save.base_report = base_report_obj
                            logger.info(f"[Stream] 设置 base_report (ID: {base_report_id}) 用于版本计算 (类型: {final_generation_type})")
                        except OddsAnalysis.DoesNotExist:
                            logger.error(f"[Stream] 无法找到 ID 为 {base_report_id} 的基础报告。")
                        except Exception as e:
                            logger.error(f"[Stream] 获取 base_report 时出错: {e}", exc_info=True)

                try:
                    await sync_to_async(analysis_to_save.save)()
                    logger.info(f"分析结果已成功保存到数据库, ID: {analysis_to_save.id}, 类型: {final_generation_type}")
                except Exception as db_err:
                     logger.error(f"保存分析结果到数据库失败: {db_err}", exc_info=True)
                     yield {"event": "warning", "data": json.dumps({"message": "分析完成，但保存到历史记录失败"})}
            elif not llm_call_successful and full_analysis_text.strip(): # 处理continue赔率未变的情况
                 logger.info("赔率未变化，仅返回提示信息，不保存。")
            elif llm_call_successful and not full_analysis_text.strip():
                 logger.warning("LLM未生成有效内容，跳过保存。")


            # --- 发送完成事件 ---
            logger.info("发送分析完成事件")
            yield {"event": "done", "data": json.dumps({
                "analysis": full_analysis_text,
                "done": True,
                "match_id": resolved_match_id,
                "used_knowledge": used_knowledge,
                "generation_type": final_generation_type # 告知前端最终类型
            })}

        except ValueError as e:
            logger.error(f"流式分析准备阶段出错: {str(e)}", exc_info=True)
            yield {"event": "error", "data": json.dumps({"error": f"准备分析数据时出错: {str(e)}", "step": "prepare_data"})}
        except Exception as e:
            logger.exception(f"流式分析过程中发生意外错误")
            yield {"event": "error", "data": json.dumps({"error": f"分析过程中发生未知错误: {type(e).__name__}", "step": "unknown"})}
        finally:
            end_time = time.time()
            logger.info(f"流式分析结束，总耗时: {end_time - start_time:.2f} 秒")

    async def _async_chat_completion_stream(self, system_prompt: str, user_prompt: str, stream: bool = True):
        """异步调用聊天模型API (流式)"""
        try:
            logger.info(f"开始异步调用聊天模型API，使用模型: {self.model_name}")
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.5,
                max_tokens=4000,
                stream=stream
            )
            logger.info("聊天模型API调用成功，开始处理流式响应")
            
            async for chunk in response:
                content = None
                finish_reason = None
                try:
                    # 增加健壮性检查，防止 IndexError
                    if chunk.choices and len(chunk.choices) > 0:
                        choice = chunk.choices[0]
                        if choice.delta:
                            content = choice.delta.content
                        finish_reason = choice.finish_reason
                    else:
                        logger.warning(f"收到的 chunk 不包含 choices: {chunk}")
                         
                    # 仅当有实际内容或结束标志时才 yield
                    if content is not None or finish_reason is not None:
                        yield {
                            "content": content, # 如果只有 finish_reason，这里是 None
                            "finish_reason": finish_reason
                        }
                except Exception as chunk_proc_err:
                    logger.error(f"处理 LLM API 响应块时出错: {chunk_proc_err}", exc_info=True)
                    # 发生错误时尝试 yield 一个错误信息，并停止
                    yield {"content": f"处理响应块时出错: {type(chunk_proc_err).__name__}", "finish_reason": "error"}
                    break # 停止处理后续块
        except Exception as e:
            logger.error(f"异步调用聊天模型API时发生错误: {str(e)}", exc_info=True)
            yield {
                "content": f"AI模型调用时发生未知错误: {type(e).__name__}",
                "finish_reason": "error"
            }

    async def analyze_match_odds(self, match_id: str, extra_data: Any = None) -> Dict[str, Any]: # 保持同步
        """分析比赛的赔率 (非流式，同步)

        Args:
            match_id: 比赛ID
            extra_data: 可选的额外数据（前端传递过来的）

        Returns:
            Dict[str, Any]: 分析结果
        """
        start_time = time.time()
        final_generation_type = 'initial' # 默认值
        full_analysis_text = "" # 初始化

        try:
            logger.info(f"开始非流式分析, match_id={match_id}")

            # 1. 获取 Match 对象 (需要同步获取)
            # 在同步函数中运行异步函数
            logger.info("Attempting to get match object...")
            match_obj = await self._get_match_object(match_id)
            if not match_obj:
                raise ValueError(f"无法获取比赛对象，ID: {match_id}")
            logger.info("Successfully got match object.")
            resolved_match_id = str(match_obj.match_id)

            # 2. 检查比赛是否结束 (基于时间)
            is_post_match = False
            full_score = None
            if match_obj.match_time:
                 # USE_TZ is False, so timezone.now() is naive. Ensure match_time is also naive for comparison.
                 naive_match_time = match_obj.match_time
                 if timezone.is_aware(naive_match_time): # Safety check
                     logger.warning(f"Match time for {resolved_match_id} was aware despite USE_TZ=False. Making it naive.")
                     naive_match_time = timezone.make_naive(naive_match_time)

                 # Compare naive datetimes
                 if timezone.now() > naive_match_time + timedelta(minutes=120):
                     is_post_match = True
                     full_score = match_obj.full_score if match_obj.full_score and match_obj.full_score != '-' else None
                     logger.info(f"比赛 {resolved_match_id} 已结束 (基于时间)。准备赛后复盘。比分: {full_score or '未获取'}")
                     final_generation_type = 'post_match_replay'
                 else:
                     logger.info(f"比赛 {resolved_match_id} 未结束 (基于时间)。准备赛前分析。")
                     final_generation_type = extra_data.get('generation_type', 'initial') if extra_data else 'initial'
            else:
                logger.warning(f"比赛 {resolved_match_id} 没有有效的 match_time，无法判断是否结束，按未结束处理。")
                final_generation_type = extra_data.get('generation_type', 'initial') if extra_data else 'initial'

            # 3. 获取最新的数据库赔率 (需要同步获取)
            logger.info("Attempting to get latest DB odds prompt...")
            match_info_dict = {
                "league": getattr(match_obj.league, 'league_short_name', '未知联赛'),
                "home_team": getattr(match_obj.home_team, 'team_name_simp', '未知主队'),
                "away_team": getattr(match_obj.away_team, 'team_name_simp', '未知客队')
            }
            match_time_formatted = None
            if match_obj.match_time:
                try:
                    aware_dt = timezone.make_aware(match_obj.match_time, timezone.get_default_timezone()) if timezone.is_naive(match_obj.match_time) else match_obj.match_time
                    match_time_formatted = aware_dt.strftime("%Y-%m-%d %H:%M %Z")
                except Exception: pass
            # 在同步函数中运行异步函数
            latest_db_odds_prompt = await self._get_latest_db_odds_prompt(resolved_match_id, match_info_dict, match_time_formatted)
            logger.info("Successfully got latest DB odds prompt.")
            
            # 4. 准备分析数据和 Prompt (需要同步获取)
            logger.info("Attempting to call async_prepare_match_analysis...")
            # 在同步函数中运行异步函数
            used_knowledge, analysis_prompt = await self.async_prepare_match_analysis(
                match=match_obj,
                is_post_match=is_post_match,
                full_score=full_score,
                latest_db_odds_prompt=latest_db_odds_prompt
            )
            logger.info("Returned from async_prepare_match_analysis.")

            # 5. 处理 'continue' 模式 (如果不是强制赛后复盘) - 同步处理
            requested_generation_type = extra_data.get('generation_type', 'initial') if extra_data else 'initial'
            if not is_post_match and requested_generation_type == 'continue' and extra_data and 'previous_report' in extra_data:
                 logger.info("处理 'continue' 模式 (赛前，非流式)")
                 previous_report = extra_data['previous_report']
                 previous_analysis = previous_report.get('analysis_text', '')
                 previous_prompt_text = previous_report.get('prompt_text', '')
                 
                 # 获取之前使用的知识库标题
                 previous_knowledge = previous_report.get('used_knowledge', [])
                 knowledge_titles = [k.get('source') for k in previous_knowledge if k.get('source')]
                 knowledge_reference = ""
                 if knowledge_titles:
                     references_str = ", ".join([f"《{title}》" for title in knowledge_titles])
                     knowledge_reference = f"请在分析中明确引用这些专业理论文档：{references_str}。每个关键分析步骤都应以'根据《欧赔核心思维》...'或'《DEL135》理论指出...'的方式开始。"
                 else:
                     knowledge_reference = "如果在您之前的分析中引用了任何知识文档，请在本次分析中继续使用相同的引用方式。"

                 previous_odds_section = ""
                 try:
                     odds_match = re.search(r"【赔率数据】(.*?)(\n【参考知识】|\Z)", previous_prompt_text, re.DOTALL)
                     if odds_match:
                         previous_odds_section = odds_match.group(1).strip()
                 except Exception as re_err:
                     logger.warning(f"Continue(非流式): 无法从旧 prompt 提取赔率: {re_err}")

                 current_odds_section = latest_db_odds_prompt
                 current_odds_data_only = re.sub(r"比赛信息:.*?\n(比赛时间:.*?\n)?\n赔率数据:\n", "", current_odds_section, flags=re.DOTALL).strip()
                 previous_odds_data_only = re.sub(r"比赛信息:.*?\n(比赛时间:.*?\n)?\n赔率数据:\n", "", previous_odds_section, flags=re.DOTALL).strip()

                 if previous_odds_section and previous_odds_data_only == current_odds_data_only:
                     analysis_prompt = f"""本次查询的赔率数据与您上一次 ({previous_report.get('created_at', '未知时间')}) 分析时的数据相同。\n\n为了进行有效的【继续分析】，请等待赔率更新后再试。"""
                     logger.warning("Continue(非流式): 赔率未变化")
                     full_analysis_text = analysis_prompt # 将提示语作为结果
                     llm_call_required = False
                 else:
                    time_warning = ""
                    if match_obj.match_time:
                        try:
                            # Ensure both datetimes are naive for comparison when USE_TZ=False
                            now_naive = timezone.now() # This is naive when USE_TZ=False
                            match_time_naive = match_obj.match_time
                            if timezone.is_aware(match_time_naive):
                                match_time_naive = timezone.make_naive(match_time_naive)
                            
                            time_diff = match_time_naive - now_naive # Calculate difference using naive datetimes
                            
                            time_diff_hours = time_diff.total_seconds() / 3600
                            time_diff_minutes = time_diff.total_seconds() / 60
                            if time_diff_hours <= 3 and time_diff_hours > 0: time_warning = f"请特别注意：当前距离比赛开始仅剩约 {time_diff_hours:.1f} 小时 ({int(time_diff_minutes)} 分钟)。"
                            elif time_diff_hours <= 0: time_warning = f"警告：比赛可能已经开始 (已过去 {-int(time_diff_minutes)} 分钟)。"
                        except Exception as time_err: logger.error(f"Continue(非流式)计算时间差出错: {time_err}")

                    analysis_prompt = f"""你曾经对以下比赛做过赔率分析：

                                    {match_info_dict['league']}：{match_info_dict['home_team']} vs {match_info_dict['away_team']}

                                    你的上一次分析结果是 (分析时间: {previous_report.get('created_at', '未知时间')})：
                                    {previous_analysis}

                                    现在，该比赛的【最新赔率数据】(从数据库获取)如下：
                                    {current_odds_section}

                                    {time_warning}

                                    请根据【最新的赔率变化】，继续分析以下几点：
                                    1. 赔率变化情况：对比上次分析，指出主要的赔率变化。
                                    2. 机构意图变化：最新赔率变化透露了什么新的信息或机构意图？{time_warning}
                                    3. 前后对比分析：新的赔率走势是否改变了你之前的判断？为什么？
                                    4. 更新后的预期：基于最新数据，你对比赛结果的最新预期是什么？
                                    5. 预测与建议: 基于以上所有分析和变化，你必须给出明确的比赛结果预测（胜/平/负）和投注建议。如果无法明确预测，请说明原因。
                                    
                                    【重要】：在分析过程中，请明确引用你之前分析中使用过的理论知识，使用"根据《欧赔核心思维》..."或"《欧洲赔率》理论指出..."等方式进行引用，{knowledge_reference}确保你的所有判断和结论都有理论依据。

                                    注意：这是对你之前分析的延续和更新，请聚焦于变化的部分。使用之前分析中可能存在的【参考知识】。"""
                    logger.info("已构建 'continue' 模式 (赛前，非流式) 的更新提示词")
                    llm_call_required = True
            else:
                 # 非 continue 模式 或 强制赛后模式
                 llm_call_required = True


            # 6. 执行LLM请求 (同步, 如果需要)
            logger.info("Checking if LLM call is required...")
            if llm_call_required:
                logger.info(f"开始使用 {self.model_name} 模型进行同步分析...")
                system_prompt = "" # System prompt 嵌入在 analysis_prompt 中
                full_analysis_text = await self._chat_completion(system_prompt, analysis_prompt) # 调用同步方法
            # else: # 如果是 continue 且赔率未变, full_analysis_text 已被设置为提示语
                logger.info("Returned from _chat_completion.")


            # 7. 保存分析结果 (同步, 如果 LLM 调用了且有内容)
            analysis_obj = None
            if llm_call_required and full_analysis_text and full_analysis_text.strip():
                logger.info("准备同步保存分析结果")
                analysis_to_save = OddsAnalysis(
                    match_id=resolved_match_id,
                    analysis_text=full_analysis_text,
                    prompt_text=analysis_prompt,
                    llm_model=self.model_name,
                    used_knowledge=used_knowledge,
                    created_at=timezone.now(),
                    generation_type=final_generation_type
                )

                # 处理 base_report 关联 (同步)
                if final_generation_type not in ['initial', 'post_match_replay'] and extra_data and 'base_report_id' in extra_data:
                    base_report_id = extra_data.get('base_report_id')
                    if base_report_id:
                        try:
                            base_report_obj = await sync_to_async(OddsAnalysis.objects.get, thread_sensitive=True)(id=base_report_id) # 同步获取
                            analysis_to_save.base_report = base_report_obj
                            logger.info(f"[Sync] 设置 base_report (ID: {base_report_id}) (类型: {final_generation_type})")
                        except OddsAnalysis.DoesNotExist:
                            logger.error(f"[Sync] 无法找到 ID 为 {base_report_id} 的基础报告。")
                        except Exception as e:
                            logger.error(f"[Sync] 获取 base_report 时出错: {e}", exc_info=True)

                try:
                    await sync_to_async(analysis_to_save.save)() # 异步保存
                    analysis_obj = analysis_to_save # 保存对象引用
                    logger.info(f"分析结果已同步保存到数据库, ID: {analysis_obj.id}, 类型: {final_generation_type}")
                except Exception as db_err:
                     logger.error(f"同步保存分析结果到数据库失败: {db_err}", exc_info=True)
                     # 在非流式中，可以选择标记保存失败
            elif not llm_call_required:
                 logger.info("Continue(非流式): 赔率未变化，跳过保存。")
            else: # LLM 调用了但没内容
                logger.warning("LLM(同步)未生成有效内容，跳过保存。")


            end_time = time.time()
            logger.info(f"非流式分析完成，总耗时: {end_time - start_time:.2f} 秒")

            # 返回结果
            return {
                'match_id': resolved_match_id,
                'analysis': full_analysis_text, # 返回 LLM 结果或提示语
                'used_knowledge': used_knowledge,
                'created_at': analysis_obj.created_at.strftime('%Y-%m-%d %H:%M:%S') if analysis_obj else timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'generation_type': final_generation_type,
                'saved': analysis_obj is not None # 指示是否保存成功 (或是否尝试保存)
            }
        except Exception as e:
            logger.error(f"非流式分析赔率时出错: {str(e)}", exc_info=True)
            # 对于非流式，可以直接向上抛出异常
            raise Exception(f"分析赔率失败: {str(e)}")

    async def search_similar_knowledge(self, query: str, request: Any = None, limit: int = 3) -> List[Dict]: # 添加 limit 参数
        """搜索与查询相关的知识库内容 (异步版本)"""
        logger.info("进入search_similar_knowledge...")
        if not self.vector_store_enabled or not self.vector_store:
            logger.info("向量存储未启用，跳过知识库搜索")
            return []

        user = None
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            user = request.user

        try:
            file_ids_qs = KnowledgeFile.objects.filter(
                user=user,
                is_vectorized=True,
                vectorization_status=KnowledgeFile.STATUS_COMPLETED
            ).values_list('vector_store_id', flat=True)
            file_ids = await sync_to_async(list)(file_ids_qs)
            logger.info(f"发现knowledge file vector_store_ids: {file_ids}")

            if not file_ids:
                logger.info(f"用户 {user.username if user else '未登录'} 没有可搜索的知识文件")
                return []

            logger.info("正在尝试呼叫vector_store.search_multiple_stores...")
            results = await sync_to_async(self.vector_store.search_multiple_stores, thread_sensitive=True)(query, list(file_ids), limit=limit) # 使用 sync_to_async 调用同步方法

            logger.info(f"Vector store search raw results: {results}")

            knowledge_context = []
            if results:
                for item in results:
                    knowledge_context.append({
                        # 返回 title 而不是 source，与 _prepare_analysis_prompt 保持一致
                        "title": item.get('metadata', {}).get('title', item.get('source', '未知来源')),
                        "content": item.get('text', ''),
                        "similarity": item.get('similarity', 0.0) # 包含相似度
                    })
            else:
                logger.info("向量存储未找到相关结果")

            logger.info(f"从知识库找到 {len(knowledge_context)} 条相关信息")
            return knowledge_context

        except Exception as e:
            logger.error(f"搜索知识库时出错: {str(e)}", exc_info=True)
            return []
