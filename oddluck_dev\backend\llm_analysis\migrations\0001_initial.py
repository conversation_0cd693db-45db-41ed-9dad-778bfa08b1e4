# Generated by Django 5.0.4 on 2025-05-25 00:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LLMProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='提供商名称')),
                ('logo', models.CharField(blank=True, max_length=255, null=True, verbose_name='提供商logo')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'LLM提供商',
                'verbose_name_plural': 'LLM提供商',
                'db_table': 'llm_analysis_llmprovider',
            },
        ),
        migrations.CreateModel(
            name='OddsAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('match_id', models.CharField(max_length=100, verbose_name='比赛ID')),
                ('analysis_text', models.TextField(verbose_name='分析文本')),
                ('prompt_text', models.TextField(verbose_name='提示词')),
                ('llm_model', models.CharField(max_length=100, verbose_name='LLM模型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('thinking', models.TextField(blank=True, null=True, verbose_name='思考过程')),
                ('used_knowledge', models.JSONField(blank=True, default=list, null=True, verbose_name='使用的知识')),
                ('generation_type', models.CharField(blank=True, choices=[('initial', '首次生成'), ('regenerate', '重新生成'), ('continue', '继续生成')], max_length=20, null=True, verbose_name='生成类型')),
            ],
            options={
                'verbose_name': '赔率分析',
                'verbose_name_plural': '赔率分析',
                'db_table': 'llm_analysis_oddsanalysis',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['match_id'], name='llm_analysi_match_i_9476f6_idx'), models.Index(fields=['created_at'], name='llm_analysi_created_916a2d_idx'), models.Index(fields=['generation_type'], name='llm_analysi_generat_33e962_idx')],
            },
        ),
        migrations.CreateModel(
            name='LLMModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=100, verbose_name='模型名称')),
                ('base_url', models.CharField(max_length=255, verbose_name='API端点')),
                ('api_key', models.CharField(max_length=255, verbose_name='API密钥')),
                ('is_active', models.BooleanField(default=False, verbose_name='是否为活跃模型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='models', to='llm_analysis.llmprovider', verbose_name='LLM提供商')),
            ],
            options={
                'verbose_name': 'LLM模型',
                'verbose_name_plural': 'LLM模型',
                'db_table': 'llm_analysis_llmmodel',
                'indexes': [models.Index(fields=['provider_id'], name='llm_analysi_provide_110eec_idx')],
            },
        ),
    ]
