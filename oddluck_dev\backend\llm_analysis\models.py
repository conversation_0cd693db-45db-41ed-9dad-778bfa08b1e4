from django.db import models
from django.db.models import Max

class OddsAnalysis(models.Model):
    """赔率分析结果模型
    
    存储LLM对赔率的分析结果
    """
    GENERATION_TYPE_CHOICES = (
        ('initial', '首次生成'),
        ('regenerate', '重新生成'),
        ('continue', '继续生成'),
    )
    
    # 主键使用Django默认的id字段 (int8)
    match_id = models.CharField(max_length=100, verbose_name="比赛ID")
    analysis_text = models.TextField(verbose_name="分析文本")
    prompt_text = models.TextField(verbose_name="提示词")
    llm_model = models.CharField(max_length=100, verbose_name="LLM模型")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    thinking = models.TextField(blank=True, null=True, verbose_name="思考过程")
    used_knowledge = models.JSONField(default=list, blank=True, null=True, verbose_name="使用的知识")
    generation_type = models.CharField(
        max_length=20,
        choices=GENERATION_TYPE_CHOICES,
        null=True,
        blank=True,
        verbose_name="生成类型"
    )
    
    class Meta:
        db_table = 'llm_analysis_oddsanalysis'
        verbose_name = "赔率分析"
        verbose_name_plural = "赔率分析"
        indexes = [
            models.Index(fields=['match_id']),
            models.Index(fields=['created_at']),
            models.Index(fields=['generation_type'])
        ]
        ordering = ['-created_at']

    def __str__(self):
        type_map = {'initial': '首次', 'regenerate': '重生成', 'continue': '继续'}
        type_str = type_map.get(self.generation_type or '', '')
        return f"{type_str}分析-{self.match_id}-{self.created_at.strftime('%Y%m%d%H%M%S')}"

class LLMProvider(models.Model):
    """LLM提供商模型
    
    存储LLM提供商信息
    """
    # 主键使用Django默认的id字段 (int8)
    name = models.CharField(max_length=100, verbose_name="提供商名称")
    logo = models.CharField(max_length=255, null=True, blank=True, verbose_name="提供商logo")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'llm_analysis_llmprovider'
        verbose_name = "LLM提供商"
        verbose_name_plural = "LLM提供商"
        
    def __str__(self):
        return self.name

class LLMModel(models.Model):
    """LLM模型配置
    
    存储LLM模型的配置信息
    """
    # 主键使用Django默认的id字段 (int8)
    model_name = models.CharField(max_length=100, verbose_name="模型名称")
    base_url = models.CharField(max_length=255, verbose_name="API端点")
    api_key = models.CharField(max_length=255, verbose_name="API密钥")
    provider = models.ForeignKey(
        LLMProvider, 
        on_delete=models.CASCADE, 
        related_name="models", 
        verbose_name="LLM提供商"
    )
    is_active = models.BooleanField(default=False, verbose_name="是否为活跃模型")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'llm_analysis_llmmodel'
        verbose_name = "LLM模型"
        verbose_name_plural = "LLM模型"
        indexes = [
            models.Index(fields=['provider_id'])
        ]
        
    def __str__(self):
        return f"{self.provider.name} - {self.model_name}"
