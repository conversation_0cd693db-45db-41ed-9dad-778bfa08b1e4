"""
Ollama嵌入模型集成
用于通过Ollama API获取嵌入向量
"""
import logging
import requests
import numpy as np
from typing import List, Optional, Any
from langchain_core.embeddings import Embeddings

logger = logging.getLogger(__name__)

class OllamaEmbeddings(Embeddings):
    """
    使用Ollama API获取嵌入向量的类
    """
    
    def __init__(
        self,
        model_name: str = "nomic-embed-text",
        base_url: str = "http://localhost:11434",
        mute_warning: bool = False,
        timeout: int = 60,
    ):
        """
        初始化OllamaEmbeddings
        
        Args:
            model_name: Ollama中的模型名称（例如nomic-embed-text、BAAI/bge-large-zh-v1.5等）
            base_url: Ollama API的基础URL
            mute_warning: 是否静默警告
            timeout: API请求超时时间(秒)
        """
        self.model_name = model_name
        self.base_url = base_url.rstrip("/")
        self.mute_warning = mute_warning
        self.timeout = timeout
        self.client = None
        self.embedding_dimensions = None
        
        # 验证Ollama服务是否可用
        self._validate_ollama_service()
        
        # 测试并获取嵌入维度
        self._get_embedding_dimension()
        
        logger.info(f"已初始化Ollama嵌入模型: {model_name}, 维度: {self.embedding_dimensions}")
    
    def _validate_ollama_service(self) -> None:
        """验证Ollama服务是否可用"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=self.timeout)
            if response.status_code != 200:
                raise ValueError(f"无法连接到Ollama服务，状态码: {response.status_code}")
            
            # 检查模型是否可用
            available_models = [model["name"] for model in response.json()["models"]]
            if self.model_name not in available_models:
                logger.warning(f"模型 '{self.model_name}' 在Ollama中不存在，可用模型: {available_models}")
                
                if not self.mute_warning:
                    logger.warning(
                        f"您需要使用以下命令将模型导入Ollama: \n"
                        f"ollama pull {self.model_name}"
                    )
        except requests.exceptions.RequestException as e:
            raise ValueError(f"无法连接到Ollama服务: {str(e)}")
    
    def _get_embedding_dimension(self) -> None:
        """测试嵌入并获取维度"""
        try:
            test_embedding = self.embed_query("测试文本")
            self.embedding_dimensions = len(test_embedding)
            logger.info(f"嵌入维度: {self.embedding_dimensions}")
        except Exception as e:
            logger.error(f"获取嵌入维度失败: {str(e)}")
            self.embedding_dimensions = None
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        获取多个文本的嵌入向量
        
        Args:
            texts: 文本列表
        
        Returns:
            嵌入向量列表
        """
        return [self.embed_query(text) for text in texts]
    
    def embed_query(self, text: str) -> List[float]:
        """
        获取单个文本的嵌入向量
        
        Args:
            text: 输入文本
        
        Returns:
            嵌入向量
        """
        if not text or not isinstance(text, str):
            raise ValueError("嵌入文本必须是非空字符串")
        
        url = f"{self.base_url}/api/embeddings"
        
        try:
            response = requests.post(
                url,
                json={"model": self.model_name, "prompt": text.strip()},
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                raise ValueError(f"Ollama API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            
            embedding = response.json().get("embedding", [])
            if not embedding:
                raise ValueError("Ollama API返回了空的嵌入向量")
            
            return embedding
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求Ollama API时出错: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取嵌入向量失败: {str(e)}")
            raise 