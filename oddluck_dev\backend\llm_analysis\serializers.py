from rest_framework import serializers
from .models import OddsAnalysis, LLMProvider, LLMModel

class KnowledgeBaseSerializer(serializers.Serializer):
    """知识库序列化器 - 已弃用但为保持兼容性而保留
    
    不再依赖KnowledgeBase模型，改为通用的Serializer
    """
    id = serializers.IntegerField(read_only=True)
    title = serializers.CharField(max_length=200)
    content = serializers.CharField()
    source = serializers.CharField(max_length=200, required=False, allow_null=True, allow_blank=True)
    category = serializers.Char<PERSON><PERSON>(max_length=50, required=False, allow_null=True, allow_blank=True)
    created_at = serializers.DateTimeField(read_only=True)
    updated_at = serializers.DateTimeField(read_only=True)
    file = serializers.FileField(required=False, allow_null=True)
    embedding_processed = serializers.BooleanField(required=False, default=False)
    vector_store_id = serializers.Char<PERSON><PERSON>(required=False, allow_null=True, allow_blank=True)

class OddsAnalysisSerializer(serializers.ModelSerializer):
    """赔率分析序列化器"""
    
    class Meta:
        model = OddsAnalysis
        fields = '__all__'

class LLMProviderSerializer(serializers.ModelSerializer):
    """LLM提供商序列化器"""
    
    class Meta:
        model = LLMProvider
        fields = ['id', 'name', 'logo', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

class LLMModelSerializer(serializers.ModelSerializer):
    """LLM模型序列化器"""
    provider_name = serializers.StringRelatedField(source='provider.name', read_only=True)
    
    class Meta:
        model = LLMModel
        fields = ['id', 'provider', 'provider_name', 'model_name', 'base_url', 'api_key', 'created_at', 'updated_at', 'is_active']
        read_only_fields = ['id', 'created_at', 'updated_at']
        extra_kwargs = {
            'api_key': {'write_only': True, 'required': False}
        }

    def validate(self, data):
        """验证API密钥，在创建时必填，更新时可选"""
        # 获取视图上下文
        view = self.context.get('view')
        # 如果是创建新模型（无instance）并且没有提供api_key则报错
        if not self.instance and 'api_key' not in data:
            raise serializers.ValidationError({"api_key": "创建新模型时，API密钥为必填项"})
        return data
    
    def update(self, instance, validated_data):
        """更新LLM模型时允许api_key字段为空"""
        # 如果没有提供api_key，则保留原来的值
        if 'api_key' not in validated_data:
            validated_data['api_key'] = instance.api_key
        return super().update(instance, validated_data)
