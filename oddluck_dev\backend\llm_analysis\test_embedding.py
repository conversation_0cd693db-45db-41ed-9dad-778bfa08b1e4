"""
测试HuggingFace嵌入模型的加载
"""
import sys
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_embedding_model():
    """测试HuggingFace嵌入模型的加载"""
    try:
        # 尝试导入所需库
        from langchain_community.embeddings import HuggingFaceEmbeddings
        logger.info("成功导入HuggingFaceEmbeddings")
        
        # 设置模型参数
        model_name = 'BAAI/bge-large-zh-v1.5'
        device = 'cpu'
        
        logger.info(f"开始加载模型: {model_name}, 设备: {device}")
        
        # 创建嵌入模型
        embeddings = HuggingFaceEmbeddings(
            model_name=model_name,
            model_kwargs={'device': device}
        )
        logger.info(f"成功加载模型: {model_name}")
        
        # 测试嵌入功能
        test_texts = ["这是一个测试句子", "这是另一个测试句子"]
        logger.info("开始生成嵌入向量...")
        
        embeddings_result = embeddings.embed_documents(test_texts)
        
        logger.info(f"嵌入向量生成成功，维度: {len(embeddings_result[0])}")
        logger.info("测试完成: HuggingFace嵌入模型加载和使用正常")
        
        return True
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("开始测试HuggingFace嵌入模型...")
    success = test_embedding_model()
    logger.info(f"测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1) 