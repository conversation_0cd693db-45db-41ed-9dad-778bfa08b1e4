"""
测试通过Ollama获取嵌入向量
"""
import sys
import os
import logging
import requests

# 设置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ollama_embeddings():
    """测试通过Ollama获取嵌入向量"""
    try:
        # Ollama配置
        base_url = "http://localhost:11434"
        model_name = "nomic-embed-text"  # 默认使用这个模型，也可以导入BAAI/bge-large-zh-v1.5
        
        # 1. 首先检查Ollama服务是否在运行
        logger.info("检查Ollama服务是否在运行...")
        try:
            response = requests.get(f"{base_url}/api/tags", timeout=10)
            if response.status_code != 200:
                logger.error(f"无法连接到Ollama服务，状态码: {response.status_code}")
                return False
            
            # 检查可用模型
            available_models = [model["name"] for model in response.json()["models"]]
            logger.info(f"可用模型: {available_models}")
            
            if model_name not in available_models:
                logger.warning(f"模型 '{model_name}' 在Ollama中不可用")
                logger.info(f"请使用以下命令导入模型:\nollama pull {model_name}")
                return False
                
            logger.info(f"Ollama服务正在运行，模型 '{model_name}' 可用")
        except requests.exceptions.RequestException as e:
            logger.error(f"连接Ollama服务时出错: {str(e)}")
            logger.info("请确保Ollama已安装并运行，可使用以下命令安装:")
            logger.info("Windows (with Scoop): scoop install ollama")
            logger.info("Linux: curl -fsSL https://ollama.com/install.sh | sh")
            logger.info("安装后运行: ollama serve")
            return False
        
        # 2. 获取嵌入向量
        logger.info(f"使用模型 '{model_name}' 获取嵌入向量...")
        
        url = f"{base_url}/api/embeddings"
        text = "这是一个测试句子，用于验证Ollama的嵌入功能。"
        
        response = requests.post(
            url,
            json={"model": model_name, "prompt": text},
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"获取嵌入向量失败，状态码: {response.status_code}, 响应: {response.text}")
            return False
        
        embedding = response.json().get("embedding", [])
        if not embedding:
            logger.error("获取到空的嵌入向量")
            return False
        
        logger.info(f"成功获取嵌入向量，维度: {len(embedding)}")
        
        # 3. 如果没有BAAI/bge-large-zh-v1.5模型，提供导入命令
        if "BAAI/bge-large-zh-v1.5" not in available_models:
            logger.info("\n要导入BAAI/bge-large-zh-v1.5模型，请执行以下步骤:")
            logger.info("1. 创建一个Modelfile:")
            logger.info("   echo 'FROM BAAI/bge-large-zh-v1.5' > Modelfile")
            logger.info("2. 导入模型:")
            logger.info("   ollama create bge-large-zh -f Modelfile")
            logger.info("3. 验证模型:")
            logger.info("   ollama list")
        
        return True
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("开始测试Ollama嵌入功能...")
    success = test_ollama_embeddings()
    logger.info(f"测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1) 