from django.urls import path, re_path, include
from rest_framework.routers import DefaultRouter
from . import views

# 恢复路由器，注册 providers 和 models
router = DefaultRouter()
router.register(r'providers', views.LLMProviderViewSet, basename='llm-providers')
router.register(r'models', views.LLMModelViewSet, basename='llm-models')

# 定义所有非 ViewSet 路由，并添加必要的 `analysis/` 前缀
llm_analysis_urlpatterns = [
    # --- 分析相关 (POST请求) ---
    re_path(r'^analysis/analyze-odds/(?P<match_id>[0-9]+)/$', views.analyze_odds, name='analyze-odds'),
    re_path(r'^analysis/analyze-odds-stream/(?P<match_id>[0-9]+)/$', views.analyze_odds_stream, name='analyze-odds-stream'),
    
    # --- 分析相关 (GET请求) ---
    path('analysis/match/<str:match_id>/check/', views.check_analysis_exists, name='check-analysis-exists'),
    path('analysis/match/<str:match_id>/stream/', views.stream_match_analysis, name='stream-match-analysis'),
    path('analysis/match/<str:match_id>/', views.get_match_analysis, name='get-match-analysis'),
    path('analysis/history/<str:original_match_id>/', views.get_historical_analysis, name='get-historical-analysis'),
    
    # --- 其他功能 ---
    path('config/', views.get_llm_config, name='get-llm-config'),
    path('config/update/', views.update_llm_config, name='update-llm-config'),
    path('analysis/session/', views.create_analysis_session, name='create-analysis-session'),
    path('analysis/control/', views.control_analysis, name='control-analysis'),
    path('models/active/', views.get_active_model_info, name='get-active-model'),
    path('deactivate-model/', views.deactivate_llm_model, name='deactivate-model'),
]

urlpatterns = [
    # 优先包含上面定义的具体路由
    path('', include(llm_analysis_urlpatterns)),
    
    # 然后包含路由器自动生成的URL (providers, models)
    path('', include(router.urls)),

    # 最后添加通用OPTIONS请求的回退处理路由
    re_path(r'^.*$', views.handle_options_request, name='handle-options'),
] 