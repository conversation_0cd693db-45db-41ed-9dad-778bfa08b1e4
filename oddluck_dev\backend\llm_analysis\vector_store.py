import os
import logging
from typing import List, Dict, Any, Optional, Union, Tuple
import uuid
import json
import time
import re
import tempfile

from django.conf import settings
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings
from langchain_community.embeddings import HuggingFaceEmbeddings

logger = logging.getLogger(__name__)

class VectorStoreService:
    """向量存储服务类，用于管理文档向量化和检索"""
    
    def __init__(self):
        """初始化向量存储服务"""
        # 从settings获取环境变量
        embedding_model_name = settings.EMBEDDING_MODEL
        embedding_type = getattr(settings, 'EMBEDDING_TYPE', 'huggingface')
        embedding_device = getattr(settings, 'EMBEDDING_DEVICE', 'cpu')
        embedding_cache_dir = getattr(settings, 'EMBEDDING_CACHE_DIR', None)
        
        # 初始化Embedding模型
        try:
            if embedding_type == 'huggingface':
                # 优先使用HuggingFace的embedding模型
                try:
                    import sentence_transformers
                    
                    # logger.info(f"正在加载HuggingFace embedding模型: {embedding_model_name}")
                    # model_kwargs = {'device': embedding_device}
                    
                    # # 使用模型参数 - 弃用 Hugging Face Hub 上的模型标识符，完全绕过在线检查
                    # embeddings_kwargs = {
                    #     # 添加参数消除resume_download警告
                    #     'model_kwargs': model_kwargs,
                    #     # 不使用已被弃用的resume_download参数
                    #     'encode_kwargs': {'normalize_embeddings': True}
                    # }
                    
                    # if embedding_cache_dir:
                    #     embeddings_kwargs['cache_folder'] = embedding_cache_dir

                    # 直接使用模型的本地快照路径
                    local_model_path = r"C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--BAAI--bge-large-zh-v1.5\\snapshots\\79e7739b6ab944e86d6171e44d24c997fc1e0116".replace('\\\\', '/')
                    logger.info(f"尝试直接从本地路径加载HuggingFace embedding模型: {local_model_path}")
                    # 确保 embedding_model_name 仍然被记录，但实际加载使用 local_model_path
                    logger.info(f"(原始模型标识符: {embedding_model_name})")

                    model_kwargs = {'device': embedding_device}
                    
                    # cache_folder 在直接指定路径时可能不是必需的，但保留无妨
                    user_cache_dir = r"C:\\Users\\<USER>\\.cache".replace('\\\\', '/') 
                    cache_dir = embedding_cache_dir or user_cache_dir
                    
                    embeddings_kwargs = {
                        'model_kwargs': model_kwargs,
                        'encode_kwargs': {'normalize_embeddings': True},
                        'cache_folder': cache_dir 
                    }
                    
                    # 使用本地路径加载模型
                    self.embeddings = HuggingFaceEmbeddings(
                        model_name=local_model_path,
                        **embeddings_kwargs
                    )
                    # 日志中仍然显示原始模型名，方便识别
                    logger.info(f"成功从本地路径加载HuggingFace embedding模型 (原始标识符: {embedding_model_name})")
                    
                    # 测试模型
                    test_embeddings = self.embeddings.embed_query("测试模型加载")
                    logger.info(f"模型测试成功，嵌入维度: {len(test_embeddings)}")
                except ImportError:
                    logger.error("缺少sentence_transformers库，请先安装")
                    raise ImportError(
                        "请使用以下命令安装sentence_transformers库：\n"
                        "pip install sentence-transformers\n"
                        "然后重新启动服务。"
                    )
                except Exception as e:
                     # 错误处理保持增强版本
                    if "Connection to huggingface.co timed out" in str(e) or "Max retries exceeded" in str(e):
                         # 如果直接用本地路径还报网络错误，说明可能仍有隐藏的网络检查或依赖问题
                         logger.error(f"即使使用本地路径 '{local_model_path}' 加载模型，仍然发生网络连接错误: {str(e)}. 请检查网络或模型文件是否完整。", exc_info=True)
                         raise ValueError(f"网络连接超时，无法验证本地模型: {str(e)}")
                    else:
                        logger.error(f"加载嵌入模型时发生未知错误: {str(e)}", exc_info=True)
                        raise ValueError(f"加载嵌入模型失败: {str(e)}")
            elif embedding_type == 'ollama':
                # 使用Ollama的embedding API
                try:
                    from .ollama_embeddings import OllamaEmbeddings
                    
                    # 获取Ollama配置
                    ollama_base_url = getattr(settings, 'OLLAMA_BASE_URL', 'http://localhost:11434')
                    logger.info(f"正在通过Ollama加载embedding模型: {embedding_model_name}")
                    
                    self.embeddings = OllamaEmbeddings(
                        model_name=embedding_model_name,
                        base_url=ollama_base_url
                    )
                    logger.info(f"成功通过Ollama加载embedding模型: {embedding_model_name}")
                except ImportError:
                    logger.error("缺少requests库，请先安装")
                    raise ImportError(
                        "请使用以下命令安装requests库：\n"
                        "pip install requests\n"
                        "然后重新启动服务。"
                    )
                except Exception as e:
                    logger.error(f"连接Ollama服务失败: {str(e)}", exc_info=True)
                    raise ValueError(f"连接Ollama服务失败: {str(e)}")
            else:
                # 不再支持的模型类型
                logger.error(f"不支持的embedding模型类型: {embedding_type}")
                raise ValueError(f"不支持的embedding模型类型: {embedding_type}，请使用'huggingface'或'ollama'")
        except Exception as e:
            logger.error(f"初始化embedding模型时出错: {str(e)}", exc_info=True)
            raise
        
        # 向量存储目录
        self.vector_store_dir = os.path.join(settings.MEDIA_ROOT, 'vector_stores')
        os.makedirs(self.vector_store_dir, exist_ok=True)
    
    def _get_vector_store_path(self, vector_store_id: str) -> str:
        """获取向量存储的路径"""
        return os.path.join(self.vector_store_dir, vector_store_id)
    
    def create_vector_store(self, texts: List[str], metadata: List[Dict] = None) -> str:
        """创建新的向量存储
        
        Args:
            texts: 文本列表
            metadata: 元数据列表，每个文本对应一个元数据字典
            
        Returns:
            str: 向量存储ID
        """
        if not texts:
            raise ValueError("文本列表不能为空")
        
        # 生成唯一的向量存储ID
        vector_store_id = str(uuid.uuid4())
        vector_store_path = self._get_vector_store_path(vector_store_id)
        
        try:
            # 创建向量存储
            if metadata:
                vector_store = FAISS.from_texts(texts, self.embeddings, metadatas=metadata)
            else:
                vector_store = FAISS.from_texts(texts, self.embeddings)
            
            # 保存向量存储
            vector_store.save_local(vector_store_path)
            logger.info(f"向量存储创建成功，ID: {vector_store_id}")
            
            return vector_store_id
        except Exception as e:
            logger.error(f"创建向量存储失败: {str(e)}", exc_info=True)
            raise
    
    def load_vector_store(self, vector_store_id: str) -> Optional[FAISS]:
        """加载向量存储
        
        Args:
            vector_store_id: 向量存储ID
            
        Returns:
            FAISS: 向量存储对象，如果不存在则返回None
        """
        vector_store_path = self._get_vector_store_path(vector_store_id)
        
        if not os.path.exists(vector_store_path):
            logger.warning(f"向量存储不存在: {vector_store_id}")
            return None
        
        try:
            # 添加allow_dangerous_deserialization=True参数以允许加载pickle文件
            # 注意：只有在确认数据源安全的情况下使用此参数
            vector_store = FAISS.load_local(
                vector_store_path, 
                self.embeddings, 
                allow_dangerous_deserialization=True
            )
            logger.info(f"向量存储加载成功: {vector_store_id}")
            return vector_store
        except Exception as e:
            logger.error(f"加载向量存储失败: {str(e)}", exc_info=True)
            return None
    
    def delete_vector_store(self, vector_store_id: str) -> bool:
        """删除向量存储
        
        Args:
            vector_store_id: 向量存储ID
            
        Returns:
            bool: 是否删除成功
        """
        import shutil
        vector_store_path = self._get_vector_store_path(vector_store_id)
        
        if not os.path.exists(vector_store_path):
            logger.warning(f"要删除的向量存储不存在: {vector_store_id}")
            return False
        
        try:
            shutil.rmtree(vector_store_path)
            logger.info(f"向量存储删除成功: {vector_store_id}")
            return True
        except Exception as e:
            logger.error(f"删除向量存储失败: {str(e)}", exc_info=True)
            return False
    
    def search_similar_documents(self, query: str, limit: int = 5) -> List[Dict]:
        """在所有知识库中搜索相似文档
        
        Args:
            query: 查询文本
            limit: 返回结果数量
            
        Returns:
            List[Dict]: 相似文档列表，包含文档内容和相似度分数
        """
        start_time = time.time()
        results = []
        
        # 修改: 使用 KnowledgeFile 代替 KnowledgeBase
        from knowledge_api.models import KnowledgeFile
        
        # 获取所有已处理向量化的知识库文件
        knowledge_files = KnowledgeFile.objects.filter(
            is_vectorized=True,
            vectorization_status=KnowledgeFile.STATUS_COMPLETED,
            vector_store_id__isnull=False
        )
        
        if not knowledge_files.exists():
            logger.warning("没有可搜索的向量化知识库文件")
            return []
        
        # 对每个知识库文件进行相似度搜索
        for file in knowledge_files:
            vector_store = self.load_vector_store(file.vector_store_id)
            if not vector_store:
                continue
            
            # 执行相似度搜索
            try:
                docs_with_scores = vector_store.similarity_search_with_score(query, k=limit)
                
                for doc, score in docs_with_scores:
                    # 计算相似度得分（将距离转换为相似度）
                    similarity = 1.0 / (1.0 + score)
                    
                    # 添加到结果列表
                    results.append({
                        "content": doc.page_content,
                        "metadata": doc.metadata,
                        "knowledge_id": file.id,
                        "knowledge_title": file.name,
                        "similarity": similarity
                    })
            except Exception as e:
                logger.error(f"搜索文件 {file.id} ({file.name}) 时出错: {str(e)}")
                continue
        
        # 按相似度排序
        results.sort(key=lambda x: x["similarity"], reverse=True)
        
        # 只返回top N个结果
        results = results[:limit]
        
        logger.info(f"相似文档搜索完成，耗时: {time.time() - start_time:.2f}秒，找到: {len(results)}个结果")
        return results

    def search(self, query: str, store_id: str, limit: int = 5) -> List[Dict]:
        """在单个向量存储中搜索
        
        Args:
            query: 搜索查询
            store_id: 向量存储ID
            limit: 返回结果数量上限，默认为5
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        try:
            # 加载向量存储
            vector_store = self.load_vector_store(store_id)
            if not vector_store:
                logger.warning(f"无法加载向量存储: {store_id}")
                return []
            
            # 执行相似度搜索
            docs_with_scores = vector_store.similarity_search_with_score(query, k=limit)
            
            results = []
            for doc, score in docs_with_scores:
                # 计算相似度得分（将距离转换为相似度）
                similarity = 1.0 / (1.0 + score)
                
                # 添加到结果列表
                results.append({
                    "id": doc.metadata.get("id", "unknown"),
                    "title": doc.metadata.get("title", "Unknown"),
                    "text": doc.page_content,
                    "source": doc.metadata.get("source", ""),
                    "category": doc.metadata.get("category", ""),
                    "similarity": similarity
                })
            
            # 按相似度排序
            results.sort(key=lambda x: x["similarity"], reverse=True)
            return results
            
        except Exception as e:
            logger.error(f"搜索向量存储 {store_id} 时出错: {str(e)}", exc_info=True)
            return []

    def search_multiple_stores(self, query: str, store_ids: List[str], limit: int = 5) -> List[Dict]:
        """在多个向量存储中搜索
        
        Args:
            query: 搜索查询
            store_ids: 向量存储ID列表
            limit: 返回结果数量上限，默认为5
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        if not store_ids:
            return []
            
        # 合并所有结果
        all_results = []
        
        # 最小相似度阈值，低于此值的结果将被过滤掉
        # 降低阈值以包含更多相关但相似度不那么高的结果
        min_similarity_threshold = 0.15
        
        # 查询每个向量存储
        for store_id in store_ids:
            try:
                # 在单个查询中增加结果数量，以获取更多候选项
                results = self.search(query, store_id, limit=limit*2)
                all_results.extend(results)
            except Exception as e:
                logger.error(f"搜索向量存储 {store_id} 时出错: {str(e)}", exc_info=True)
                continue
        
        # 过滤掉相似度低于阈值的结果
        filtered_results = [
            result for result in all_results 
            if result.get('similarity', 0) >= min_similarity_threshold
        ]
        
        # 如果过滤后结果太少，降低阈值再次尝试
        if len(filtered_results) < limit and all_results:
            lower_threshold = 0.10  # 更低的阈值
            filtered_results = [
                result for result in all_results 
                if result.get('similarity', 0) >= lower_threshold
            ]
            logger.info(f"降低相似度阈值至 {lower_threshold} 后找到 {len(filtered_results)} 条结果")
        
        # 按相似度排序 - 修正排序逻辑
        filtered_results.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        
        # 添加调试日志
        logger.info(f"搜索多个向量存储完成，过滤前有 {len(all_results)} 条结果，过滤后有 {len(filtered_results)} 条结果")
        for i, result in enumerate(filtered_results[:5]):
            logger.info(f"  - 结果 #{i+1}: 标题={result.get('title', 'Unknown')}, 相似度={result.get('similarity', 0):.4f}")
        
        # 截取前limit个结果
        return filtered_results[:limit]

    def embed_knowledge_base(self, knowledge_id: int) -> bool:
        """将知识库条目转化为嵌入向量并存储
        
        此方法已弃用，请使用embed_knowledge_file代替
        
        Args:
            knowledge_id: 知识库ID
            
        Returns:
            bool: 是否成功嵌入
        """
        logger.warning("调用了已弃用的embed_knowledge_base方法，请改用embed_knowledge_file")
        return False