from django.shortcuts import render, get_object_or_404
import json
from rest_framework import status, viewsets
from rest_framework.decorators import api_view, action, permission_classes
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser, JSONParser
from django.http import StreamingHttpResponse, JsonResponse, HttpResponse
import logging
from typing import List, Dict, Any
from django.conf import settings
from rest_framework.permissions import IsAuthenticated
import os
import sys
import uuid
from datetime import datetime
from django.db.models import Sum, Count, Avg, Max, Q
from django.utils import timezone
import asyncio
import concurrent.futures
from asgiref.sync import sync_to_async
from asgiref.sync import async_to_sync

from api.models import Match
from .models import OddsAnalysis, LLMProvider, LLMModel
from .llm_service import LLMService
from .serializers import KnowledgeBaseSerializer, OddsAnalysisSerializer, LLMProviderSerializer, LLMModelSerializer

logger = logging.getLogger(__name__)

@api_view(['POST'])
def analyze_odds(request, match_id):
    """
    分析指定比赛的赔率
    """
    # === 新增最高优先级日志 ===
    logger.warning("analyze_odds view function started!") 
    # === 日志结束 ===
    try:
        # 动态获取当前激活的LLM配置
        try:
            active_model = LLMModel.objects.filter(is_active=True).first()
            logger.info(f"数据库查询到的激活模型: {active_model}")
            if not active_model:
                logger.warning("请求分析时没有找到激活的LLM模型。")
                return Response(
                    {'error': '请先在"配置LLM"页面选择并激活一个模型后再进行分析。'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            else:
                model_name = active_model.model_name
                base_url = active_model.base_url
                api_key = active_model.api_key
                logger.info(f"使用数据库中激活的模型进行分析: {model_name}")
        except Exception as db_err:
            logger.error(f"查询激活LLM模型时出错: {db_err}", exc_info=True)
            return Response(
                {'error': f'查询激活LLM模型配置时出错: {db_err}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
        # 获取请求参数
        data = request.data
        extra_data = data.get('extra_data', None)  # 获取额外数据
        
        logger.info(f"开始分析赔率，match_id={match_id}，使用模型: {model_name}，是否有额外数据：{extra_data is not None}")
        
        # 初始化LLM服务，传入动态获取的配置
        llm_service = LLMService(llm_model=model_name, base_url=base_url, api_key=api_key, request=request)
        logger.info("About to call async_to_sync(llm_service.analyze_match_odds)")

        try:
            # 分析赔率，传递额外数据
            result = async_to_sync(llm_service.analyze_match_odds)(match_id, extra_data)
            logger.info(f"Call to async_to_sync(llm_service.analyze_match_odds) completed. Result keys: {result.keys() if isinstance(result, dict) else type(result)}")
        except Exception as inner_e:
            logger.error(f"Error during async_to_sync(llm_service.analyze_match_odds): {inner_e}", exc_info=True)
            return Response({'error': f"分析赔率时出错: {str(inner_e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
        logger.info("Returning response from analyze_odds view")
        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        logger.error(f"赔率分析值错误: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"赔率分析失败: {str(e)}", exc_info=True)
        error_message = f"分析处理失败: {str(e)}"
        return Response({'error': error_message}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def analyze_odds_stream(request, match_id):
    """
    分析指定比赛的赔率（流式输出）
    """
    # === 新增最高优先级日志 ===
    logger.warning("analyze_odds_stream view function started!") 
    # === 日志结束 ===
    try:
        # 动态获取当前激活的LLM配置
        try:
            active_model = LLMModel.objects.filter(is_active=True).first()
            logger.info(f"数据库查询到的激活(流式)模型: {active_model}")
            if not active_model:
                logger.warning("请求流式分析时没有找到激活的LLM模型。")
                return Response(
                    {'error': '请先在"配置LLM"页面选择并激活一个模型后再进行分析。'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            else:
                model_name = active_model.model_name
                base_url = active_model.base_url
                api_key = active_model.api_key
                logger.info(f"使用数据库中激活的模型进行流式分析: {model_name}")
        except Exception as db_err:
            logger.error(f"查询激活LLM模型时出错: {db_err}", exc_info=True)
            return Response(
                {'error': f'查询激活LLM模型配置时出错: {db_err}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
        # 获取请求参数
        data = request.data
        extra_data = data.get('extra_data', None)  # 获取额外数据
        session_id = data.get('session_id', str(uuid.uuid4()))  # 获取会话ID或生成新的
        
        logger.info(f"开始流式分析赔率，match_id={match_id}，使用模型: {model_name}，是否有额外数据：{extra_data is not None}")
        if extra_data:
            # 记录一下收到的额外数据类型和大小
            bookmakers = extra_data.get('bookmakers', []) if isinstance(extra_data, dict) else []
            bookmaker_count = len(bookmakers)
            total_odds_count = sum(len(b.get('odds', [])) for b in bookmakers) if bookmaker_count > 0 else 0
            logger.info(f"收到额外数据: {type(extra_data)}, 博彩公司数量: {bookmaker_count}, 总赔率数据点: {total_odds_count}")
            logger.info(f"额外数据字段: {list(extra_data.keys() if isinstance(extra_data, dict) else [])}")
            
            # 检查match_info数据
            if isinstance(extra_data, dict) and 'match_info' in extra_data:
                match_info = extra_data.get('match_info', {})
                logger.info(f"比赛信息: {match_info}")
        
        # 初始化LLM服务，传入动态获取的配置
        llm_service = LLMService(llm_model=model_name, base_url=base_url, api_key=api_key, request=request)
        
        # 定义异步事件流生成器，接收 llm_service 作为参数
        async def generate_events_async(llm_service_instance):
            nonlocal match_id, session_id # 只保留这两个
            try:
                # 创建LLM服务实例的代码已移到外部
                logger.info(f"使用LLM服务实例 (传入参数): {model_name} @ {base_url}")

                # 发送开始事件
                yield "event: start\ndata: {\"type\":\"info\",\"message\":\"开始分析...\"}\n\n"
                await asyncio.sleep(0.01)

                # 使用传入的 llm_service_instance
                async for chunk in llm_service_instance.analyze_match_odds_stream(match_id, None):
                    if chunk.get('done', False):
                        logger.info(f"分析完成，生成内容长度: {len(chunk.get('analysis', ''))}")
                        yield f"event: done\ndata: {json.dumps(chunk)}\n\n"
                        await asyncio.sleep(0.01)
                    else:
                        yield f"event: chunk\ndata: {json.dumps(chunk)}\n\n"
                        await asyncio.sleep(0.01)

            except asyncio.CancelledError:
                logger.warning(f"客户端 {session_id} 在 generate_events 中断开了连接.")
                raise
            except Exception as e:
                logger.exception(f"生成事件流时出错 ({session_id})")
                error_msg = {
                    'error': f"生成分析时出错: {type(e).__name__}",
                    'type': 'error',
                    'done': True,
                    'match_id': match_id,
                    'session_id': session_id
                }
                try:
                    yield f"event: error\ndata: {json.dumps(error_msg)}\n\n"
                    await asyncio.sleep(0.01)
                except Exception as yield_err:
                    logger.error(f"在 yield 错误信息时再次出错 (generate_events): {yield_err}")
            finally:
                logger.info(f"事件生成器 generate_events_async ({session_id}) 结束.")
                
        # 创建同步迭代器适配器，将异步迭代器转换为同步迭代器
        def sync_iterator_adapter():
            """
            这个适配器将异步迭代器转换为同步迭代器，
            通过创建一个队列和一个后台任务来处理异步迭代。
            """
            logger.info(f"创建同步迭代器适配器...")
            
            # 创建一个队列用于在异步和同步世界之间传递数据
            queue = asyncio.Queue()
            stop_event = asyncio.Event()
            
            # 创建一个异步任务来运行异步迭代器并将结果放入队列
            async def consumer_task():
                try:
                    logger.info(f"启动异步消费者任务...")
                    async for item in generate_events_async(llm_service):
                        await queue.put(item)
                        # 如果异步迭代器已完成，放入None作为结束标记
                    await queue.put(None)  # 结束标记
                except Exception as e:
                    logger.exception(f"异步消费者任务出错: {e}")
                    await queue.put(None)  # 确保在错误时也发送结束标记
                finally:
                    stop_event.set()
                    logger.info(f"异步消费者任务结束")
                    
            # 创建并启动后台任务
            loop = asyncio.new_event_loop()
            
            def run_async_task():
                asyncio.set_event_loop(loop)
                loop.run_until_complete(consumer_task())
                
            import threading
            background_thread = threading.Thread(target=run_async_task)
            background_thread.daemon = True  # 设置为守护线程，这样主线程结束时它也会结束
            background_thread.start()
            
            # 同步迭代器主体
            logger.info(f"开始同步产出数据...")
            try:
                while not stop_event.is_set() or not queue.empty():
                    try:
                        # 非阻塞地从队列中获取下一个项目，最多等待0.1秒
                        future = asyncio.run_coroutine_threadsafe(
                            queue.get(), loop
                        )
                        item = future.result(timeout=0.1)  # 最多等待0.1秒
                        
                        # 如果收到结束标记，退出循环
                        if item is None:
                            logger.info(f"收到结束标记，同步迭代器退出")
                            break
                            
                        # 记录成功产出的数据类型
                        if "event: chunk" in item:
                            logger.info(f"同步产出chunk事件")
                        elif "event: done" in item:
                            logger.info(f"同步产出done事件")
                        elif "event: start" in item:
                            logger.info(f"同步产出start事件")
                        elif "event: error" in item:
                            logger.info(f"同步产出error事件")
                            
                        yield item
                    except concurrent.futures.TimeoutError:
                        # 超时但未结束，继续尝试
                        continue
                    except Exception as e:
                        logger.exception(f"同步产出过程中出错: {e}")
                        yield f"event: error\ndata: {{\"error\":\"同步迭代器错误: {str(e)}\",\"type\":\"error\",\"done\":true}}\n\n"
                        break
            finally:
                # 清理资源
                logger.info(f"同步迭代器适配器结束，准备清理资源")
                if not stop_event.is_set():
                    future = asyncio.run_coroutine_threadsafe(stop_event.set(), loop)
                    try:
                        future.result(timeout=1.0)
                    except Exception:
                        pass
                
                # 等待后台线程完成
                background_thread.join(timeout=2.0)
                logger.info(f"同步迭代器适配器完全结束")

        # 创建流式响应，使用同步迭代器适配器
        logger.info("创建流式响应...")
        response = StreamingHttpResponse(
            sync_iterator_adapter(),  # 使用同步适配器
            content_type='text/event-stream',
            status=200
        )
        
        # 添加必要的headers - 不设置Connection头，让服务器自动处理
        response['Cache-Control'] = 'no-cache'
        response['X-Accel-Buffering'] = 'no'  # 禁用Nginx缓冲
        response['Access-Control-Allow-Origin'] = '*'  # 允许跨域
        
        return response
    
    except ValueError as e:
        logger.error(f"流式分析赔率值错误: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_404_NOT_FOUND)
        
    except Exception as e:
        logger.error(f"初始化流式分析失败: {str(e)}", exc_info=True)
        return Response({'error': f"初始化流式分析失败: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_analysis_history(request, match_id=None):
    """
    获取分析历史记录
    """
    try:
        if match_id:
            # 获取指定比赛的分析
            analyses = OddsAnalysis.objects.filter(match_id=match_id).order_by('-created_at')
        else:
            # 获取所有分析
            analyses = OddsAnalysis.objects.all().order_by('-created_at')[:20]  # 限制返回最近20条
        
        results = []
        for analysis in analyses:
            results.append({
                'id': analysis.id,
                'match_id': analysis.match_id,
                'analysis_text': analysis.analysis_text,
                'prompt_text': analysis.prompt_text,
                'llm_model': analysis.llm_model,
                'created_at': analysis.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return Response(results, status=status.HTTP_200_OK)
    
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class LLMAnalysisViewSet(viewsets.ViewSet):
    """处理LLM分析相关的视图集"""
    
    @action(detail=False, methods=['post'], url_path='analyze-odds')
    def analyze_odds(self, request):
        """使用LLM分析比赛赔率
        
        POST /api/llm/analyze-odds/
        请求体: {
            "match_id": "比赛ID",
            "model": "模型名称" (可选，默认为DeepSeek模型),
            "extra_data": { ... } (可选，前端提供的额外赔率数据)
        }
        """
        match_id = request.data.get('match_id')
        model = request.data.get('model', settings.LLM_MODEL_NAME)
        extra_data = request.data.get('extra_data', None)
        
        if not match_id:
            return Response(
                {'error': '缺少必要参数: match_id'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            # 验证比赛ID是否存在
            match = Match.objects.filter(match_id=match_id).first()
            if not match:
                return Response(
                    {'error': f'未找到ID为 {match_id} 的比赛'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 如果没有提供额外数据，检查是否已有分析结果
            if not extra_data:
                existing_analysis = OddsAnalysis.objects.filter(
                    match_id=match_id, 
                    llm_model=model
                ).order_by('-created_at').first()
                
                # 如果已有结果且不超过1小时，直接返回
                if existing_analysis:
                    return Response({
                        'match_id': match_id,
                        'analysis': existing_analysis.analysis_text,
                        'match_info': existing_analysis.prompt_text.split('赔率数据:')[0].replace('比赛信息:', '').strip(),
                        'used_knowledge': [],  # 历史数据暂不提供知识来源
                        'is_cached': True,
                        'created_at': existing_analysis.created_at
                    })
            
            # 创建LLM服务并分析，传递额外数据
            llm_service = LLMService(llm_model=model)
            result = llm_service.analyze_match_odds(match_id, extra_data)
            
            return Response(result)
            
        except Exception as e:
            logger.error(f"分析赔率时出错: {str(e)}", exc_info=True)
            return Response(
                {'error': f'分析赔率时出错: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'], url_path='analyze-odds-stream')
    def analyze_odds_stream(self, request):
        """使用LLM流式分析比赛赔率
        
        POST /api/llm/analyze-odds-stream/
        请求体: {
            "match_id": "比赛ID",
            "model": "模型名称" (可选),
            "extra_data": { ... } (可选，前端提供的额外赔率数据)
        }
        """
        match_id = request.data.get('match_id')
        model = request.data.get('model', settings.LLM_MODEL_NAME)
        extra_data = request.data.get('extra_data', None)
        session_id = request.data.get('session_id', str(uuid.uuid4()))  # 获取会话ID或生成新的
        
        if not match_id:
            return Response(
                {'error': '缺少必要参数: match_id'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            # 验证比赛ID是否存在
            match = Match.objects.filter(match_id=match_id).first()
            if not match:
                return Response(
                    {'error': f'未找到ID为 {match_id} 的比赛'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 定义流式响应生成器
            def event_stream():
                try:
                    # === 临时测试代码 - 移除 ===
                    # import time
                    # logger.info("开始发送测试事件流...")
                    # yield "event: start\ndata: {\"type\":\"info\",\"message\":\"开始测试分析...\"}\n\n"
                    # time.sleep(1)
                    # yield "event: chunk\ndata: {\"analysis\": \"这是第一块测试数据... \"}\n\n"
                    # time.sleep(1)
                    # yield "event: chunk\ndata: {\"analysis\": \"这是第二块测试数据，即将完成。\"}\n\n"
                    # time.sleep(1)
                    # yield "event: done\ndata: {\"analysis\": \"测试分析完成。\", \"done\": true}\n\n"
                    # logger.info("测试事件流发送完毕。")
                    
                    # === 原始代码 - 恢复 ===
                    llm_service = LLMService(llm_model=model)
                    logger.info(f"创建LLM服务实例成功: {model}")
                    
                    yield "event: start\ndata: {\"type\":\"info\",\"message\":\"开始分析...\"}\n\n"
                    
                    for chunk in llm_service.analyze_match_odds_stream(match_id, extra_data):
                        if chunk.get('done', False):
                            logger.info(f"分析完成，生成内容长度: {len(chunk.get('analysis', ''))}")
                            yield f"event: done\ndata: {json.dumps(chunk)}\n\n"
                        else:
                            yield f"event: chunk\ndata: {json.dumps(chunk)}\n\n"
                    # === 原始代码结束 ===
                    
                except Exception as e:
                    logger.error(f"生成事件流时出错: {str(e)}", exc_info=True)
                    error_msg = {
                        'error': str(e),
                        'type': 'error',
                        'done': True,
                        'match_id': match_id,
                        'session_id': session_id
                    }
                    yield f"event: error\ndata: {json.dumps(error_msg)}\n\n"
            
            # 创建流式响应
            logger.info("创建流式响应...")
            response = StreamingHttpResponse(
                event_stream(),
                content_type='text/event-stream',
                status=200
            )
            
            # 设置SSE所需的响应头 - 不设置Connection头，让服务器自动处理
            response['Cache-Control'] = 'no-cache'
            response['X-Accel-Buffering'] = 'no'
            response['Access-Control-Allow-Origin'] = '*'
            
            logger.info(f"响应头设置完成: {dict(response.items())}")
            return response
            
        except Exception as e:
            logger.error(f"准备流式分析时出错: {str(e)}", exc_info=True)
            return Response(
                {'error': f'准备流式分析时出错: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'], url_path='history')
    def get_history(self, request):
        """获取分析历史
        
        GET /api/llm/history/
        """
        try:
            analyses = OddsAnalysis.objects.all().order_by('-created_at')[:20]
            serializer = OddsAnalysisSerializer(analyses, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"获取分析历史时出错: {str(e)}", exc_info=True)
            return Response(
                {'error': f'获取分析历史时出错: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'], url_path='history')
    def get_match_history(self, request, pk=None):
        """获取特定比赛的分析历史
        
        GET /api/llm/history/{match_id}/
        """
        try:
            analyses = OddsAnalysis.objects.filter(match_id=pk).order_by('-created_at')
            
            results = []
            for analysis in analyses:
                results.append({
                    'id': analysis.id,
                    'match_id': analysis.match_id,
                    'analysis_text': analysis.analysis_text,
                    'prompt_text': analysis.prompt_text,
                    'llm_model': analysis.llm_model,
                    'created_at': analysis.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })
            
            return Response(results)
            
        except Exception as e:
            logger.error(f"获取分析历史时出错: {str(e)}", exc_info=True)
            return Response(
                {'error': f'获取分析历史时出错: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class KnowledgeBaseViewSet(viewsets.ModelViewSet):
    """知识库视图集，已完全弃用并重定向到knowledge_api应用
    
    所有操作都已重定向到knowledge_api应用的相应端点
    为了保持API兼容性，保留了视图集但不再使用KnowledgeBase模型
    """
    serializer_class = KnowledgeBaseSerializer
    
    def get_queryset(self):
        """返回空查询集，因为KnowledgeBase表已不再使用"""
        return []
    
    def list(self, request, *args, **kwargs):
        """
        获取知识库列表 - 重定向到knowledge_api
        """
        # 检查用户是否登录
        if not request.user.is_authenticated:
            return Response({"error": "请先登录"}, status=status.HTTP_401_UNAUTHORIZED)
            
        # 从knowledge_api获取用户的知识文件
        from knowledge_api.models import KnowledgeFile
        from knowledge_api.serializers import KnowledgeFileSerializer
        
        # 分页参数
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)
        
        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            page = 1
            page_size = 10
            
        # 获取用户知识文件
        files = KnowledgeFile.objects.filter(user=request.user).order_by('-upload_time')
        
        # 计算分页
        start = (page - 1) * page_size
        end = start + page_size
        
        # 切片数据
        paged_files = files[start:end]
        
        # 序列化
        file_serializer = KnowledgeFileSerializer(paged_files, many=True)
        
        # 适配为旧API响应格式
        results = []
        for file in file_serializer.data:
            results.append({
                'id': file['id'],
                'title': file['name'],
                'content': f"文件: {file['name']} (向量化状态: {file['vectorization_status']})",
                'source': file.get('category_name', ''),
                'category': file.get('category_name', ''),
                'created_at': file['upload_time'],
                'updated_at': file['upload_time'],
                'file': file.get('file', None),
                'embedding_processed': file['is_vectorized'],
                'vector_store_id': file.get('vector_store_id', None)
            })
            
        # 返回适配的响应
        return Response({
            'count': files.count(),
            'next': f"/api/llm/knowledge/?page={page+1}&page_size={page_size}" if end < files.count() else None,
            'previous': f"/api/llm/knowledge/?page={page-1}&page_size={page_size}" if page > 1 else None,
            'results': results
        })
    
    def create(self, request, *args, **kwargs):
        """
        创建知识库 - 已适配到新系统
        此方法接收JSON格式的知识内容并保存
        """
        # 检查用户是否登录
        if not request.user.is_authenticated:
            return Response({"error": "请先登录"}, status=status.HTTP_401_UNAUTHORIZED)
            
        # 获取参数
        title = request.data.get('title')
        content = request.data.get('content')
        source = request.data.get('source', '')
        category_name = request.data.get('category', '未分类')
        
        if not title or not content:
            return Response({"error": "标题和内容不能为空"}, status=status.HTTP_400_BAD_REQUEST)
            
        # 导入需要的模型
        from knowledge_api.models import Category, KnowledgeFile
        from django.core.files.base import ContentFile
        import tempfile
        import os
        from datetime import datetime
        
        # 获取或创建分类
        category, created = Category.objects.get_or_create(
            name=category_name,
            user=request.user
        )
        
        # 创建临时文本文件
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp:
            temp_path = temp.name
            temp.write(content.encode('utf-8'))
            
        try:
            # 创建知识文件记录
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            file_name = f"{title}_{timestamp}.txt"
            
            with open(temp_path, 'rb') as f:
                knowledge_file = KnowledgeFile.objects.create(
                    name=file_name,
                    file=ContentFile(f.read(), name=file_name),
                    size=os.path.getsize(temp_path),
                    category=category,
                    user=request.user,
                    vectorization_status=KnowledgeFile.STATUS_PENDING
                )
                
            # 启动异步向量化
            from knowledge_api.views import KnowledgeFileViewSet
            thread = threading.Thread(
                target=KnowledgeFileViewSet._process_file_vectorization,
                args=(None, knowledge_file.id)
            )
            thread.daemon = True
            thread.start()
            
            # 返回适配的响应
            return Response({
                'id': knowledge_file.id,
                'title': knowledge_file.name,
                'content': content[:200] + '...' if len(content) > 200 else content,
                'source': source,
                'category': category_name,
                'file': knowledge_file.file.url if knowledge_file.file else None,
                'embedding_processed': False,
                'created_at': knowledge_file.upload_time,
                'updated_at': knowledge_file.upload_time
            }, status=status.HTTP_201_CREATED)
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    def retrieve(self, request, pk=None, *args, **kwargs):
        """获取单个知识库详情 - 已适配到新系统"""
        # 检查用户是否登录
        if not request.user.is_authenticated:
            return Response({"error": "请先登录"}, status=status.HTTP_401_UNAUTHORIZED)
            
        # 从knowledge_api获取文件
        from knowledge_api.models import KnowledgeFile
        from knowledge_api.serializers import KnowledgeFileSerializer
        
        try:
            # 查找文件
            file = KnowledgeFile.objects.get(id=pk, user=request.user)
            file_serializer = KnowledgeFileSerializer(file)
            
            # 读取文件内容
            content = "文件内容无法直接获取，请下载文件查看"
            if file.file and os.path.exists(file.file.path):
                try:
                    with open(file.file.path, 'r', encoding='utf-8') as f:
                        content = f.read()
                except:
                    pass
            
            # 适配为旧API响应格式
            return Response({
                'id': file.id,
                'title': file.name,
                'content': content,
                'source': file_serializer.data.get('category_name', ''),
                'category': file_serializer.data.get('category_name', ''),
                'created_at': file.upload_time,
                'updated_at': file.upload_time,
                'file': file_serializer.data.get('file', None),
                'embedding_processed': file.is_vectorized,
                'vector_store_id': file.vector_store_id
            })
            
        except KnowledgeFile.DoesNotExist:
            return Response({"error": "知识文件不存在"}, status=status.HTTP_404_NOT_FOUND)
    
    def destroy(self, request, pk=None, *args, **kwargs):
        """删除知识库 - 已适配到新系统"""
        # 检查用户是否登录
        if not request.user.is_authenticated:
            return Response({"error": "请先登录"}, status=status.HTTP_401_UNAUTHORIZED)
            
        # 从knowledge_api删除文件
        from knowledge_api.models import KnowledgeFile
        
        try:
            # 查找文件
            file = KnowledgeFile.objects.get(id=pk, user=request.user)
            file.delete()
            
            return Response(status=status.HTTP_204_NO_CONTENT)
            
        except KnowledgeFile.DoesNotExist:
            return Response({"error": "知识文件不存在"}, status=status.HTTP_404_NOT_FOUND)
    
    def update(self, request, pk=None, *args, **kwargs):
        """更新知识库 - 已适配到新系统"""
        # 暂时不支持更新，返回不支持操作
        return Response({"error": "此操作不再支持，请使用新的API"}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索知识库 - 已适配到新系统"""
        # 检查用户是否登录
        if not request.user.is_authenticated:
            return Response({"error": "请先登录"}, status=status.HTTP_401_UNAUTHORIZED)
            
        # 获取查询参数
        query = request.query_params.get('q', '')
        
        if not query:
            return Response({"error": "查询参数不能为空"}, status=status.HTTP_400_BAD_REQUEST)
        
        # 导入向量搜索服务
        from llm_analysis.vector_store import VectorStoreService
        vector_service = VectorStoreService()
        
        # 从knowledge_api获取文件
        from knowledge_api.models import KnowledgeFile
        
        # 获取当前用户的所有向量化文件ID
        file_ids = KnowledgeFile.objects.filter(
            user=request.user, 
            is_vectorized=True,
            vectorization_status=KnowledgeFile.STATUS_COMPLETED
        ).values_list('vector_store_id', flat=True)
        
        # 执行向量搜索
        try:
            results = vector_service.search_multiple_stores(query, list(file_ids), limit=5)
            
            # 适配为旧API响应格式
            response_data = []
            for item in results:
                response_data.append({
                    'id': item.get('id', 0),
                    'title': item.get('title', '未知'),
                    'content': item.get('text', ''),
                    'source': item.get('source', ''),
                    'category': item.get('category', ''),
                    'score': item.get('score', 0),
                })
            
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"搜索知识库时出错: {str(e)}", exc_info=True)
            return Response({"error": f"搜索失败: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class LLMProviderViewSet(viewsets.ModelViewSet):
    """LLM提供商视图集"""
    queryset = LLMProvider.objects.all().order_by('name')
    serializer_class = LLMProviderSerializer
    permission_classes = [IsAuthenticated]
    
    def list(self, request, *args, **kwargs):
        """获取LLM提供商列表"""
        # 简单返回数据库中的提供商
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def models(self, request, pk=None):
        """获取指定提供商的所有模型"""
        provider = self.get_object()
        models = LLMModel.objects.filter(provider=provider)
        serializer = LLMModelSerializer(models, many=True)
        return Response(serializer.data)

class LLMModelViewSet(viewsets.ModelViewSet):
    """LLM模型视图集"""
    queryset = LLMModel.objects.all().order_by('provider__name', 'model_name')
    serializer_class = LLMModelSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 根据提供商过滤
        provider_id = self.request.query_params.get('provider', None)
        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)
            
        # 只返回活跃的模型
        active_only = self.request.query_params.get('active_only', False)
        if active_only and active_only.lower() in ['true', '1', 'yes']:
            queryset = queryset.filter(is_active=True)
            
        return queryset
    
    def perform_create(self, serializer):
        # 如果设置了is_active为True，将其他模型设为非活跃
        if serializer.validated_data.get('is_active', False):
            LLMModel.objects.filter(is_active=True).update(is_active=False)
        serializer.save()
    
    def perform_update(self, serializer):
        # 如果设置了is_active为True，将其他模型设为非活跃
        if serializer.validated_data.get('is_active', False):
            LLMModel.objects.filter(is_active=True).update(is_active=False)
        serializer.save()
        
        # 如果模型被设置为活跃状态，更新环境变量和设置
        if serializer.instance.is_active:
            try:
                self._update_active_model_settings(serializer.instance)
            except Exception as e:
                logger.error(f"更新环境变量设置时出错: {str(e)}", exc_info=True)
                # 即使更新环境变量失败，也不影响模型的保存操作
        
    def update(self, request, *args, **kwargs):
        """处理模型更新，支持部分更新字段"""
        instance = self.get_object()
        
        # 始终使用部分更新，如果API密钥不存在则不会验证这个字段
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        
        # 验证序列化器
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """激活指定的模型"""
        try:
            model = self.get_object()
            
            # 将其他模型设为非活跃
            LLMModel.objects.filter(is_active=True).update(is_active=False)
            
            # 激活当前模型
            model.is_active = True
            model.save()
            
            # 更新环境变量和设置，但不让错误影响API响应
            try:
                self._update_active_model_settings(model)
            except Exception as e:
                logger.error(f"激活模型时更新环境变量失败: {str(e)}", exc_info=True)
                # 返回成功但带警告
                return Response({
                    **self.get_serializer(model).data,
                    "warning": "模型已激活，但环境变量更新失败"
                })
            
            serializer = self.get_serializer(model)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"激活模型失败: {str(e)}", exc_info=True)
            return Response(
                {"error": f"激活模型失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _update_active_model_settings(self, model):
        """更新活跃模型的设置到环境变量和.env文件"""
        try:
            # 使用模型名称作为LLM模型名称
            model_name = model.model_name
            
            # 更新环境变量和.env文件
            env_file = os.path.join(settings.BASE_DIR.parent, '.env')
            
            # 读取现有.env文件
            if os.path.exists(env_file):
                try:
                    with open(env_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                except Exception as e:
                    logger.warning(f"无法读取.env文件: {str(e)}")
                    lines = []
            else:
                lines = []
            
            # 更新LLM配置行
            updated_lines = []
            updated_keys = set()
            
            # 配置键值对
            updates = {
                'LLM_MODEL_NAME': model_name,
                'LLM_BASE_URL': model.base_url,
                'LLM_API_KEY': model.api_key
            }
            
            # 更新现有行
            for line in lines:
                line_stripped = line.strip()
                if not line_stripped or line_stripped.startswith('#'):
                    updated_lines.append(line.rstrip('\n'))
                    continue
                    
                if '=' in line_stripped:
                    try:
                        key, value = line_stripped.split('=', 1)
                        key = key.strip()
                        
                        if key in updates:
                            updated_lines.append(f"{key}={updates[key]}")
                            updated_keys.add(key)
                        else:
                            updated_lines.append(line_stripped)
                    except Exception:
                        # 处理格式不正确的行
                        updated_lines.append(line_stripped)
                else:
                    updated_lines.append(line_stripped)
            
            # 添加缺失的配置
            for key, value in updates.items():
                if key not in updated_keys:
                    updated_lines.append(f"{key}={value}")
            
            # 写回.env文件
            try:
                with open(env_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(updated_lines) + '\n')
            except Exception as e:
                logger.error(f"无法写入.env文件: {str(e)}")
                # 继续执行，至少更新内存中的设置
            
            # 更新运行时环境变量
            os.environ['LLM_MODEL_NAME'] = model_name
            os.environ['LLM_BASE_URL'] = model.base_url
            os.environ['LLM_API_KEY'] = model.api_key
            
            # 同步到Django设置
            settings.LLM_MODEL_NAME = model_name
            settings.LLM_BASE_URL = model.base_url
            settings.LLM_API_KEY = model.api_key
            
            # 更新前端.env文件
            try:
                # 导入并运行更新脚本
                sys.path.append(str(settings.BASE_DIR.parent))
                from update_config import update_frontend_env
                update_frontend_env()
            except Exception as e:
                logger.error(f"更新前端环境变量时出错: {str(e)}")
                # 不中断操作
                
            logger.info(f"成功更新LLM设置: 模型={model_name}, API端点={model.base_url}")
            return True
                
        except Exception as e:
            logger.error(f"更新活跃模型设置时出错: {str(e)}", exc_info=True)
            # 不影响模型的保存操作
            return False

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """测试LLM模型连接是否正常
        
        发送一个简单的请求到模型API端点，检查响应是否正常
        """
        try:
            model = self.get_object()
            
            # 构建测试消息
            test_messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, this is a connection test. Please respond with 'Connection successful'."}
            ]
            
            # 构建请求数据
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {model.api_key}"
            }
            
            payload = {
                "model": model.model_name,
                "messages": test_messages,
                "max_tokens": 20,
                "temperature": 0.7
            }
            
            import requests
            import json
            
            # 发送请求
            logger.info(f"测试连接 {model.provider.name}/{model.model_name} 到 {model.base_url}")
            start_time = datetime.now()
            
            # 设置超时为10秒
            response = requests.post(
                model.base_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=10
            )
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            # 检查响应
            if response.status_code >= 200 and response.status_code < 300:
                response_data = response.json()
                
                # 提取模型响应
                model_response = ""
                try:
                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        if "message" in response_data["choices"][0]:
                            model_response = response_data["choices"][0]["message"].get("content", "")
                        elif "text" in response_data["choices"][0]:
                            model_response = response_data["choices"][0]["text"]
                except Exception as e:
                    logger.warning(f"解析模型响应时出错: {str(e)}")
                
                return Response({
                    "success": True,
                    "message": "连接成功",
                    "status_code": response.status_code,
                    "response_time": response_time,
                    "model_response": model_response,
                    "response_data": response_data
                })
            else:
                error_message = "连接失败"
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        if isinstance(error_data["error"], dict) and "message" in error_data["error"]:
                            error_message = error_data["error"]["message"]
                        elif isinstance(error_data["error"], str):
                            error_message = error_data["error"]
                except Exception:
                    try:
                        error_message = response.text
                    except:
                        pass
                
                return Response({
                    "success": False,
                    "message": error_message,
                    "status_code": response.status_code,
                    "response_time": response_time
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except requests.Timeout:
            logger.error(f"连接测试超时: {model.provider.name}/{model.model_name}")
            return Response({
                "success": False,
                "message": "连接超时，请检查API端点是否正确",
                "status_code": None,
                "response_time": 10  # 超时时间
            }, status=status.HTTP_408_REQUEST_TIMEOUT)
            
        except requests.ConnectionError:
            logger.error(f"连接错误: {model.provider.name}/{model.model_name}")
            return Response({
                "success": False,
                "message": "连接错误，无法连接到API端点",
                "status_code": None,
                "response_time": None
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
        except Exception as e:
            logger.error(f"测试连接时出错: {str(e)}", exc_info=True)
            return Response({
                "success": False,
                "message": f"测试连接时出错: {str(e)}",
                "status_code": None,
                "response_time": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_llm_config(request):
    """获取当前LLM配置"""
    # 尝试从数据库获取活跃模型配置
    active_model = LLMModel.objects.filter(is_active=True).first()
    
    if active_model:
        config = {
            'provider': {
                'id': active_model.provider.id,
                'name': active_model.provider.name
            },
            'model': {
                'id': active_model.id,
                'name': active_model.model_name,  # 使用model_name替代name
                'model_id': active_model.model_name,  # 兼容前端，使用model_name替代model_id
                'base_url': active_model.base_url,
                'api_key': active_model.api_key[:10] + '*****',  # 隐藏API密钥的大部分内容
            }
        }
    else:
        # 如果没有活跃模型，使用环境变量配置
        config = {
            'model_name': settings.LLM_MODEL_NAME,
            'base_url': settings.LLM_BASE_URL,
            'api_key': settings.LLM_API_KEY[:10] + '*****',  # 隐藏API密钥的大部分内容
        }
    
    return Response(config)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_llm_config(request):
    """更新LLM配置"""
    # 获取请求数据
    model_name = request.data.get('model_name')
    base_url = request.data.get('base_url')
    api_key = request.data.get('api_key')

    # 验证必要的字段
    if not all([model_name, base_url, api_key]):
        return Response(
            {"error": "缺少必要字段：model_name, base_url, api_key"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # 更新环境变量和.env文件
    env_file = os.path.join(settings.BASE_DIR.parent, '.env')
    
    # 读取现有.env文件
    if os.path.exists(env_file):
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    else:
        lines = []
    
    # 更新LLM配置行
    updated_lines = []
    updated_keys = set()
    
    # 配置键值对
    updates = {
        'LLM_MODEL_NAME': model_name,
        'LLM_BASE_URL': base_url,
        'LLM_API_KEY': api_key
    }
    
    # 更新现有行
    for line in lines:
        line_stripped = line.strip()
        if not line_stripped or line_stripped.startswith('#'):
            updated_lines.append(line.rstrip('\n'))
            continue
            
        if '=' in line_stripped:
            key, value = line_stripped.split('=', 1)
            key = key.strip()
            
            if key in updates:
                updated_lines.append(f"{key}={updates[key]}")
                updated_keys.add(key)
            else:
                updated_lines.append(line_stripped)
        else:
            updated_lines.append(line_stripped)
    
    # 添加缺失的配置
    for key, value in updates.items():
        if key not in updated_keys:
            updated_lines.append(f"{key}={value}")
    
    # 写回.env文件
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(updated_lines) + '\n')
    
    # 更新运行时环境变量
    os.environ['LLM_MODEL_NAME'] = model_name
    os.environ['LLM_BASE_URL'] = base_url
    os.environ['LLM_API_KEY'] = api_key
    
    # 同步到Django设置
    settings.LLM_MODEL_NAME = model_name
    settings.LLM_BASE_URL = base_url
    settings.LLM_API_KEY = api_key
    
    # 更新前端.env文件
    try:
        # 导入并运行更新脚本
        sys.path.append(str(settings.BASE_DIR.parent))
        from update_config import update_frontend_env
        update_frontend_env()
    except Exception as e:
        print(f"更新前端环境变量时出错: {str(e)}")
    
    return Response({
        "message": "LLM配置已更新",
        "model_name": model_name,
        "base_url": base_url,
        "api_key": api_key[:10] + '*****'  # 隐藏API密钥的大部分内容
    })

@api_view(['GET'])
def check_analysis_exists(request, match_id):
    """检查指定比赛是否存在分析报告"""
    try:
        # 创建一个临时的LLMService实例来处理ID映射
        llm_service = LLMService()
        
        # 使用_resolve_match_id方法获取内部match_id
        internal_match_id = llm_service._resolve_match_id(match_id)
        
        # 检查是否存在分析报告
        exists = OddsAnalysis.objects.filter(match_id=internal_match_id).exists()
        
        return Response({'exists': exists})
    except Exception as e:
        logger.error(f"检查分析报告存在性时出错: {str(e)}", exc_info=True)
        return Response(
            {'error': f'检查分析报告时出错: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
def get_match_analysis(request, match_id):
    """获取指定比赛的最新分析报告，先尝试原始ID，再尝试内部ID"""
    try:
        # 创建一个临时的LLMService实例来处理ID映射
        llm_service = LLMService()
        
        # 首先直接使用原始ID查询
        analysis = OddsAnalysis.objects.filter(match_id=match_id).order_by('-created_at').first()
        
        # 如果使用原始ID找不到，再尝试使用内部ID映射
        if not analysis:
            logger.info(f"使用原始ID {match_id} 未找到分析报告，尝试查找内部ID映射")
            internal_match_id = llm_service._resolve_match_id(match_id)
            logger.info(f"映射后的内部比赛ID: {internal_match_id}")
            
            if internal_match_id != match_id:
                analysis = OddsAnalysis.objects.filter(match_id=internal_match_id).order_by('-created_at').first()
        
        # 还是找不到，返回404
        if not analysis:
            # 在日志中记录所有可能的match_id值，帮助调试
            available_ids = list(OddsAnalysis.objects.values_list('match_id', flat=True).distinct())
            logger.warning(f"未找到分析报告，尝试查询的ID: {match_id}")
            logger.warning(f"数据库中可用的match_id值: {available_ids}")
            
            return Response(
                {'error': f'未找到ID为 {match_id} 的比赛分析报告'},
                status=status.HTTP_404_NOT_FOUND
            )
            
        # 解析分析文本
        analysis_text = analysis.analysis_text
        match_info = ""
        
        # 尝试从prompt中提取比赛信息
        try:
            if "比赛信息:" in analysis.prompt_text:
                match_info = analysis.prompt_text.split('赔率数据:')[0].replace('比赛信息:', '').strip()
        except Exception as e:
            logger.error(f"提取比赛信息时出错: {str(e)}")
            match_info = "无法提取比赛信息"
        
        # 从分析文本中提取使用的知识
        used_knowledge = analysis.used_knowledge or []
        
        return Response({
            'id': analysis.id,
            'match_id': analysis.match_id,
            'original_match_id': match_id,
            'analysis': analysis_text,
            'match_info': match_info,
            'used_knowledge': used_knowledge,
            'created_at': analysis.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        logger.error(f"获取分析报告时出错: {str(e)}", exc_info=True)
        return Response(
            {'error': f'获取分析报告时出错: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST', 'OPTIONS'])
def create_analysis_session(request):
    """创建分析会话，不验证比赛是否存在于Match表中"""
    # 处理OPTIONS请求
    if request.method == 'OPTIONS':
        return cors_options_response(request)
        
    match_id = request.data.get('match_id')
    
    if not match_id:
        return Response(
            {'error': '缺少必要参数: match_id'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        # 生成一个会话ID
        session_id = str(uuid.uuid4())
        
        # 记录会话创建信息
        logger.info(f"为比赛ID {match_id} 创建分析会话: {session_id}")
        
        # 添加CORS头
        response = Response({
            'session_id': session_id,
            'match_id': match_id,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        
        return response
        
    except Exception as e:
        logger.error(f"创建分析会话时出错: {str(e)}", exc_info=True)
        return Response(
            {'error': f'创建分析会话时出错: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def cors_options_response(request):
    """处理OPTIONS请求的辅助函数，返回正确的CORS头"""
    response = Response()
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
    response['Access-Control-Max-Age'] = '86400'  # 24小时
    response['Access-Control-Allow-Credentials'] = 'true'
    return response

async def stream_match_analysis(request, match_id):
    """流式分析比赛赔率 (异步Django视图)"""
    logger.info(f"收到流式分析请求 (异步视图)")
    logger.info(f"Request headers: {dict(request.headers)}")
    logger.info(f"Request Accept header: {request.headers.get('Accept', 'Not provided')}")

    # 处理OPTIONS请求 (保持不变)
    if request.method == 'OPTIONS':
        response = HttpResponse()
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, Accept"
        response["Access-Control-Max-Age"] = "86400"
        return response

    session_id = request.GET.get('session_id', str(uuid.uuid4())) # 从GET参数获取
    logger.info(f"分析参数: match_id={match_id}, session_id={session_id}")

    # --- 新增：从 GET 参数解析 extra_data ---
    extra_data = {}
    allowed_extra_keys = ['generation_type', 'base_report_id', 'report_number', 'next_version', 'continue_analysis', 'regenerate']
    for key in request.GET:
        if key in allowed_extra_keys:
            # 尝试将 next_version, report_number, base_report_id 转为整数
            if key in ['next_version', 'report_number', 'base_report_id']:
                try:
                    extra_data[key] = int(request.GET[key])
                except (ValueError, TypeError):
                    logger.warning(f"无法将查询参数 '{key}' 的值 '{request.GET[key]}' 转换为整数，将忽略此参数")
            # 将布尔值字符串转为布尔值
            elif key in ['continue_analysis', 'regenerate']:
                 extra_data[key] = request.GET[key].lower() == 'true'
            else:
                extra_data[key] = request.GET[key]
    logger.info(f"从GET参数解析得到的 extra_data: {extra_data}")
    # --- extra_data 解析结束 ---

    # if not session_id: # session_id 现在是可选的，如果未提供则生成
    #     return JsonResponse({'error': '缺少必要参数: session_id'}, status=400)
    
    try:
        # 获取当前活跃模型 (保持不变)
        model = await sync_to_async(LLMModel.objects.filter(is_active=True).first)()
        if model:
            logger.info(f"使用数据库中的活跃模型: {model.model_name}")
            model_name = model.model_name
            base_url = model.base_url
            api_key = model.api_key
        else:
            logger.warning(f"数据库中没有活跃模型，将尝试使用settings中的默认模型")
            model_name = getattr(settings, 'LLM_MODEL_NAME', None)
            base_url = getattr(settings, 'LLM_BASE_URL', None)
            api_key = getattr(settings, 'LLM_API_KEY', None)
            if not all([model_name, base_url, api_key]):
                 logger.error("无法从数据库或设置中获取完整的LLM模型配置")
                 return JsonResponse({'error': 'LLM模型配置不完整'}, status=500)
             
        logger.info(f"最终使用的LLM配置: 模型={model_name}, URL={base_url}")

        # 定义异步事件流生成器
        async def sse_event_stream(llm_service_instance):
            nonlocal match_id, session_id, extra_data # <-- 包含 extra_data
            event_id_counter = 0
            try:
                logger.info(f"SSE事件流生成器开始 (会话: {session_id})")
                # --- 修改：传递解析到的 extra_data --- 
                async for chunk_dict in llm_service_instance.analyze_match_odds_stream(match_id, extra_data):
                    event_id_counter += 1
                    event_type = chunk_dict.get("event", "message")
                    event_data = chunk_dict.get("data", "{}")
                    sse_formatted_chunk = f"id: {event_id_counter}\nevent: {event_type}\ndata: {event_data}\n\n"
                    logger.debug(f"Yielding SSE chunk ({session_id}): {sse_formatted_chunk[:100]}...")
                    yield sse_formatted_chunk.encode('utf-8')
                    await asyncio.sleep(0.01)
            except asyncio.CancelledError:
                logger.warning(f"客户端 {session_id} 在 sse_event_stream 中断开了连接.")
            except Exception as e:
                logger.exception(f"生成 SSE 事件流时出错 ({session_id})")
                event_id_counter += 1
                error_msg = json.dumps({
                    'error': f"生成分析时出错: {type(e).__name__}",
                    'step': 'sse_event_stream',
                    'done': True
                })
                try:
                    # 发送错误事件
                    yield f"id: {event_id_counter}\nevent: error\ndata: {error_msg}\n\n".encode('utf-8')
                    await asyncio.sleep(0.01)
                except Exception as yield_err:
                    logger.error(f"在 yield 错误信息时再次出错 (sse_event_stream): {yield_err}")
            finally:
                logger.info(f"SSE 事件流生成器 sse_event_stream ({session_id}) 结束.")

        # 创建LLMService实例
        llm_service = LLMService(llm_model=model_name, base_url=base_url, api_key=api_key, request=request)

        # 创建流式响应
        logger.info("创建异步 SSE 流式响应...")
        response = StreamingHttpResponse(
            sse_event_stream(llm_service),
            content_type='text/event-stream'
        )
        # ... (设置响应头不变) ...
        return response

    except Exception as e:
        logger.error(f"准备异步流式分析时出错: {str(e)}", exc_info=True)
        return JsonResponse({'error': f'准备流式分析时出错: {str(e)}'}, status=500)

@api_view(['POST'])
def control_analysis(request):
    """控制分析过程"""
    match_id = request.data.get('match_id')
    action = request.data.get('action')
    
    if not match_id or not action:
        return Response(
            {'error': '缺少必要参数: match_id, action'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    if action not in ['pause', 'resume', 'stop']:
        return Response(
            {'error': f'不支持的操作: {action}'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        # 这里实现分析控制逻辑
        # 实际应用中需要一个分析任务管理器来跟踪和控制任务
        actions_map = {"pause": "暂停", "resume": "恢复", "stop": "停止"}
        action_text = actions_map.get(action, action)
        
        return Response({
            'success': True,
            'message': f'成功{action_text}分析',
            'match_id': match_id,
            'action': action
        })
    except Exception as e:
        logger.error(f"控制分析过程时出错: {str(e)}", exc_info=True)
        return Response(
            {'error': f'控制分析过程时出错: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET', 'OPTIONS'])
def get_active_model_info(request):
    """获取当前活跃的AI模型信息，允许未登录用户访问"""
    # 处理OPTIONS请求
    if request.method == 'OPTIONS':
        return cors_options_response(request)
        
    try:
        # 获取活跃模型
        model = LLMModel.objects.filter(is_active=True).first()
        
        if model:
            logger.info(f"返回活跃模型信息: {model.model_name}")
            return Response({
                'model_id': model.id,
                'model_name': model.model_name,
                'provider_name': model.provider.name if model.provider else "未知提供商",
                'is_active': model.is_active,
                'base_url': model.base_url
            })
        else:
            # 如果没有活跃模型，返回默认配置
            logger.info("没有找到活跃模型，返回默认配置")
            return Response({
                'model_name': settings.LLM_MODEL_NAME,
                'provider_name': '系统默认',
                'is_active': True,
                'base_url': settings.LLM_BASE_URL
            })
            
    except Exception as e:
        logger.error(f"获取活跃模型信息时出错: {str(e)}", exc_info=True)
        # 返回默认配置
        return Response({
            'model_name': settings.LLM_MODEL_NAME,
            'provider_name': '系统默认',
            'is_active': True,
            'base_url': settings.LLM_BASE_URL
        })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def deactivate_llm_model(request):
    """停用当前活跃的LLM模型"""
    try:
        # 将所有模型设为非活跃
        LLMModel.objects.filter(is_active=True).update(is_active=False)
        
        return Response({
            'success': True,
            'message': '所有模型已停用'
        })
    except Exception as e:
        logger.error(f"停用LLM模型时出错: {str(e)}", exc_info=True)
        return Response(
            {'error': f'停用模型时出错: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET', 'OPTIONS'])
def get_historical_analysis(request, original_match_id):
    """
    获取历史分析报告
    
    通过原始比赛ID(外部ID)查找分析报告，返回所有历史报告
    """
    # 处理OPTIONS请求
    if request.method == 'OPTIONS':
        return cors_options_response(request)
        
    try:
        logger.info(f"开始获取历史分析报告，原始比赛ID: {original_match_id}")
        
        # 创建一个临时的LLMService实例来处理ID映射
        llm_service = LLMService()
        
        # 首先直接使用原始ID查询所有记录
        analyses = OddsAnalysis.objects.filter(match_id=original_match_id).order_by('-created_at')
        
        # 如果使用原始ID找不到，再尝试使用内部ID映射
        if not analyses.exists():
            logger.info(f"使用原始ID未找到分析报告，尝试查找内部ID映射")
            # 使用LLMService的方法寻找内部ID映射
            internal_match_id = llm_service._resolve_match_id(original_match_id)
            logger.info(f"映射后的内部比赛ID: {internal_match_id}")
            
            if internal_match_id != original_match_id:
                analyses = OddsAnalysis.objects.filter(match_id=internal_match_id).order_by('-created_at')
        
        # 还是找不到，返回404
        if not analyses.exists():
            # 在日志中记录所有可能的match_id值，帮助调试
            available_ids = list(OddsAnalysis.objects.values_list('match_id', flat=True).distinct())
            logger.warning(f"未找到分析报告，尝试查询的ID: {original_match_id}")
            logger.warning(f"数据库中可用的match_id值: {available_ids}")
            
            return Response(
                {'error': f'未找到比赛分析报告，请先生成'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 强制使用本地时区处理时间
        from django.utils import timezone
        from datetime import datetime, timedelta
        import pytz
        
        # 获取中国时区信息
        china_tz = pytz.timezone('Asia/Shanghai')
        
        # 准备所有报告
        response_data = []
        
        for analysis in analyses:
            # 解析分析文本
            analysis_text = analysis.analysis_text
            match_info = ""
            
            # 尝试从prompt中提取比赛信息
            try:
                if "比赛信息:" in analysis.prompt_text:
                    match_info = analysis.prompt_text.split('赔率数据:')[0].replace('比赛信息:', '').strip()
            except Exception as e:
                logger.error(f"提取比赛信息时出错: {str(e)}")
                match_info = "无法提取比赛信息"
            
            # 从分析文本中提取使用的知识
            used_knowledge = analysis.used_knowledge or []
            
            # 获取原始时间对象并确保时区处理正确
            created_at = analysis.created_at
            if timezone.is_naive(created_at):
                # 如果是无时区时间，加上亚洲/上海时区
                created_at = timezone.make_aware(created_at, china_tz)
            else:
                # 如果已有时区信息，确保转换到亚洲/上海时区
                created_at = created_at.astimezone(china_tz)
                
            # 获取各种格式的时间字符串
            created_at_str = created_at.strftime('%Y-%m-%d %H:%M:%S')  # 基本格式
            created_at_raw = created_at.strftime('%Y-%m-%d %H:%M:%S.%f %z')  # 包含毫秒和时区
            created_at_iso = created_at.isoformat()  # ISO格式
            
            logger.info(f"报告ID {analysis.id} 的创建时间: {created_at_raw}")
            
            # 添加到结果列表，包含新字段
            response_data.append({
                'id': analysis.id,
                'match_id': analysis.match_id,
                'original_match_id': original_match_id,
                'analysis_text': analysis_text,
                'prompt_text': analysis.prompt_text,
                'match_info': match_info,
                'used_knowledge': used_knowledge,
                'llm_model': analysis.llm_model,
                'report_number': analysis.report_number,
                'version': analysis.version,
                'base_report_id': analysis.base_report_id if analysis.base_report else None,
                'created_at_original': created_at_iso,  # 完整的ISO格式，包含微秒和时区
                'created_at_raw': created_at_raw,       # 原始格式字符串
                'created_at': created_at_str,            # 保持简单格式便于显示
                'generation_type': analysis.generation_type,
            })
        
        logger.info(f"成功获取历史分析报告: 共{len(response_data)}条记录")
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"获取历史分析报告时出错: {str(e)}", exc_info=True)
        return Response(
            {'error': f'获取历史分析报告时出错: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['OPTIONS'])
def handle_options_request(request):
    """处理所有OPTIONS请求的通用处理函数"""
    response = Response()
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
    response['Access-Control-Max-Age'] = '86400'  # 24小时
    response['Access-Control-Allow-Credentials'] = 'true'
    return response