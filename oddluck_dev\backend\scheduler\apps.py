from django.apps import AppConfig
import os

class SchedulerConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'scheduler'

    def ready(self):
        # 确保只在主进程中启动调度器，防止 runserver 重载时启动多个实例
        # 注意：这种检查方式在某些部署环境（如多 worker 的 gunicorn）下可能不完全可靠
        # 更健壮的方式可能需要结合锁机制或确保只有一个 worker 启动调度器
        run_main = os.environ.get('RUN_MAIN', None) == 'true'
        if run_main or not os.environ.get('RUN_MAIN'): # 本地 runserver 或非 runserver 环境下尝试启动
            from . import jobs # 导入包含任务函数的模块
            from apscheduler.schedulers.background import BackgroundScheduler
            from django_apscheduler.jobstores import DjangoJobStore

            scheduler = BackgroundScheduler(timezone=settings.TIME_ZONE) # 使用 Django 的时区设置
            scheduler.add_jobstore(DjangoJobStore(), "default")

            # 添加定时任务，例如每 10 分钟运行一次检查
            scheduler.add_job(
                jobs.check_upcoming_matches_and_scrape,
                trigger='interval',
                minutes=10, # 每 10 分钟执行一次
                id='check_upcoming_matches', # 任务的唯一 ID
                max_instances=1,
                replace_existing=True,
            )
            logger.info("Added job 'check_upcoming_matches'.")

            try:
                logger.info("Starting scheduler...")
                scheduler.start()
            except KeyboardInterrupt:
                logger.info("Stopping scheduler...")
                scheduler.shutdown()
            except Exception as e:
                logger.error(f"Error starting scheduler: {e}")

# 在文件顶部添加必要的导入
from django.conf import settings
import logging
logger = logging.getLogger(__name__)
