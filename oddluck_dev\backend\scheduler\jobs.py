import requests
from datetime import timedelta
from django.utils import timezone
from django.conf import settings
from api.models import Match # 假设你的 Match 模型在 api app 中
import logging

logger = logging.getLogger(__name__)

# scrapyd 的 API 地址，最好配置在 settings.py 中
SCRAPYD_URL = getattr(settings, 'SCRAPYD_URL', 'http://localhost:6800/schedule.json') 
SCRAPY_PROJECT = 'football_spider' # 你的 Scrapy 项目名

def trigger_scrape(match_id):
    """调用 scrapyd API 触发单个比赛的爬取"""
    try:
        # 首先查询这个match_id对应的联赛ID
        from api.models import Match
        from django.db.models import ObjectDoesNotExist
        
        league_id = None  # 默认为None
        try:
            match = Match.objects.get(match_id=match_id)
            # 获取联赛ID（通过league外键的league_id字段）
            if match.league:
                league_id = match.league.league_id
                logger.info(f"获取到比赛 {match_id} 对应的联赛ID: {league_id}")
            else:
                logger.warning(f"比赛 {match_id} 没有关联联赛信息")
        except Match.DoesNotExist:
            logger.error(f"未找到比赛ID {match_id} 对应的数据库记录")
        except ObjectDoesNotExist as e:
            logger.error(f"获取比赛 {match_id} 联赛信息时出错: {str(e)}")
        except Exception as e:
            logger.error(f"查询比赛 {match_id} 信息时发生未知错误: {str(e)}")
        
        # 构建请求参数
        params = {
            'project': SCRAPY_PROJECT,
            'spider': 'match',  # 假设你的比赛爬虫叫 'match'
            'target_match_id': str(match_id),  # 传递比赛ID作为参数
        }
        
        # 如果找到了联赛ID，则添加到请求参数中
        if league_id:
            params['league'] = str(league_id)
            logger.info(f"将使用联赛ID {league_id} 爬取比赛 {match_id}")
        
        response = requests.post(SCRAPYD_URL, data=params)
        response.raise_for_status()  # 如果请求失败则抛出异常
        logger.info(f"成功触发比赛 {match_id} 的爬取任务: {response.json()}")
        return True
    except requests.exceptions.RequestException as e:
        logger.error(f"触发比赛 {match_id} 爬取失败: {e}")
        return False

def check_upcoming_matches_and_scrape():
    """定时检查即将开始的比赛并触发爬取"""
    now = timezone.now()
    logger.info(f"[{now}] 开始检查需要触发爬取的比赛...")

    # 定义时间窗口 (例如，检查未来 1 天 + 1 小时内的比赛，避免错过边缘情况)
    check_window_start = now
    check_window_end = now + timedelta(days=1, hours=1)

    # 查询即将开始且比赛时间不为空的比赛
    upcoming_matches = Match.objects.filter(
        match_time__isnull=False,
        match_time__gte=check_window_start,
        match_time__lte=check_window_end
    ).exclude(match_id__isnull=True) # 确保 match_id 存在

    triggered_count = 0
    for match in upcoming_matches:
        time_until_match = match.match_time - now
        match_updated = False

        # 检查各个时间点
        if timedelta(hours=23, minutes=55) < time_until_match <= timedelta(days=1, minutes=5) and not match.scraped_1d:
            logger.info(f"比赛 {match.match_id} 即将开始 (1天内)，尝试触发爬取...")
            if trigger_scrape(match.match_id):
                match.scraped_1d = True
                match_updated = True
                triggered_count += 1

        elif timedelta(hours=5, minutes=55) < time_until_match <= timedelta(hours=6, minutes=5) and not match.scraped_6h:
            logger.info(f"比赛 {match.match_id} 即将开始 (6小时内)，尝试触发爬取...")
            if trigger_scrape(match.match_id):
                match.scraped_6h = True
                match_updated = True
                triggered_count += 1
            
        elif timedelta(hours=1, minutes=55) < time_until_match <= timedelta(hours=2, minutes=5) and not match.scraped_2h:
            logger.info(f"比赛 {match.match_id} 即将开始 (2小时内)，尝试触发爬取...")
            if trigger_scrape(match.match_id):
                match.scraped_2h = True
                match_updated = True
                triggered_count += 1

        elif timedelta(minutes=14, seconds=30) < time_until_match <= timedelta(minutes=15, seconds=30) and not match.scraped_15m:
            logger.info(f"比赛 {match.match_id} 即将开始 (15分钟内)，尝试触发爬取...")
            if trigger_scrape(match.match_id):
                match.scraped_15m = True
                match_updated = True
                triggered_count += 1

        # 如果状态有更新，保存 Match 对象 (注意：这会调用 save())
        # 因为 managed=False，Django 不会真正执行 UPDATE SQL 来更新这些标记字段。
        # 你需要修改 trigger_scrape 成功后，直接通过 SQL 更新数据库标记位，
        # 或者调整 Match 模型的 save() 方法，或者暂时设 managed=True 来更新。
        # 这里为了演示逻辑，暂时保留 Django ORM 的方式。
        # **推荐方式：在 trigger_scrape 成功后，使用 psycopg2 或 SQLAlchemy 直接执行 SQL 更新标记位**
        # if match_updated:
        #     try:
        #         # match.save(update_fields=['scraped_1d', 'scraped_6h', 'scraped_2h', 'scraped_15m']) 
        #         # 上面的 save 可能无效，需要直接执行 SQL
        #         logger.info(f"标记比赛 {match.match_id} 的爬取状态已（尝试）更新。") 
        #     except Exception as e:
        #         logger.error(f"更新比赛 {match.match_id} 标记失败: {e}")
            
        # --- 替代方案：直接执行 SQL 更新 ---
        if match_updated:
            from django.db import connection
            update_fields = []
            if match.scraped_1d: update_fields.append("scraped_1d = TRUE")
            if match.scraped_6h: update_fields.append("scraped_6h = TRUE")
            if match.scraped_2h: update_fields.append("scraped_2h = TRUE")
            if match.scraped_15m: update_fields.append("scraped_15m = TRUE")
                
            if update_fields:
                try:
                     with connection.cursor() as cursor:
                        sql = f"UPDATE matches SET {', '.join(update_fields)} WHERE match_id = %s"
                        cursor.execute(sql, [match.match_id])
                        logger.info(f"通过 SQL 更新了比赛 {match.match_id} 的爬取标记。")
                except Exception as e:
                        logger.error(f"通过 SQL 更新比赛 {match.match_id} 标记失败: {e}")


        logger.info(f"检查完成，本次触发了 {triggered_count} 个爬取任务。")

# 你还可以添加其他需要的定时任务，例如定期清理旧日志等