#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复Django数据库SSL连接问题
检测当前连接的是本地还是云数据库，并设置正确的SSL模式
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def fix_django_ssl_settings():
    """修复Django的SSL设置"""
    try:
        # 加载环境变量
        env_file = Path(__file__).resolve().parent / ".env"
        if env_file.exists():
            load_dotenv(env_file)
            logger.info(f"已加载环境变量: {env_file}")
        
        # 获取数据库主机和SSL模式
        db_host = os.getenv('DB_HOST', 'localhost')
        
        # 根据主机设置正确的SSL模式
        if db_host == 'localhost' or db_host == '127.0.0.1':
            os.environ['SSL_MODE'] = 'disable'
            logger.info(f"检测到本地数据库连接，设置SSL_MODE=disable")
        else:
            os.environ['SSL_MODE'] = 'require'
            logger.info(f"检测到云数据库连接，设置SSL_MODE=require")
        
        logger.info(f"数据库主机: {db_host}")
        logger.info(f"SSL模式: {os.environ.get('SSL_MODE')}")
        
        return True
    except Exception as e:
        logger.error(f"修复Django SSL设置时出错: {str(e)}")
        return False

def main():
    logger.info("开始修复Django SSL设置...")
    
    success = fix_django_ssl_settings()
    
    if success:
        logger.info("Django SSL设置修复成功!")
        logger.info("请确保在启动Django之前运行本脚本:")
        logger.info("python fix_django_ssl.py && python manage.py runserver")
    else:
        logger.error("Django SSL设置修复失败!")

if __name__ == "__main__":
    main() 