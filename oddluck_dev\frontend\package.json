{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "axios": "^1.8.3", "cropperjs": "^2.0.0", "date-fns": "^4.1.0", "floating-vue": "^5.2.2", "v-calendar": "^3.1.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/vue3": "^6.1.15", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "sass": "^1.86.0", "tailwindcss": "^4.0.14", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}