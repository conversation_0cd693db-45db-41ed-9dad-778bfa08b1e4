<template>
  <div class="match-table-container">
    <!-- 空状态 -->
    <div v-if="!selectedRegion && !isInternational && !loading" class="empty-state">
      <font-awesome-icon icon="map-marker-alt" class="empty-icon" />
      <h3>请在左侧选择国家以查看赛事</h3>
      <p>选择一个国家或地区来查看当日的比赛信息</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="skeleton-table">
        <div class="skeleton-header"></div>
        <div v-for="i in 5" :key="i" class="skeleton-row">
          <div class="skeleton-cell"></div>
          <div class="skeleton-cell"></div>
          <div class="skeleton-cell"></div>
          <div class="skeleton-cell"></div>
          <div class="skeleton-cell"></div>
        </div>
      </div>
    </div>

    <!-- 比赛表格 -->
    <div v-if="!loading && (selectedRegion || isInternational) && groupedMatches.length > 0" class="matches-content">
      <div class="content-header">
        <h3>{{ getRegionDisplayName() }} - {{ formatDate(selectedDate) }}</h3>
        <span class="match-count">共 {{ totalMatchCount }} 场比赛</span>
      </div>

      <div class="table-container">
        <table class="matches-table">
          <thead>
            <tr>
              <th class="league-col">{{ getTableHeader() }}</th>
              <th class="time-col">开赛时间</th>
              <th class="home-col">主队</th>
              <th class="score-col">比分</th>
              <th class="away-col">客队</th>
              <th class="odds-col">
                赔率更新
                <font-awesome-icon
                  icon="info-circle"
                  class="tooltip-icon"
                  title="橙色指示器表示赔率已更新"
                />
              </th>
              <th class="report-col">
                报告生成
                <font-awesome-icon
                  icon="info-circle"
                  class="tooltip-icon"
                  title="绿色对勾表示分析报告已生成"
                />
              </th>
            </tr>
          </thead>
          <tbody>
            <template v-for="group in groupedMatches" :key="group.id">
              <!-- 联赛/杯赛分组标题 -->
              <tr class="league-divider">
                <td colspan="7" class="league-title">
                  <img
                    v-if="group.icon"
                    :src="group.icon"
                    :alt="group.name"
                    class="league-icon"
                    @error="handleImageError"
                  />
                  <span>{{ group.name }}</span>
                </td>
              </tr>

              <!-- 比赛行 -->
              <tr
                v-for="match in group.matches"
                :key="match.id"
                class="match-row"
                @click="navigateToOdds(match.id)"
              >
                <td class="league-col">{{ group.name }}</td>
                <td class="time-col">{{ formatTime(match.start) }}</td>
                <td class="home-col">
                  <span class="team-name">{{ getTeamName(match.homeTeam) }}</span>
                </td>
                <td class="score-col">
                  <span v-if="match.homeScore !== undefined && match.awayScore !== undefined" class="score">
                    {{ match.homeScore }} - {{ match.awayScore }}
                  </span>
                  <span v-else class="vs">vs</span>
                </td>
                <td class="away-col">
                  <span class="team-name">{{ getTeamName(match.awayTeam) }}</span>
                </td>
                <td class="odds-col">
                  <div class="indicator-container">
                    <div
                      v-if="unacknowledgedUpdates.has(match.id)"
                      class="odds-indicator"
                      title="赔率已更新"
                    >
                      <font-awesome-icon icon="circle" />
                    </div>
                  </div>
                </td>
                <td class="report-col">
                  <div class="indicator-container">
                    <div
                      v-if="hasAnalysisReport(match.id)"
                      class="report-indicator"
                      title="分析报告已生成"
                    >
                      <font-awesome-icon icon="check-circle" />
                    </div>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 无比赛状态 -->
    <div v-if="!loading && (selectedRegion || isInternational) && groupedMatches.length === 0" class="no-matches">
      <font-awesome-icon icon="calendar-times" class="no-matches-icon" />
      <h3>{{ getRegionDisplayName() }} 今日无比赛</h3>
      <p>请选择其他日期或地区查看比赛信息</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { library } from '@fortawesome/fontawesome-svg-core'
import {
  faMapMarkerAlt,
  faInfoCircle,
  faCircle,
  faCheckCircle,
  faCalendarTimes
} from '@fortawesome/free-solid-svg-icons'
import { format } from 'date-fns'
import { useRouter } from 'vue-router'

// 注册图标
library.add(faMapMarkerAlt, faInfoCircle, faCircle, faCheckCircle, faCalendarTimes)

// 类型定义
interface Match {
  id: string
  title: string
  start: Date
  homeTeam: string
  awayTeam: string
  homeScore?: number
  awayScore?: number
  leagueId: string
  leagueName: string
  leagueIcon?: string
}

interface League {
  id: string
  name: string
  icon?: string
  matches: Match[]
}

interface Country {
  country_id: number
  country_name: string
  country_logo?: string
  continent_id: number
}

// Props
const props = defineProps<{
  selectedRegion: Country | 'international' | null
  selectedDate: Date
  groupedMatches: League[]
  loading: boolean
  unacknowledgedUpdates: Set<string>
  isInternational: boolean
}>()

// Router
const router = useRouter()

// 计算属性
const totalMatchCount = computed(() => {
  return props.groupedMatches.reduce((total, group) => total + group.matches.length, 0)
})

// 方法
const getRegionDisplayName = () => {
  if (props.isInternational) {
    return '国际赛事'
  }
  return props.selectedRegion && typeof props.selectedRegion === 'object'
    ? props.selectedRegion.country_name
    : '未知地区'
}

const getTableHeader = () => {
  return props.isInternational ? '杯赛' : '联赛'
}

const formatDate = (date: Date) => {
  return format(date, 'MM月dd日')
}

const formatTime = (date: Date) => {
  return format(date, 'HH:mm')
}

const getTeamName = (name: string) => {
  return name.length > 8 ? name.slice(0, 8) + '...' : name
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const hasAnalysisReport = (matchId: string) => {
  // TODO: 实现检查分析报告的逻辑
  return Math.random() > 0.7 // 临时随机逻辑
}

const navigateToOdds = (matchId: string) => {
  router.push(`/match/${matchId}`)
}
</script>
