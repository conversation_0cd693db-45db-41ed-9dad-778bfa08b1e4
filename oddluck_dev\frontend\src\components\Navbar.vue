<template>
  <nav class="navbar">
    <div class="navbar-container">
      <!-- Logo部分 -->
      <div class="navbar-logo">
        <router-link to="/">
          <img src="../assets/oddluck-logo.png" alt="OddLuck" />
        </router-link>
      </div>

      <!-- 导航链接 -->
      <div class="navbar-links">
        <!-- <router-link to="/" class="nav-link" active-class="active">首页</router-link>
        <router-link to="/matches" class="nav-link" active-class="active">比赛</router-link>
        <router-link to="/odds" class="nav-link" active-class="active">赔率</router-link>
        <router-link to="/analysis" class="nav-link" active-class="active">分析</router-link> -->
        <!-- <router-link to="/knowledge" class="nav-link" active-class="active">知识库</router-link> -->
        <!-- <router-link to="/spider-logs" class="nav-link" active-class="active">爬虫日志</router-link> -->
      </div>

      <!-- 右侧操作区域 -->
      <div class="navbar-actions">
        <!-- <div class="search-box">
          <input type="text" placeholder="搜索..." />
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="search-icon">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
        </div> -->
        
        <!-- 主题切换 -->
        <!-- <button class="theme-toggle" @click="toggleTheme" aria-label="切换主题">
          <svg v-if="isDarkMode" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" y1="1" x2="12" y2="3"></line>
            <line x1="12" y1="21" x2="12" y2="23"></line>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
            <line x1="1" y1="12" x2="3" y2="12"></line>
            <line x1="21" y1="12" x2="23" y2="12"></line>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
          </svg>
        </button> -->
        
        <!-- 登录/用户信息 -->
        <div class="user-section">
          <!-- 添加用户头像 - 当用户未登录时显示默认头像 -->
          <div v-if="!isLoggedIn" class="user-avatar default-avatar" title="未登录">
            <img src="../assets/default-avatar.svg" alt="默认头像" />
          </div>
          
          <!-- 当用户已登录时，显示用户自定义头像 -->
          <div v-else class="user-avatar-container" 
               @mouseenter="showUserMenu = true" 
               @mouseleave="startCloseTimer"
               @click="showUserMenu = !showUserMenu"
               :class="{ 'menu-open': showUserMenu }">
            <div class="user-avatar" :class="{ 'active': showUserMenu }">
              <img v-if="userAvatar" :src="userAvatar" alt="User" />
              <div v-else class="avatar-placeholder">
                {{ userInitials }}
              </div>
            </div>
            
            <!-- 添加下拉箭头指示器 -->
            <div class="dropdown-indicator">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotated': showUserMenu }">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </div>
            
            <!-- 用户下拉菜单 -->
            <transition name="dropdown">
              <div v-show="showUserMenu" 
                   class="user-dropdown" 
                   @mouseenter="cancelCloseTimer" 
                   @mouseleave="startCloseTimer"
                   @click.stop>
                <div class="dropdown-user-info">
                  <div class="dropdown-avatar">
                    <img v-if="userAvatar" :src="userAvatar" alt="User" />
                    <div v-else class="dropdown-avatar-placeholder">{{ userInitials }}</div>
                  </div>
                  <div class="dropdown-name">{{ userName }}</div>
                  <div class="dropdown-email">{{ userEmail }}</div>
                </div>
                <div class="dropdown-divider"></div>
                <router-link to="/llm-config" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 18h.01M8 21h8a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2z"></path>
                  </svg>
                  LLM
                </router-link>
                <router-link to="/knowledge" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                  </svg>
                  知识库
                </router-link>
                <router-link to="/profile" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  用户档案
                </router-link>
                <router-link to="/settings" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                  </svg>
                  设置
                </router-link>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item logout" @click.prevent="logout">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                  </svg>
                  退出登录
                </a>
              </div>
            </transition>
          </div>
          
          <!-- 登录图标 -->
          <router-link v-if="!isLoggedIn" to="/auth" class="login-icon" aria-label="登录">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
              <polyline points="10 17 15 12 10 7"></polyline>
              <line x1="15" y1="12" x2="3" y2="12"></line>
            </svg>
          </router-link>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import api from '../services/api';
import '../styles/navbar.css'; // 导入外部CSS文件

// 登录状态
const isLoggedIn = ref(!!localStorage.getItem('token'));
const router = useRouter();

// 用户信息
const userName = ref(localStorage.getItem('username') || '未登录');
const userEmail = ref(localStorage.getItem('email') || localStorage.getItem('username') + '@oddluck.com');
const userAvatar = ref(localStorage.getItem('avatar') || '');

// 用户菜单状态
const showUserMenu = ref(false);
const closeMenuTimer = ref<number | null>(null);

// 深色模式状态
const isDarkMode = ref(localStorage.getItem('theme') === 'dark');

// 计算用户名首字母（用于头像占位符）
const userInitials = computed(() => {
  if (!userName.value) return '';
  return userName.value.charAt(0).toUpperCase();
});

// 切换主题
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value;
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark-theme');
    localStorage.setItem('theme', 'dark');
  } else {
    document.documentElement.classList.remove('dark-theme');
    localStorage.setItem('theme', 'light');
  }
};

// 退出登录
const logout = async () => {
  try {
    // 清除本地存储中的所有用户相关信息
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('username');
    localStorage.removeItem('user_id');
    localStorage.removeItem('avatar');
    localStorage.removeItem('email');
    localStorage.removeItem('joinDate');
    
    isLoggedIn.value = false;
    showUserMenu.value = false;
    userName.value = '未登录';
    userEmail.value = '';
    userAvatar.value = '';
    
    // 清理菜单定时器
    if (closeMenuTimer.value) {
      clearTimeout(closeMenuTimer.value);
      closeMenuTimer.value = null;
    }
    
    // 通知其他组件用户已退出登录
    window.dispatchEvent(new CustomEvent('user-logout'));
    
    // 跳转到首页
    router.push('/auth');
  } catch (error) {
    console.error('退出登录时出错:', error);
  }
};

// 从服务器获取用户信息
const fetchUserProfile = async () => {
  if (!isLoggedIn.value) return;
  
  try {
    // 首先获取基本用户信息
    const userResponse = await api.get('/auth/user/');
    if (userResponse.data) {
      userName.value = userResponse.data.username;
      userEmail.value = userResponse.data.email;
    }
    
    // 然后获取用户档案信息
    const profileResponse = await api.get('/auth/profile/');
    if (profileResponse.data) {
      if (profileResponse.data.avatar) {
        userAvatar.value = profileResponse.data.avatar;
      }
      // 使用档案中的邮箱，如果存在的话
      if (profileResponse.data.email) {
        userEmail.value = profileResponse.data.email;
        // 更新localStorage中的邮箱
        localStorage.setItem('email', profileResponse.data.email);
      }
    }
  } catch (error) {
    console.error('获取用户档案失败:', error);
  }
};

// 监听用户资料更新事件
const handleProfileUpdate = (event: Event) => {
  const customEvent = event as CustomEvent;
  console.log('用户资料更新事件:', customEvent.detail);
  
  if (customEvent.detail) {
    if (customEvent.detail.username) {
      userName.value = customEvent.detail.username;
      // 更新localStorage中的用户名
      localStorage.setItem('username', customEvent.detail.username);
    }
    if (customEvent.detail.email) {
      userEmail.value = customEvent.detail.email;
      // 更新localStorage中的邮箱
      localStorage.setItem('email', customEvent.detail.email);
    }
    if (customEvent.detail.avatar) {
      userAvatar.value = customEvent.detail.avatar;
      // 更新localStorage中的头像
      localStorage.setItem('avatar', customEvent.detail.avatar);
    }
  }
};

// 处理登录成功事件
const handleLoginSuccess = (event: Event) => {
  const customEvent = event as CustomEvent;
  console.log('登录成功事件触发:', customEvent.detail);
  
  // 更新登录状态
  isLoggedIn.value = true;
  
  // 更新用户信息
  if (customEvent.detail) {
    userName.value = customEvent.detail.username || localStorage.getItem('username') || '用户';
    
    // 优先使用事件中的邮箱
    if (customEvent.detail.email) {
      userEmail.value = customEvent.detail.email;
      localStorage.setItem('email', customEvent.detail.email);
    } else {
      // 降级使用localStorage或拼接默认邮箱
      userEmail.value = localStorage.getItem('email') || (userName.value + '@oddluck.com');
    }
    
    // 更新头像
    if (customEvent.detail.avatar) {
      userAvatar.value = customEvent.detail.avatar;
    } else {
      userAvatar.value = localStorage.getItem('avatar') || '';
    }
    
    // 登录成功后，尝试立即获取最新用户资料
    setTimeout(() => {
      fetchUserProfile();
    }, 500);
  } else {
    // 如果事件没有详细信息，则从localStorage获取
    userName.value = localStorage.getItem('username') || '用户';
    userEmail.value = localStorage.getItem('email') || (userName.value + '@oddluck.com');
    userAvatar.value = localStorage.getItem('avatar') || '';
    
    // 同样尝试获取最新资料
    fetchUserProfile();
  }
};

// 延迟关闭菜单
const startCloseTimer = () => {
  closeMenuTimer.value = window.setTimeout(() => {
    showUserMenu.value = false;
  }, 600);
};

// 取消关闭菜单
const cancelCloseTimer = () => {
  if (closeMenuTimer.value) {
    clearTimeout(closeMenuTimer.value);
    closeMenuTimer.value = null;
  }
};

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && showUserMenu.value) {
    showUserMenu.value = false;
  }
};

// 添加处理全局点击的函数
const handleGlobalClick = (event: MouseEvent) => {
  // 如果菜单已打开，且点击的不是头像容器或其子元素
  if (showUserMenu.value) {
    const avatarContainer = document.querySelector('.user-avatar-container');
    if (avatarContainer && !avatarContainer.contains(event.target as Node)) {
      showUserMenu.value = false;
    }
  }
};

// 挂载事件监听
onMounted(() => {
  // 检查是否已登录
  const token = localStorage.getItem('token');
  if (token) {
    isLoggedIn.value = true;
    
    // 从localStorage获取基本信息
    userName.value = localStorage.getItem('username') || '用户';
    userEmail.value = localStorage.getItem('email') || localStorage.getItem('username') + '@oddluck.com';
    userAvatar.value = localStorage.getItem('avatar') || '';
    
    // 获取最新的用户档案
    fetchUserProfile();
  }
  
  // 监听登录成功事件
  window.addEventListener('login-success', handleLoginSuccess);
  
  // 监听用户资料更新事件
  window.addEventListener('user-profile-updated', handleProfileUpdate);
  
  // 监听键盘事件
  document.addEventListener('keydown', handleKeyDown);
  
  // 监听全局点击事件关闭菜单
  document.addEventListener('click', handleGlobalClick);
  
  // 设置主题
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark-theme');
  }
});

onUnmounted(() => {
  // 组件卸载时移除事件监听
  window.removeEventListener('login-success', handleLoginSuccess);
  window.removeEventListener('user-profile-updated', handleProfileUpdate);
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('click', handleGlobalClick);
  
  // 清理定时器
  if (closeMenuTimer.value) {
    clearTimeout(closeMenuTimer.value);
  }
});
</script> 