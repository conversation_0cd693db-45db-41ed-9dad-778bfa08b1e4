<template>
  <div class="analysis-container">
    <div class="analysis-header">
      <div class="title-action-group">
        <button 
          class="action-btn primary-btn small-btn" 
          @click="analyzeOdds" 
          :disabled="isAnalysisDisabled"
          @mousedown="createRippleEffect"
        >
          <span v-if="analyzing">正在剖析</span>
          <span v-else>{{ hasExistingReport ? '重新生成' : '深度报告' }}</span>
        </button>
        
        <!-- 继续生成按钮移到这里，只在有现有报告且不在分析中时显示 -->
        <button 
          v-if="hasExistingReport"
          class="action-btn continue-btn small-btn" 
          @click="continuousGenerate" 
          :disabled="isContinueDisabled"
          @mousedown="createRippleEffect"
        >
          继续生成
        </button>
        
        <!-- 将流式输出切换移至此处，紧跟继续生成按钮 -->
        <div class="stream-toggle-control" title="启用流式输出（实时显示分析过程）">
          <label class="switch stream-switch">
            <input
              type="checkbox"
              v-model="useStreaming"
              :disabled="analyzing"
            >
            <span class="slider round"></span>
          </label>
          <!-- 添加流式输出状态文字提示 -->
          <span class="stream-status">{{ useStreaming ? '流式输出: 开' : '流式输出: 关' }}</span>
        </div>
      </div>
      
      <div class="right-controls">
        <!-- LLM模型选择和状态控制 -->
        <div class="llm-controls">
          <select 
            class="llm-select" 
            v-model="selectedModelId"
            :disabled="llmModelStatus === 'activating' || isModelLoading"
            :title="getSelectedModelTitle()"
          >
            <option value="" disabled v-if="isModelLoading">加载模型中...</option>
            <option value="" disabled v-else-if="availableModels.length === 0">没有可用模型</option>
            <option 
              v-for="model in availableModels" 
              :key="model.id" 
              :value="model.id" 
              :title="`${model.provider_name}: ${model.model_name}`"
            >
              {{ formatModelOption(model) }}
            </option>
          </select>
          
          <div class="toggle-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="isModelActive"
                @change="toggleModelActive"
                :disabled="llmModelStatus === 'activating' || isModelLoading || availableModels.length === 0 || !selectedModelId"
              >
              <span class="slider round"></span>
            </label>
            
            <div class="status-indicator">
              <span 
                class="status-dot" 
                :class="{
                  'active': llmModelStatus === 'active',
                  'inactive': llmModelStatus === 'inactive',
                  'activating': llmModelStatus === 'activating',
                  'loading': isModelLoading
                }"
              ></span>
            </div>
          </div>
        </div>
        
        <!-- 控制按钮 -->
        <div v-if="analyzing" class="control-buttons">
          <button 
            class="action-btn secondary-btn" 
            @click="pauseAnalysis"
          >
            {{ isPaused ? '继续' : '暂停' }}
          </button>
          <button 
            class="action-btn danger-btn" 
            @click="stopAnalysis"
          >
            停止
          </button>
        </div>
      </div>
    </div>
    
    <!-- 修改报告历史选择器位置，将其移至右边 -->
    <div v-if="hasExistingReport && !analyzing && reportHistory.length > 0" class="history-selector-row">
      <div class="report-history-dropdown">
        <select v-model="selectedReportIndex" @change="switchReport" class="history-select">
          <option v-for="(report, index) in reportHistory" :key="index" :value="index">
            {{ formatReportDisplay(report, index) }}
          </option>
        </select>
      </div>
    </div>
    
    <!-- 分析中状态 -->
    <div v-if="analyzing" class="analyzing-state">
      <!-- 分析进度指示器 -->
      <div class="analysis-progress-container">
        <div class="progress-initializing">
          <div class="initializing-animation"></div>
          <p>正在解读机构意图...</p>
          <p class="wait-hint">可能需要30-360秒，最终耗时取决于赔率数据复杂度</p>
          <!-- 添加流式输出指示器 -->
          <div v-if="useStreaming" class="stream-indicator">
            <span class="stream-dot"></span>
            <span>流式输出模式：内容将实时显示</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="analysisError" class="error-state">
      <p>{{ analysisError }}</p>
      <button class="action-btn primary-btn" @click="analyzeOdds">重试</button>
    </div>
    
    <!-- 分析结果 -->
    <div v-else-if="analysisResult" class="analysis-result">
      <!-- 分析内容 -->
      <div class="analysis-content-section">
        <h3 class="section-title">深度报告</h3>
        <!-- Show initial loading text only when analyzing AND no content yet -->
        <div v-if="analyzing && (!analysisResult || !analysisResult.analysis)" class="initial-loading-text">
          正在接收分析内容...
        </div>
        <!-- 当正在分析时，提供一个空容器用于直接DOM操作 (或显示累积内容) -->
        <div v-if="analyzing" class="analysis-content flow-container" v-html="formattedAnalysis"></div>
        <!-- 非分析状态时使用标准v-html展示 -->
        <div v-if="!analyzing" class="analysis-content"
          v-html="formattedAnalysis"
          :key="refreshKey"></div>
        <!-- Show empty message only when NOT analyzing AND no valid analysisResult.analysis -->
        <div v-if="!analyzing && analysisResult && !analysisResult.analysis && !analysisError" class="empty-analysis-message">
          <p>分析内容为空，请尝试重新生成报告</p>
          <button class="action-btn primary-btn" @click="analyzeOdds">重新生成</button>
        </div>
      </div>
      
      <!-- 知识来源 -->
      <div v-if="analysisResult.used_knowledge && analysisResult.used_knowledge.length > 0" class="knowledge-section">
        <h3 class="section-title">参考知识</h3>
        <div class="knowledge-list">
          <div v-for="(source, index) in analysisResult.used_knowledge" :key="index" class="knowledge-item">
            <div class="knowledge-header">
              <span class="knowledge-title">{{ source.title }}</span>
              <span class="knowledge-similarity" v-if="source.similarity">(相似度: {{ (source.similarity * 100).toFixed(1) }}%)</span>
            </div>
            <div class="knowledge-preview" v-if="source.content">
              {{ source.content.substring(0, 150) }}{{ source.content.length > 150 ? '...' : '' }}
            </div>
          </div>
        </div>
        <div class="knowledge-usage-tip">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>
          <p>分析内容中引用的知识文档会被高亮显示，如"《欧赔核心思维》"</p>
        </div>
      </div>
    </div>
    
    <!-- 无分析状态 -->
    <div v-else-if="!analyzing && !analysisResult && !analysisError" class="empty-state">
      <div class="empty-guide">
        <h3 class="guide-title">使用向导</h3>
        <ol class="guide-steps">
          <li class="guide-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <strong>选择LLM</strong>
              <p>从下拉菜单中选择您已配置好的LLM</p>
            </div>
          </li>
          <li class="guide-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <strong>激活所选LLM</strong>
              <p>点击LLM右侧的开关按钮，将其切换为激活状态</p>
            </div>
          </li>
          <li class="guide-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <strong>开始深度剖析</strong>
              <p>点击顶部的"深度报告"按钮，LLM将开始分析本场比赛的所有赔率数据</p>
            </div>
          </li>
        </ol>
        <div class="guide-note">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>
          <p>您可以在"配置LLM"页面添加和管理更多语言模型</p>
        </div>
      </div>
    </div>

    <!-- 登录提示 -->
    <div v-if="authError" class="auth-error">
      <p>请先登录后再使用分析功能</p>
      <button @click="redirectToLogin" class="login-btn">前往登录</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref, onMounted, watch } from 'vue'
import { getAllLLMModels, activateLLMModel, getCurrentLLMConfig, deactivateLLMModel, getHistoricalAnalysis } from '../services/llmService'
import '../styles/odds-analysis.css' // 导入外部CSS文件

interface AnalysisResult {
  match_id?: string;
  analysis: string;
  match_info?: string;
  used_knowledge?: Array<{
    title: string;
    content?: string;
    source?: string;
    similarity?: number;
    category?: string;
    id?: number;
  }>;
  display_match_time?: string;
  is_cached?: boolean;
  created_at?: string;
  created_at_raw?: string;      // 原始格式时间字符串，包含毫秒和时区
  created_at_original?: string; // ISO格式时间字符串
}

interface LLMModel {
  id: number;
  model_name: string;
  provider_name: string;
  is_active: boolean;
  provider: number;
  base_url: string;
}

// 报告历史记录接口
interface Report {
  match_id: string;
  analysis_text: string;
  prompt_text: string;
  llm_model: string;
  created_at: string;
  created_at_raw?: string;      // 原始格式时间字符串，包含毫秒和时区
  created_at_original?: string; // ISO格式时间字符串
  id?: number;
  match_info?: string;
  used_knowledge?: Array<{
    title: string;
    source?: string;
  }>;
  base_report_id?: number;      // 基于哪个报告生成的
  report_number?: number;       // 报告编号
  version?: number;             // 版本号
  is_continuous?: boolean;      // 是否是继续生成的报告
  generation_type?: 'initial' | 'regenerate' | 'continue'; // 新增：后端需要返回
  displayLabel?: string; // 新增：前端计算后存储
}

const props = defineProps({
  matchId: {
    type: String,
    required: true
  },
  analyzing: {
    type: Boolean,
    default: false
  },
  analysisResult: {
    type: Object as () => AnalysisResult | null,
    default: null
  },
  analysisError: {
    type: String,
    default: ''
  },
  hasExistingReport: {
    type: Boolean,
    default: false
  },
  isPaused: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'analyze-odds', 
  'pause-analysis', 
  'stop-analysis',
  'update-analysis-result'
])

// 新增：控制是否使用流式输出
const useStreaming = ref(localStorage.getItem('useStreamingOutput') !== 'false') // 默认启用流式输出

// 监听流式输出设置变更，保存用户偏好
watch(useStreaming, (newValue) => {
  localStorage.setItem('useStreamingOutput', newValue.toString())
  console.log(`流式输出设置已更新: ${newValue ? '开启' : '关闭'}`)
})

// 报告历史状态管理
const reportHistory = ref<Report[]>([])
const selectedReportIndex = ref(0)
const currentReport = ref<Report | null>(null)
const reportGroups = ref<Map<number, Report[]>>(new Map()) // 按报告编号分组

// LLM模型状态
const availableModels = ref<LLMModel[]>([])
const selectedModelId = ref<number | null>(null)
const isModelLoading = ref(true)
const isModelActive = ref(false)
const authError = ref(false)
const llmModelStatus = ref<'active' | 'inactive' | 'activating'>('inactive')
const debugMode = ref(false)  // 添加调试模式开关
const refreshKey = ref(0) // 添加刷新键，用于强制重新渲染分析内容
// 添加一个计算属性来统一禁用逻辑
const isAnalysisDisabled = computed(() => {
  // 禁用条件：正在分析中 或 LLM未激活 或 没有选择模型 或 模型正在加载
  return props.analyzing || !isModelActive.value || !selectedModelId.value || isModelLoading.value;
});

// 继续生成按钮的禁用逻辑
const isContinueDisabled = computed(() => {
  // 禁用条件：已有报告但未激活LLM 或 正在分析中 或 没有选择模型 或 模型正在加载
  // 注意：即使有报告，如果LLM未激活也不能继续生成
  return !isModelActive.value || props.analyzing || !selectedModelId.value || isModelLoading.value;
});

// 监听分析结果变化，更新refreshKey
watch(() => props.analysisResult?.analysis, () => {
  refreshKey.value++;
}, { immediate: false })

// 格式化LLM选项显示
const formatModelOption = (model: LLMModel) => {
  // 直接使用完整的 model_name
  return `${model.provider_name}: ${model.model_name}`
}

// 获取选中模型的完整提示信息
const getSelectedModelTitle = () => {
  if (isModelLoading.value) return '正在加载模型...'
  if (!selectedModelId.value) return '请选择一个LLM模型'
  
  const model = availableModels.value.find(m => m.id === selectedModelId.value)
  if (!model) return '未知模型'
  
  return `${model.provider_name}: ${model.model_name}`
}

// 格式化日期函数，直接显示数据库原始格式，去掉毫秒部分
const formatReportDate = (dateString?: string) => {
  try {
    // 如果没有日期字符串，返回未知日期
    if (!dateString) return '未知日期';
    
    // 对于原始格式，直接提取日期和时间部分，去掉毫秒
    if (dateString.includes('.') && dateString.includes('+')) {
      // 原始格式：2025-04-02 23:53:13.764 +0800
      const parts = dateString.split(' ');
      if (parts.length >= 2) {
        const datePart = parts[0];
        const timePart = parts[1].split('.')[0]; // 移除毫秒
        return `${datePart} ${timePart}`;
      }
    }
    
    // 如果是普通格式，直接使用现有逻辑
    if (dateString.includes('-') && dateString.includes(':')) {
      // 检查是否有毫秒部分，如果有则去掉
      if (dateString.includes('.')) {
        const parts = dateString.split('.');
        return parts[0]; // 只返回毫秒点之前的部分
      }
      return dateString;
    }
    
    // 其他格式直接返回原始字符串
    return dateString;
  } catch (e) {
    console.error('日期格式化错误:', e);
    return dateString || '未知日期'; // 出错时返回原始日期字符串
  }
}

// 修改：格式化报告显示名称 - 现在主要依赖 report.displayLabel
const formatReportDisplay = (report: Report, index: number) => {
  // 优先使用预先计算好的 displayLabel
  if (report.displayLabel) {
    return report.displayLabel;
  }

  // --- Fallback logic if displayLabel wasn't calculated (e.g., backend missing generation_type) ---
  console.warn(`报告 ID ${report.id} 缺少 displayLabel，使用旧格式回退。`);
  try {
    const datetime = formatReportDate(report.created_at_raw || report.created_at);
    if (report.report_number) {
      const reportNum = report.report_number;
      const version = report.version || 1;
      return `报告${reportNum}-v${version}：${datetime}`; 
    } else {
      return `历史报告 ${index + 1}：${datetime}`;
    }
  } catch (e) {
    console.error('格式化报告显示名称（回退逻辑）错误:', e);
    return `历史报告 ${index + 1}：${formatReportDate(report.created_at_raw || report.created_at)}`;
  }
}

// 修改：加载报告历史记录 - 添加计算 displayLabel 的逻辑
const loadReportHistory = async () => {
  try {
    console.log('加载报告历史记录...')
    const data: Report[] = await getHistoricalAnalysis(props.matchId) // 假设返回 Report[]
    
    if (data && Array.isArray(data)) {
      console.log(`从API获取了 ${data.length} 条原始报告数据`);
      // 验证并记录内容缺失
      data.forEach((report, idx) => {
        if (!report.analysis_text) {
          console.warn(`警告: 报告 #${idx} (ID:${report.id}) 没有分析文本内容`);
        }
      });
      
      // 1. 按创建时间 *升序* 排序以方便计数
      const sortedReportsAsc = data.sort((a, b) => {
        // 优先使用 created_at_original 或 created_at_raw 进行精确排序
        const dateA = a.created_at_original || a.created_at_raw || a.created_at;
        const dateB = b.created_at_original || b.created_at_raw || b.created_at;
        try {
           return new Date(dateA).getTime() - new Date(dateB).getTime();
        } catch (e) {
           console.error("排序时日期解析错误:", e, dateA, dateB);
           return 0; // 保持原始顺序如果日期无效
        }
      });
      
      // 2. 初始化计数器
      let regenerateCounter = 1;
      let continueCounter = 1;
      let postMatchCounter = 1; // 新增：赛后复盘计数器
      
      // 3. 遍历并计算 displayLabel
      sortedReportsAsc.forEach(report => {
        const datetime = formatReportDate(report.created_at_raw || report.created_at);
        // 尝试获取 generation_type (假设后端会返回)
        const type = report.generation_type;
        
        if (type === 'initial') {
          report.displayLabel = `首次生成: ${datetime}`;
        } else if (type === 'regenerate') {
          report.displayLabel = `重新生成 ${regenerateCounter}: ${datetime}`;
          regenerateCounter++;
        } else if (type === 'continue') {
          report.displayLabel = `继续生成 ${continueCounter}: ${datetime}`;
          continueCounter++;
        } else if (type === 'post_match_replay') { // 新增处理 post_match_replay
          report.displayLabel = `赛后复盘 ${postMatchCounter}: ${datetime}`;
          postMatchCounter++;
        } else {
          // Fallback if generation_type is missing or unknown
          // 使用旧逻辑生成回退标签，并存储，这样 formatReportDisplay 也能用
          if (report.report_number) {
             const reportNum = report.report_number;
             const version = report.version || 1;
             report.displayLabel = `报告${reportNum}-v${version}：${datetime}`; 
          } else {
             // 需要一个基于索引的稳定回退，但这里在循环内部无法直接用最终反转后的索引
             // 暂时使用ID作为回退
             console.warn(`报告 ID ${report.id} 缺少 generation_type 和 report_number，使用 ID 回退标签`);
             report.displayLabel = `报告ID ${report.id}: ${datetime}`;
          }
        }
      });
      
      // 4. *反转* 数组，使最新的报告在前面
      const finalSortedReports = sortedReportsAsc.reverse();
      
      reportHistory.value = finalSortedReports;
      console.log(`处理并排序后 ${reportHistory.value.length} 条报告历史记录`);
      
      // 如果有历史记录，选择最新的一条 (索引 0)
      if (reportHistory.value.length > 0) {
        selectedReportIndex.value = 0;
        currentReport.value = reportHistory.value[0];
        console.log('默认选择的当前报告 (最新):', currentReport.value.id, 
                  'Label:', currentReport.value.displayLabel, 
                  '内容长度:', currentReport.value.analysis_text?.length || 0);
        // 首次加载历史后，触发一次更新，让父组件显示最新的报告内容
        // 确保 currentReport.value 有效再触发
        if (currentReport.value) {
           emitResultFromReport(currentReport.value);
        }
      } else {
          currentReport.value = null;
      }
    } else {
      console.warn('报告历史记录API返回非数组数据或为空:', data);
      reportHistory.value = [];
      currentReport.value = null;
    }
  } catch (e) {
    console.error('加载报告历史记录出错:', e);
    reportHistory.value = [];
    currentReport.value = null;
  }
}

// 新增：辅助函数，用于根据 Report 对象触发 update-analysis-result
const emitResultFromReport = (report: Report) => {
     if (!report) return;
     const result: AnalysisResult = {
        match_id: report.match_id,
        analysis: report.analysis_text || '未找到分析内容',
        match_info: report.match_info || (report.prompt_text && report.prompt_text.includes('比赛信息:') 
                  ? report.prompt_text.split('赔率数据:')[0].replace('比赛信息:', '').trim() 
                  : '无法提取比赛信息'),
        created_at: report.created_at_original || report.created_at, // 优先使用ISO格式
        created_at_raw: report.created_at_raw,
        used_knowledge: report.used_knowledge || [],
        is_cached: true // 标记这是从历史加载的
      };
      emit('update-analysis-result', result);
}

// 修改：切换报告 - 现在主要更新 selectedReportIndex 并触发 emit
const switchReport = async () => {
  if (selectedReportIndex.value >= 0 && selectedReportIndex.value < reportHistory.value.length) {
    try {
      const report = reportHistory.value[selectedReportIndex.value];
      console.log(`切换到报告 ${selectedReportIndex.value}:`, report.id, `Label: ${report.displayLabel}`);
      
      currentReport.value = report; // 更新当前报告引用
      
      // 使用辅助函数触发更新
      emitResultFromReport(report);
      
      // 强制重新渲染分析内容 (如果需要，但通常 emit 更新后 Vue 会处理)
      // setTimeout(() => { ... }, 10);
    } catch (error) {
      console.error('切换报告时出错:', error);
    }
  }
}

// 修改：继续生成报告 - 添加 generation_type
const continuousGenerate = async () => {
  try {
    console.log(`继续基于最新赔率生成新报告... (流式: ${useStreaming.value})`) 
    const baseReport = currentReport.value || (reportHistory.value.length > 0 ? reportHistory.value[0] : null);
    
    if (!baseReport) {
        console.error('无法继续生成，因为没有找到基础报告。');
        return;
    }

    const reportNumber = baseReport.report_number;
    if (reportNumber === undefined || reportNumber === null) {
        console.warn(`基础报告 ID ${baseReport.id} 没有 report_number，继续生成可能导致版本混乱。`);
    }

    const existingVersions = reportHistory.value
      .filter(r => r.report_number === reportNumber)
      .map(r => r.version || 1); 
    const maxVersion = existingVersions.length > 0 ? Math.max(...existingVersions) : 0;
    const nextVersion = maxVersion + 1;

    console.log(`基础报告号: ${reportNumber}, 最高版本: ${maxVersion}, 下一版本: ${nextVersion}`);

    const payload = {
      stream: useStreaming.value, 
      previous_report: baseReport,
      continue_analysis: true,
      generation_type: 'continue', // <--- 添加类型
      base_report_id: baseReport.id,
      report_number: reportNumber,    
      next_version: nextVersion       
    };

    emit('analyze-odds', payload); 
  } catch (e) {
    console.error('继续生成报告出错:', e)
  }
}

// 格式化后的分析内容
const formattedAnalysis = computed(() => {
  const sourceText = props.analysisResult?.analysis;

  if (sourceText) {
    let formatted = sourceText.replace(/\n/g, '<br>');

    formatted = formatted.replace(/《([^》]+)》/g, '<span class="reference">《$1》</span>');
    return formatted;
  }
  return props.analysisResult ? (props.analysisResult.analysis || '') : null;
})

// 当前活跃模型名称
const activeModelName = computed(() => {
  const activeModel = availableModels.value.find(m => m.is_active)
  if (!activeModel) return '正在处理...'
  return `${activeModel.provider_name}: ${activeModel.model_name.split('/').pop() || activeModel.model_name}`
})

// 加载LLM模型列表
const loadModels = async () => {
  isModelLoading.value = true
  
  try {
    const models = await getAllLLMModels()
    
    if (models.length === 0) {
      console.warn('没有找到可用的LLM模型')
    }
    
    availableModels.value = models
    
    // 检查是否有活跃模型
    const activeModel = models.find(m => m.is_active)
    if (activeModel) {
      selectedModelId.value = activeModel.id
      isModelActive.value = true
      llmModelStatus.value = 'active'
    } else if (models.length > 0) {
      selectedModelId.value = models[0].id
    }
  } catch (error: any) {
    console.error('加载LLM模型失败:', error)
    
    if (error.response && error.response.status === 401) {
      authError.value = true
    }
  } finally {
    isModelLoading.value = false
  }
}

// 切换模型激活状态
const toggleModelActive = async () => {
  if (!selectedModelId.value) return
  
  llmModelStatus.value = 'activating'
  
  try {
    if (isModelActive.value) {
      // 激活模型
      await activateLLMModel(selectedModelId.value)
      llmModelStatus.value = 'active'
    } else {
      // 停用模型
      await deactivateLLMModel()
      llmModelStatus.value = 'inactive'
    }
  } catch (error) {
    console.error('切换模型状态失败:', error)
    isModelActive.value = !isModelActive.value // 恢复之前的状态
    llmModelStatus.value = isModelActive.value ? 'active' : 'inactive'
  }
}

// 修改：分析赔率 - 添加 generation_type
const analyzeOdds = () => {
  try {
    const isRegeneration = props.hasExistingReport;
    let payload: any = { 
      stream: useStreaming.value, 
      // 添加 generation_type
      generation_type: isRegeneration ? 'regenerate' : 'initial' 
    }; 

    if (isRegeneration) {
      console.log(`请求重新生成报告... (流式: ${useStreaming.value})`);
      const baseReport = currentReport.value || (reportHistory.value.length > 0 ? reportHistory.value[0] : null);

      if (!baseReport) {
        console.error('无法重新生成，因为没有找到基础报告。');
        // 即使没有基础报告，也触发（带stream和generation_type标志）
        emit('analyze-odds', payload);
        return;
      }

      const reportNumber = baseReport.report_number;
      // ... (计算 nextVersion 的逻辑保持不变) ...
      const existingVersions = reportHistory.value
        .filter(r => r.report_number === reportNumber)
        .map(r => r.version || 1);
      const maxVersion = existingVersions.length > 0 ? Math.max(...existingVersions) : 0;
      const nextVersion = maxVersion + 1;

      payload = {
        ...payload,
        regenerate: true, // 保留 regenerate 标志可能对后端有用
        base_report_id: baseReport.id,
        report_number: reportNumber,
        next_version: nextVersion
      };

    } else {
      console.log(`请求首次生成报告... (流式: ${useStreaming.value})`);
      // 首次生成，payload 已包含 stream 和 generation_type
    }
    emit('analyze-odds', payload); // 发送包含 stream 和 generation_type 的 payload
  } catch (e) {
    console.error('分析按钮点击处理出错:', e);
  }
}

// 暂停分析
const pauseAnalysis = () => {
  emit('pause-analysis')
}

// 停止分析
const stopAnalysis = () => {
  emit('stop-analysis')
}

// 跳转到登录页面
const redirectToLogin = () => {
  window.location.href = '/login'
}

// 组件挂载时加载模型列表
onMounted(async () => {
  await loadModels()
  
  // 加载当前配置
  try {
    // No need to call getCurrentLLMConfig here as loadModels handles active state
    // const config = await getCurrentLLMConfig()
    // llmModelStatus.value = config.is_active ? 'active' : 'inactive'
  } catch (e) {
    console.warn('获取当前LLM配置失败:', e)
  }
  
  // 加载报告历史记录 - Initial load on mount if reports exist
  if (props.hasExistingReport) {
    console.log("Component mounted with existing reports, loading history...");
    await loadReportHistory()
  }
})

// 当报告状态从无到有时，加载历史记录
watch(() => props.hasExistingReport, async (newVal, oldVal) => {
  // Only load if it transitioned from false to true OR if matchId changes and newVal is true
  if ((newVal === true && oldVal === false) || (newVal === true)) {
    console.log("hasExistingReport is true or transitioned to true, reloading report history for matchId:", props.matchId);
    await loadReportHistory();
  } else if (newVal === false) {
     console.log("hasExistingReport is false, clearing history.");
     reportHistory.value = [];
     currentReport.value = null;
  }
}, { immediate: false }); // Don't run immediately on mount, let onMounted handle initial load

// 当分析过程结束时，刷新历史记录以包含新报告
watch(() => props.analyzing, async (newAnalyzing, oldAnalyzing) => {
    if (oldAnalyzing === true && newAnalyzing === false && !props.analysisError) { // Only reload if analysis finished successfully
        // Analysis just finished successfully, reload history to include the new report
        console.log("Analysis finished successfully, reloading report history...");
        await loadReportHistory();
    } else if (oldAnalyzing === true && newAnalyzing === false && props.analysisError) {
        console.log("Analysis finished with error, not reloading history.");
    }
});

// Watch for matchId changes to reload history
watch(() => props.matchId, async (newMatchId, oldMatchId) => {
  if (newMatchId !== oldMatchId) {
    console.log(`Match ID changed from ${oldMatchId} to ${newMatchId}. Reloading history.`);
    reportHistory.value = [];
    selectedReportIndex.value = 0;
    currentReport.value = null;
    // Reload history for the new match ID
    await loadReportHistory(); // 调用重构后的函数
  }
});

// 按钮波纹效果
const createRippleEffect = (event: MouseEvent) => {
  const button = event.currentTarget as HTMLElement;
  const circle = document.createElement('span');
  const diameter = Math.max(button.clientWidth, button.clientHeight);
  const radius = diameter / 2;
  
  circle.style.width = circle.style.height = `${diameter}px`;
  circle.style.left = `${event.clientX - button.getBoundingClientRect().left - radius}px`;
  circle.style.top = `${event.clientY - button.getBoundingClientRect().top - radius}px`;
  circle.classList.add('ripple');
  
  const ripple = button.querySelector('.ripple');
  if (ripple) {
    ripple.remove();
  }
  
  button.appendChild(circle);
  
  // 移除波纹效果
  setTimeout(() => {
    if (circle && circle.parentNode === button) {
      button.removeChild(circle);
    }
  }, 600);
}
</script> 