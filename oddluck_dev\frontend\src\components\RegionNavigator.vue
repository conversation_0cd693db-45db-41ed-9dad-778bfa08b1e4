<template>
  <div class="region-navigator">
    <div class="navigator-header">
      <h3>地区选择</h3>
    </div>
    
    <div class="navigator-content">
      <!-- 五大洲 -->
      <div 
        v-for="continent in continents" 
        :key="continent.continent_id"
        class="region-group"
      >
        <div 
          class="region-header"
          @click="toggleContinent(continent.continent_id)"
          :class="{ 'expanded': expandedContinents.has(continent.continent_id) }"
        >
          <font-awesome-icon 
            icon="chevron-right" 
            class="expand-icon"
            :class="{ 'rotated': expandedContinents.has(continent.continent_id) }"
          />
          <span class="region-name">{{ continent.continent_name }}</span>
          <span class="country-count">({{ getContinentCountryCount(continent.continent_id) }})</span>
        </div>
        
        <transition name="slide-down">
          <div 
            v-if="expandedContinents.has(continent.continent_id)"
            class="countries-list"
          >
            <div
              v-for="country in getContinentCountries(continent.continent_id)"
              :key="country.country_id"
              class="country-item"
              :class="{ 'selected': selectedCountry?.country_id === country.country_id }"
              @click="selectCountry(country)"
            >
              <img 
                v-if="country.country_logo"
                :src="getCountryLogoUrl(country.country_logo)"
                :alt="country.country_name"
                class="country-flag"
                @error="handleImageError"
              />
              <span class="country-name">{{ country.country_name }}</span>
            </div>
          </div>
        </transition>
      </div>
      
      <!-- 国际/杯赛分类 -->
      <div class="region-group">
        <div 
          class="region-header"
          @click="toggleInternational()"
          :class="{ 'expanded': showInternational }"
        >
          <font-awesome-icon 
            icon="chevron-right" 
            class="expand-icon"
            :class="{ 'rotated': showInternational }"
          />
          <span class="region-name">国际/杯赛</span>
          <span class="country-count">({{ internationalCups.length }})</span>
        </div>
        
        <transition name="slide-down">
          <div 
            v-if="showInternational"
            class="countries-list"
          >
            <div
              class="country-item international-item"
              :class="{ 'selected': selectedInternational }"
              @click="selectInternational()"
            >
              <font-awesome-icon icon="globe" class="international-icon" />
              <span class="country-name">国际赛事</span>
            </div>
          </div>
        </transition>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <font-awesome-icon icon="spinner" spin />
      <span>加载中...</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <font-awesome-icon icon="exclamation-circle" />
      <span>{{ error }}</span>
      <button @click="loadData" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { library } from '@fortawesome/fontawesome-svg-core'
import { 
  faChevronRight, 
  faSpinner, 
  faExclamationCircle, 
  faGlobe 
} from '@fortawesome/free-solid-svg-icons'
import api from '../services/api'

// 注册图标
library.add(faChevronRight, faSpinner, faExclamationCircle, faGlobe)

// 类型定义
interface Continent {
  continent_id: number
  continent_name: string
  continent_name_en?: string
}

interface Country {
  country_id: number
  country_name: string
  country_logo?: string
  continent_id: number
}

interface Cup {
  id: number
  cup_id: string
  cup_short_name: string
  cup_logo?: string
  continent_id?: number
  country_id?: number
}

// Props 和 Emits
const emit = defineEmits<{
  countrySelected: [country: Country]
  internationalSelected: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref<string | null>(null)
const continents = ref<Continent[]>([])
const countries = ref<Country[]>([])
const internationalCups = ref<Cup[]>([])
const expandedContinents = ref(new Set<number>())
const showInternational = ref(false)
const selectedCountry = ref<Country | null>(null)
const selectedInternational = ref(false)

// 计算属性
const getContinentCountries = (continentId: number) => {
  return countries.value.filter(country => country.continent_id === continentId)
}

const getContinentCountryCount = (continentId: number) => {
  return getContinentCountries(continentId).length
}

// 方法
const getCountryLogoUrl = (logoPath: string) => {
  return logoPath.startsWith('http') ? logoPath : `http://localhost:8000${logoPath}`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const toggleContinent = (continentId: number) => {
  if (expandedContinents.value.has(continentId)) {
    expandedContinents.value.delete(continentId)
  } else {
    expandedContinents.value.add(continentId)
  }
}

const toggleInternational = () => {
  showInternational.value = !showInternational.value
}

const selectCountry = (country: Country) => {
  selectedCountry.value = country
  selectedInternational.value = false
  emit('countrySelected', country)
}

const selectInternational = () => {
  selectedCountry.value = null
  selectedInternational.value = true
  emit('internationalSelected')
}

const loadData = async () => {
  try {
    loading.value = true
    error.value = null
    
    // 并行加载数据
    const [continentsRes, countriesRes, cupsRes] = await Promise.all([
      api.get('/continents/'),
      api.get('/countries/'),
      api.get('/cups/')
    ])
    
    continents.value = continentsRes.data.results || continentsRes.data
    countries.value = countriesRes.data.results || countriesRes.data
    
    // 筛选国际杯赛（没有特定国家的杯赛）
    const allCups = cupsRes.data.results || cupsRes.data
    internationalCups.value = allCups.filter((cup: Cup) => 
      !cup.country_id || cup.cup_short_name.includes('欧冠') || 
      cup.cup_short_name.includes('欧联') || cup.cup_short_name.includes('世界杯') ||
      cup.cup_short_name.includes('欧洲杯')
    )
    
  } catch (err: any) {
    console.error('加载地区数据失败:', err)
    error.value = '加载地区数据失败，请重试'
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>
