<template>
  <div class="spider-log-viewer">
    <div class="log-header">
      <h3 class="log-title">{{ title }}</h3>
      <div class="log-status" :class="statusClass">
        <span class="status-text">{{ task?.status }}</span>
        <span class="status-indicator"></span>
      </div>
    </div>
    
    <div class="log-info">
      <div class="info-item">
        <span class="label">任务ID:</span>
        <span class="value">{{ task?.task_id }}</span>
      </div>
      <div class="info-item">
        <span class="label">类型:</span>
        <span class="value">{{ spiderTypeText }}</span>
      </div>
      <div class="info-item">
        <span class="label">联赛:</span>
        <span class="value">{{ task?.league }}</span>
      </div>
      <div class="info-item">
        <span class="label">赛季:</span>
        <span class="value">{{ task?.season }}</span>
      </div>
      <div class="info-item" v-if="task?.round">
        <span class="label">轮次:</span>
        <span class="value">{{ task?.round }}</span>
      </div>
      <div class="info-item">
        <span class="label">开始时间:</span>
        <span class="value">{{ formatTime(task?.started_at) }}</span>
      </div>
      <div class="info-item" v-if="task?.completed_at">
        <span class="label">完成时间:</span>
        <span class="value">{{ formatTime(task?.completed_at) }}</span>
      </div>
      <div class="info-item full-width">
        <span class="label">当前消息:</span>
        <span class="value">{{ task?.message }}</span>
      </div>
    </div>
    
    <div class="progress-container" v-if="task">
      <div class="progress-label">进度 {{ task.progress }}%</div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: `${task.progress}%` }"></div>
      </div>
    </div>
    
    <div class="log-actions">
      <button @click="refreshLog" class="refresh-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="1 4 1 10 7 10"></polyline>
          <polyline points="23 20 23 14 17 14"></polyline>
          <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
        </svg>
        刷新
      </button>
      <button @click="pauseTask" class="pause-btn" v-if="task?.status === 'running'">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="6" y="4" width="4" height="16"></rect>
          <rect x="14" y="4" width="4" height="16"></rect>
        </svg>
        暂停任务
      </button>
      <button @click="resumeTask" class="resume-btn" v-if="task?.status === 'paused'">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polygon points="5 3 19 12 5 21 5 3"></polygon>
        </svg>
        恢复任务
      </button>
      <button @click="terminateTask" class="terminate-btn" v-if="task?.status === 'running' || task?.status === 'paused'">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        </svg>
        终止任务
      </button>
      <button @click="copyLog" class="copy-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
        复制日志
      </button>
      <button @click="downloadLog" class="download-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="7 10 12 15 17 10"></polyline>
          <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载日志
      </button>
    </div>
    
    <div class="log-content-wrapper">
      <div class="log-content" ref="logContent">
        <pre>{{ formatLogContent }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { PropType } from 'vue'
import axios from 'axios'
import '../styles/spider-log-viewer.css'

interface SpiderTask {
  task_id: string;
  spider_type: string;
  league: string;
  season: string;
  round?: string;
  status: string;
  progress: number;
  message: string;
  log_content: string;
  started_at: string;
  updated_at: string;
  completed_at?: string;
}

const props = defineProps({
  taskId: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: '爬虫日志'
  },
  autoRefresh: {
    type: Boolean,
    default: false
  },
  refreshInterval: {
    type: Number,
    default: 3000
  }
})

const emit = defineEmits(['statusChanged'])

const task = ref<SpiderTask | null>(null)
const loading = ref(false)
const error = ref('')
const logContent = ref<HTMLElement | null>(null)
const refreshTimer = ref<number | null>(null)

// 计算属性
const statusClass = computed(() => {
  if (!task.value) return 'status-unknown'
  
  switch (task.value.status) {
    case 'running':
      return 'status-running'
    case 'completed':
      return 'status-completed'
    case 'failed':
      return 'status-failed'
    case 'paused':
      return 'status-paused'
    default:
      return 'status-unknown'
  }
})

const spiderTypeText = computed(() => {
  if (!task.value) return ''
  
  switch (task.value.spider_type) {
    case 'league':
      return '联赛爬虫'
    case 'match':
      return '比赛爬虫'
    default:
      return task.value.spider_type || '未知'
  }
})

const formatLogContent = computed(() => {
  if (!task.value?.log_content) return '暂无日志'
  return task.value.log_content
})

// 添加状态变更监听
let previousStatus = ''

const fetchLogData = async () => {
  if (!props.taskId) return
  
  loading.value = true
  error.value = ''
  
  try {
    const response = await axios.get(`http://localhost:8000/api/spider-logs/${props.taskId}/status/`)
    task.value = response.data
    
    // 检查状态是否变更，如果变更则触发事件
    if (previousStatus && task.value && previousStatus !== task.value.status) {
      emit('statusChanged', {
        taskId: props.taskId,
        status: task.value.status,
        message: task.value.message,
        progress: task.value.progress
      })
    }
    
    // 更新前一个状态
    previousStatus = task.value?.status || ''
    
    // 如果日志已完成或失败，停止自动刷新
    if (task.value && (task.value.status === 'completed' || task.value.status === 'failed')) {
      stopAutoRefresh()
    }
    
    // 滚动到日志底部
    setTimeout(scrollToBottom, 100)
  } catch (err) {
    console.error('获取爬虫日志失败:', err)
    error.value = '获取爬虫日志失败'
  } finally {
    loading.value = false
  }
}

const refreshLog = () => {
  fetchLogData()
}

const copyLog = () => {
  if (!task.value?.log_content) return
  
  // 创建任务详情的文本
  const taskInfo = `爬虫任务详情:
任务ID: ${task.value.task_id}
类型: ${spiderTypeText.value}
联赛: ${task.value.league}
赛季: ${task.value.season}
${task.value.round ? `轮次: ${task.value.round}` : ''}
状态: ${task.value.status}
进度: ${task.value.progress}%
开始时间: ${formatTime(task.value.started_at)}
${task.value.completed_at ? `完成时间: ${formatTime(task.value.completed_at)}` : ''}
当前消息: ${task.value.message}

日志内容:
${task.value.log_content}`
  
  navigator.clipboard.writeText(taskInfo)
    .then(() => alert('日志已复制到剪贴板'))
    .catch(err => console.error('复制失败:', err))
}

const downloadLog = () => {
  if (!task.value?.log_content) return
  
  // 创建任务详情的文本
  const taskInfo = `爬虫任务详情:
任务ID: ${task.value.task_id}
类型: ${spiderTypeText.value}
联赛: ${task.value.league}
赛季: ${task.value.season}
${task.value.round ? `轮次: ${task.value.round}` : ''}
状态: ${task.value.status}
进度: ${task.value.progress}%
开始时间: ${formatTime(task.value.started_at)}
${task.value.completed_at ? `完成时间: ${formatTime(task.value.completed_at)}` : ''}
当前消息: ${task.value.message}

日志内容:
${task.value.log_content}`
  
  // 创建Blob对象
  const blob = new Blob([taskInfo], { type: 'text/plain' })
  
  // 创建下载链接
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  a.href = url
  a.download = `爬虫日志_${task.value.league}_${task.value.season}_${timestamp}.txt`
  document.body.appendChild(a)
  a.click()
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, 100)
}

const scrollToBottom = () => {
  if (logContent.value) {
    logContent.value.scrollTop = logContent.value.scrollHeight
  }
}

const formatTime = (timeString?: string) => {
  if (!timeString) return '未知'
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN')
}

const startAutoRefresh = () => {
  if (props.autoRefresh && !refreshTimer.value) {
    refreshTimer.value = window.setInterval(fetchLogData, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 暂停任务方法
const pauseTask = async () => {
  if (!props.taskId) return
  
  try {
    await axios.post(`http://localhost:8000/api/spider-logs/${props.taskId}/pause/`)
    
    // 立即刷新状态
    await fetchLogData()
    
    // 触发状态变更事件
    emit('statusChanged', {
      taskId: props.taskId,
      status: 'paused',
      message: '任务已暂停',
      progress: task.value?.progress || 0
    })
  } catch (err) {
    console.error('暂停任务失败:', err)
    error.value = '暂停任务失败'
  }
}

// 恢复任务方法
const resumeTask = async () => {
  if (!props.taskId) return
  
  try {
    await axios.post(`http://localhost:8000/api/spider-logs/${props.taskId}/resume/`)
    
    // 立即刷新状态
    await fetchLogData()
    
    // 添加任务状态轮询
    startAutoRefresh()
    
    // 修改状态为运行中
    if (task.value) {
      task.value.status = 'running'
    }
    
    // 触发状态变更事件
    emit('statusChanged', {
      taskId: props.taskId,
      status: 'running',
      message: '任务已恢复运行',
      progress: task.value?.progress || 0
    })
  } catch (err) {
    console.error('恢复任务失败:', err)
    error.value = '恢复任务失败'
  }
}

// 终止任务方法
const terminateTask = async () => {
  if (!props.taskId) return
  
  if (confirm('确定要终止此任务吗？此操作不可撤销。')) {
    try {
      await axios.post(`http://localhost:8000/api/spider-logs/${props.taskId}/terminate/`)
      
      // 立即刷新状态
      await fetchLogData()
      
      // 触发状态变更事件
      emit('statusChanged', {
        taskId: props.taskId,
        status: 'failed',
        message: '任务被用户终止',
        progress: 100
      })
    } catch (err) {
      console.error('终止任务失败:', err)
      error.value = '终止任务失败'
    }
  }
}

// 生命周期钩子
onMounted(() => {
  fetchLogData()
  startAutoRefresh()
  logContent.value = document.querySelector('.log-content') as HTMLElement
})

// 监听taskId变化
watch(() => props.taskId, (newId) => {
  if (newId) {
    fetchLogData()
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 组件销毁时清除定时器
// Added cleanup to prevent memory leaks
watch(() => props.autoRefresh, (newVal) => {
  if (newVal) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

defineExpose({
  refresh: refreshLog,
  copyLog,
  downloadLog,
  pauseTask,
  resumeTask,
  terminateTask
})
</script> 