import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './style.css'
import './styles/global.css'
import './styles/match-detail.css'
import '@fortawesome/fontawesome-free/css/all.min.css'

// 初始化主题
const initTheme = () => {
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
    document.documentElement.classList.add('dark-theme')
    localStorage.setItem('theme', 'dark')
  } else {
    document.documentElement.classList.remove('dark-theme')
    localStorage.setItem('theme', 'light')
  }
}

// 初始化主题
initTheme()

// 添加全局通知函数
declare global {
  interface Window {
    createNotification: (options: {
      title?: string;
      message: string;
      theme?: 'success' | 'info' | 'warning' | 'error' | 'default';
      duration?: number;
    }) => void;
  }
}

// 全局通知函数
window.createNotification = (options) => {
  const { message, title = '通知', theme = 'default', duration = 4000 } = options;
  
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = `notification notification-${theme}`;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 280px;
    max-width: 350px;
    background-color: white;
    color: #333;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 10px;
    z-index: 9999;
    animation: notification-in 0.3s ease-out;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  `;
  
  // 主题颜色
  const themes = {
    success: '#52c41a',
    info: '#1890ff',
    warning: '#faad14',
    error: '#f5222d',
    default: '#1890ff'
  };
  
  // 标题
  const titleElement = document.createElement('div');
  titleElement.className = 'notification-title';
  titleElement.textContent = title;
  titleElement.style.cssText = `
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
    color: ${themes[theme]};
  `;
  
  // 消息
  const messageElement = document.createElement('div');
  messageElement.className = 'notification-message';
  messageElement.textContent = message;
  messageElement.style.cssText = `
    font-size: 14px;
  `;
  
  // 关闭按钮
  const closeButton = document.createElement('span');
  closeButton.className = 'notification-close';
  closeButton.innerHTML = '×';
  closeButton.style.cssText = `
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 16px;
    cursor: pointer;
    color: #999;
  `;
  
  closeButton.addEventListener('click', () => {
    closeNotification();
  });
  
  // 添加所有元素
  notification.appendChild(titleElement);
  notification.appendChild(messageElement);
  notification.appendChild(closeButton);
  
  // 添加到文档
  document.body.appendChild(notification);
  
  // 添加动画样式
  const style = document.createElement('style');
  style.innerHTML = `
    @keyframes notification-in {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    @keyframes notification-out {
      from { transform: translateX(0); opacity: 1; }
      to { transform: translateX(100%); opacity: 0; }
    }
  `;
  document.head.appendChild(style);
  
  // 关闭通知的函数
  const closeNotification = () => {
    notification.style.transform = 'translateX(100%)';
    notification.style.opacity = '0';
    
    // 移除元素
    setTimeout(() => {
      if (document.body.contains(notification)) {
        document.body.removeChild(notification);
      }
    }, 300);
  };
  
  // 自动关闭
  if (duration > 0) {
    setTimeout(() => {
      closeNotification();
    }, duration);
  }
  
  return {
    close: closeNotification
  };
};

const app = createApp(App)
app.use(router)
app.mount('#app')
