import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/match'
    },
    {
      path: '/match',
      name: 'match',
      component: () => import('../views/Match.vue')
    },
    {
      path: '/match/:id',
      name: 'odds',
      component: () => import('../views/Odds.vue')
    },
    {
      path: '/absorb',
      name: 'absorb',
      component: () => import('../views/Absorb.vue')
    },
    {
      path: '/knowledge',
      name: 'knowledge',
      component: () => import('../views/KnowledgeBase.vue')
    },
    {
      path: '/auth',
      name: 'auth',
      component: () => import('../views/Auth.vue')
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/Profile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/Profile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/llm-config',
      name: 'LLMConfig',
      component: () => import('../views/LLMConfig.vue'),
      meta: { 
        requiresAuth: true,
        title: 'LLM配置管理'
      }
    }
  ]
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 检查用户是否已登录
  const isAuthenticated = localStorage.getItem('token')
  
  // 如果页面需要登录而用户未登录，则重定向到登录页
  if ((to.path.includes('/knowledge') || to.meta.requiresAuth) && !isAuthenticated) {
    next('/auth')
  } else {
    next()
  }
})

export default router 