import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8000/api', // Django后端API地址
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理token过期
api.interceptors.response.use(
  response => {
    return response;
  },
  async error => {
    const originalRequest = error.config;
    
    // 检查 error.response 是否存在，并且是401错误
    if (error.response && error.response.status === 401 && !originalRequest._retry && originalRequest.url !== '/auth/token/refresh/') {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
          // 如果没有refreshToken，直接登出
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          window.location.href = '/auth';
          return Promise.reject(error);
        }
        
        // 尝试刷新token
        const response = await api.post('/auth/token/refresh/', {
          refresh: refreshToken
        });
        
        // 更新token
        const newToken = response.data.access;
        localStorage.setItem('token', newToken);
        
        // 重试原请求
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
        return axios(originalRequest);
      } catch (refreshError) {
        // 刷新失败，清除token并重定向到登录页
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        window.location.href = '/auth';
        return Promise.reject(refreshError);
      }
    }
    
    // 对于其他错误（包括没有 response 的网络错误），直接拒绝
    return Promise.reject(error);
  }
);

// 添加命名导出
export { api };
export default api; 