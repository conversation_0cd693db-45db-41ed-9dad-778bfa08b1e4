import axios from 'axios'

const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

const api = axios.create({
  baseURL: `${baseURL}/api`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    // 获取令牌
    const token = localStorage.getItem('token')

    // 如果存在令牌，添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    console.log('API Request:', config.url, config.params || {}, 'Headers:', config.headers)
    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 添加响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API Response:', response.config.url, response.status)
    return response
  },
  error => {
    console.error('Response Error:', error.config?.url, error.response?.status, error.response?.data, 'Headers:', error.config?.headers)

    // 如果是401错误(未授权)，可能是token过期
    if (error.response && error.response.status === 401) {
      console.log('收到401未授权响应，检查认证信息...')

      // 获取令牌
      const token = localStorage.getItem('token')
      console.log('当前Token状态:', token ? '存在' : '不存在')

      // 可以在这里实现自动刷新token的逻辑
      // 或者直接清除认证信息并重定向到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('username')
      localStorage.removeItem('userId')

      // 如果不在登录页，重定向到登录页
      if (window.location.pathname !== '/auth') {
        console.log('重定向到登录页...')
        window.location.href = '/auth'
      }
    }

    return Promise.reject(error)
  }
)

export { api }

export const mediaURL = `${baseURL}/media/`

export const leagueApi = {
  getAll: () => api.get('/leagues/'),
  getById: (id: string) => api.get(`/leagues/${id}/`),
  search: (query: string) => api.get(`/leagues/?search=${query}`)
}

export const teamApi = {
  getAll: () => api.get('/teams/'),
  getById: (id: string) => api.get(`/teams/${id}/`),
  getByLeague: (leagueId: string) => api.get(`/teams/?league=${leagueId}`)
}

export const matchApi = {
  getAll: () => api.get('/matches/'),
  getById: (id: string) => api.get(`/matches/${id}/`),
  getByLeague: (leagueId: string) => api.get(`/matches/?league=${leagueId}`),
  getByDate: (date: string) => api.get(`/matches/date/${date}/`),
  getByCountryAndDate: (countryId: number, date: string) =>
    api.get(`/matches/date/${date}/?country_id=${countryId}`),
  getInternationalByDate: (date: string) =>
    api.get(`/matches/date/${date}/?international=true`)
}

export const oddsApi = {
  getByMatch: (matchId: string) => api.get(`/odds/?match=${matchId}`),
  getLatest: (matchId: string) => api.get(`/odds/?match=${matchId}&ordering=-update_time`)
}

export default api