import { api } from './api';

// 获取API基础URL的辅助函数
const getApiBaseUrl = () => {
  // 从api实例中获取baseURL，如果不可用，则使用默认值
  return (api.defaults.baseURL || 'http://localhost:8000/api').replace(/\/$/, '');
};

// 从环境变量获取默认LLM模型
const DEFAULT_LLM_MODEL = (() => {
  try {
    return import.meta.env.VITE_LLM_MODEL_NAME || 'deepseek-ai/DeepSeek-V3';
  } catch (e) {
    console.warn('无法读取环境变量VITE_LLM_MODEL_NAME，使用默认值');
    return 'deepseek-ai/DeepSeek-V3';
  }
})();

/**
 * 分析比赛赔率
 * @param matchId 比赛ID
 * @param model LLM模型名称，默认为DeepSeek模型
 * @returns 
 */
export const analyzeMatchOdds = async (matchId: string, model: string = DEFAULT_LLM_MODEL) => {
  try {
    const response = await api.post(`/llm/analyze-odds/${matchId}/`, { 
      model: model
    });
    return response.data;
  } catch (error) {
    console.error('获取赔率分析失败:', error);
    throw error;
  }
}

/**
 * 获取当前活跃LLM模型配置
 * @returns 当前活跃的LLM模型配置
 */
export const getCurrentLLMConfig = async () => {
  try {
    const response = await api.get('/llm/config/');
    return response.data;
  } catch (error) {
    console.error('获取当前LLM配置失败:', error);
    throw error;
  }
}

/**
 * 流式分析比赛赔率
 * @param matchId 比赛ID
 * @param model LLM模型名称
 * @param onChunk 数据块回调
 * @param onError 错误回调
 * @param onComplete 完成回调
 * @param extraData 额外数据，如完整的赔率数据
 * @returns 包含取消方法的控制器对象
 */
export function analyzeMatchOddsStream(
  matchId: string, 
  model: string = DEFAULT_LLM_MODEL,
  onChunk: (data: any) => void,
  onError: (error: any) => void,
  onComplete: () => void,
  extraData: any = null
) {
  // 获取token
  const token = localStorage.getItem('token');
  
  const dataQueue: any[] = [];
  let isPaused = false;

  // 递归处理队列中的数据，用于暂停/恢复功能
  async function processQueue() {
    if (isPaused && dataQueue.length > 0) {
      // 暂停时，延迟处理队列
      setTimeout(processQueue, 500);
      return;
    }
    
    if (dataQueue.length > 0) {
      const item = dataQueue.shift();
      onChunk(item);
      
      // 继续处理队列中的下一项
      setTimeout(processQueue, 0);
    }
  }
  
  // 定义取消函数
  const controller = {
    cancel: () => {},
    pause: () => {
      isPaused = true;
    },
    resume: () => {
      isPaused = false;
      if (dataQueue.length > 0) {
        processQueue();
      }
    }
  };
  
  // 创建一个提取JSON对象的函数
  function extractJsonObjects(text: string) {
    const result = {
      objects: [] as string[],
      remainder: ''
    };
    
    let stack = 0;
    let start = -1;
    
    for (let i = 0; i < text.length; i++) {
      if (text[i] === '{') {
        if (stack === 0) {
          start = i;
        }
        stack++;
      } else if (text[i] === '}') {
        stack--;
        if (stack === 0 && start !== -1) {
          result.objects.push(text.substring(start, i + 1));
          start = -1;
        }
      }
    }
    
    // 保存未完成的JSON对象
    if (start !== -1) {
      result.remainder = text.substring(start);
    }
    
    return result;
  }
  
  // 浏览器原生的TextDecoder
  const decoder = new TextDecoder();
  
  // 发起请求
  (async () => {
    try {
      console.log(`开始流式分析赔率 (matchId=${matchId}, model=${model})`);
      
      // 准备请求体，移除 match_id
      const requestBody: any = { 
        model 
      };
      
      // 如果提供了额外数据，添加到请求中
      if (extraData) {
        requestBody.extra_data = extraData;
        console.log('发送额外赔率数据:', JSON.stringify(extraData).substring(0, 100) + '...');
      }
      
      // 修正：将 matchId 加入 URL 路径，并使用正确的流式端点
      const response = await fetch(`${getApiBaseUrl()}/llm/analysis/match/${matchId}/stream/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        body: JSON.stringify(requestBody),
      });
      
      if (!response.ok) {
        console.error(`流式请求失败: ${response.status} ${response.statusText}`);
        let errorText = '';
        try {
          const errorData = await response.text();
          errorText = errorData;
          console.error('错误响应内容:', errorData);
        } catch (e) {
          console.error('无法解析错误响应:', e);
        }
        
        onError({ 
          message: `请求失败 (${response.status}): ${errorText || response.statusText}`,
          status: response.status,
          response
        });
        return;
      }
      
      // 使用ReadableStream处理流式响应
      const reader = response.body!.getReader();
      let buffer = '';
      let receivedData = false;
      
      // 设置超时检测
      const timeoutCheck = setTimeout(() => {
        if (!receivedData) {
          console.error('响应超时，未收到任何数据');
          onError({ message: '响应超时，未收到任何数据' });
          controller.cancel();
        }
      }, 10000); // 10秒超时
      
      // 流式读取响应
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          // 处理缓冲区中剩余的数据
          if (buffer.trim()) {
            try {
              const jsonData = JSON.parse(buffer);
              if (!isPaused) {
                onChunk(jsonData);
              } else {
                dataQueue.push(jsonData);
              }
              // 如果缓冲区有数据但未标记done，则设置done标志
              if (!jsonData.done) {
                jsonData.done = true;
                if (!isPaused) {
                  onChunk(jsonData);
                } else {
                  dataQueue.push(jsonData);
                }
              }
            } catch (e) {
              console.error('解析JSON出错（读取结束）:', e, buffer);
            }
          }
          break;
        }

        // 将收到的数据块转换为文本，并添加到缓冲区
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        receivedData = true;

        // 处理缓冲区中的完整JSON对象
        const jsonObjects = extractJsonObjects(buffer);
        if (jsonObjects.objects.length > 0) {
          buffer = jsonObjects.remainder;
          
          // 处理每个提取的JSON对象
          for (const jsonStr of jsonObjects.objects) {
            try {
              const data = JSON.parse(jsonStr);
              if (!isPaused) {
                onChunk(data);
              } else {
                dataQueue.push(data);
                // 启动队列处理
                if (dataQueue.length === 1) {
                  processQueue();
                }
              }
            } catch (e) {
              console.error('解析JSON出错:', e, jsonStr);
            }
          }
        }
      }
      
      // 清除超时检测
      clearTimeout(timeoutCheck);
      
      console.log('流式请求完成');
      // 通知完成
      onComplete();
    } catch (error) {
      console.error('启动流式分析时出错:', error);
      onError({ message: '启动分析时出错', error });
    }
  })();
  
  return controller;
}

/**
 * 获取知识库内容
 * @param page 页码，默认为1
 * @param pageSize 每页数量，默认为10
 * @returns 
 */
export const getKnowledgeBase = async (page: number = 1, pageSize: number = 10) => {
  try {
    const response = await api.get('/llm/knowledge/', {
      params: {
        page,
        page_size: pageSize
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取知识库内容失败:', error);
    throw error;
  }
}

/**
 * 添加知识到知识库
 * @param title 标题
 * @param content 内容
 * @param source 来源
 * @param category 类别
 * @returns 
 */
export const addKnowledge = async (
  title: string, 
  content: string, 
  source?: string, 
  category?: string
) => {
  try {
    const response = await api.post('/llm/knowledge/', {
      title,
      content,
      source,
      category
    });
    return response.data;
  } catch (error) {
    console.error('添加知识失败:', error);
    throw error;
  }
}

/**
 * 删除知识库内容
 * @param id 知识ID
 * @returns 
 */
export const deleteKnowledge = async (id: number) => {
  try {
    const response = await api.delete(`/llm/knowledge/${id}/`);
    return response.data;
  } catch (error) {
    console.error('删除知识失败:', error);
    throw error;
  }
}

/**
 * 修改知识库内容
 * @param id 知识ID
 * @param data 要更新的数据
 * @returns 
 */
export const updateKnowledge = async (id: number, data: any) => {
  try {
    const response = await api.put(`/llm/knowledge/${id}/`, data);
    return response.data;
  } catch (error) {
    console.error('更新知识失败:', error);
    throw error;
  }
}

/**
 * 搜索知识库
 * @param query 搜索关键词
 * @returns 
 */
export const searchKnowledge = async (query: string) => {
  try {
    const response = await api.get('/llm/knowledge/search/', {
      params: { q: query }
    });
    return response.data;
  } catch (error) {
    console.error('搜索知识失败:', error);
    throw error;
  }
}

/**
 * 获取分析历史记录
 * @param matchId 可选的比赛ID，如果提供则只返回该比赛的分析
 * @returns 分析历史记录
 */
export async function getAnalysisHistory(matchId?: string) {
  try {
    const url = matchId 
      ? `/llm/history/${matchId}/` 
      : `/llm/history/`;
    
    console.log('调用分析历史API:', url);
    const response = await api.get(url);
    console.log('分析历史API返回:', response.status, response.data ? (Array.isArray(response.data) ? response.data.length + '条记录' : '有数据') : '无数据');
    return response.data;
  } catch (error: any) {
    console.error('获取分析历史时出错:', error);
    // 如果是404错误，返回空数组而不是抛出错误
    if (error.response && error.response.status === 404) {
      console.warn('未找到分析历史记录，返回空数组');
      return [];
    }
    throw error;
  }
}

/**
 * 获取知识详情
 * @param knowledgeId 知识ID
 * @returns 知识详情
 */
export async function getKnowledgeDetail(knowledgeId: number) {
  try {
    const response = await api.get(`/llm/knowledge/${knowledgeId}/`);
    return response.data;
  } catch (error) {
    console.error('获取知识详情时出错:', error);
    throw error;
  }
}

/**
 * 获取所有可用的LLM模型
 * @returns 所有可用的LLM模型列表
 */
export const getAllLLMModels = async () => {
  try {
    // 尝试从API获取模型列表
    const response = await api.get('/llm/models/');
    console.log('API返回的LLM模型数据:', response.data);
    
    // 处理不同数据格式的情况
    let modelsList = [];
    if (Array.isArray(response.data)) {
      // 如果直接是数组
      modelsList = response.data;
    } else if (response.data && typeof response.data === 'object') {
      // 如果是包含results或models字段的对象
      modelsList = response.data.results || response.data.models || [];
      
      // 检查是否有嵌套结构，如{data: {results: []}}
      if (modelsList.length === 0 && response.data.data) {
        if (Array.isArray(response.data.data)) {
          modelsList = response.data.data;
        } else if (response.data.data && typeof response.data.data === 'object') {
          modelsList = response.data.data.results || response.data.data.models || [];
        }
      }
    }
    
    console.log('处理后的LLM模型列表:', modelsList);
    
    // 确保返回的是数组
    if (!Array.isArray(modelsList)) {
      console.warn('格式处理后的模型列表不是数组，返回空数组');
      return [];
    }
    
    return modelsList;
  } catch (error) {
    console.error('获取可用LLM模型失败:', error);
    // 尝试从备用端点获取模型
    try {
      console.log('尝试从备用端点获取模型信息...');
      
      // 尝试从config端点获取当前配置
      const configResponse = await api.get('/llm/config/');
      console.log('配置API返回:', configResponse.data);
      
      if (configResponse.data && configResponse.data.model) {
        // 从配置创建一个模型对象
        const model = {
          id: configResponse.data.model.id,
          provider_name: configResponse.data.provider?.name || '默认提供商',
          model_name: configResponse.data.model.name || configResponse.data.model.model_id || configResponse.data.model_name,
          is_active: true
        };
        
        console.log('从配置创建的模型:', model);
        return [model];
      }
    } catch (backupError) {
      console.error('尝试备用端点也失败:', backupError);
    }
    
    // 如果所有尝试都失败，返回默认示例模型
    console.log('所有尝试均失败，返回默认模型数据');
    return [
      {
        id: 1,
        provider_name: 'SiliconFlow',
        model_name: 'deepseek-ai/DeepSeek-V3',
        is_active: true
      }
    ];
  }
}

/**
 * 激活特定的LLM模型
 * @param modelId 要激活的模型ID
 * @returns 激活结果
 */
export const activateLLMModel = async (modelId: number) => {
  try {
    console.log(`调用激活API，模型ID: ${modelId}`);
    // 确保使用正确的API路径和参数格式
    const response = await api.post('/llm/models/' + modelId + '/activate/');
    console.log('激活模型成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('激活LLM模型失败:', error);
    throw error;
  }
}

/**
 * 停用当前活跃的LLM模型
 * @returns 停用结果
 */
export const deactivateLLMModel = async () => {
  try {
    console.log('调用停用API');
    const response = await api.post('/llm/deactivate-model/');
    console.log('停用模型成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('停用LLM模型失败:', error);
    throw error;
  }
}

/**
 * 测试LLM模型连接
 * @param modelId 要测试的模型ID
 * @returns 测试结果
 */
export const testLLMModelConnection = async (modelId: number) => {
  try {
    console.log(`测试模型连接，模型ID: ${modelId}`);
    const response = await api.post(`/llm/models/${modelId}/test_connection/`);
    console.log('测试连接结果:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('测试模型连接失败:', error);
    // 如果服务器返回了错误信息，提取并返回
    if (error.response && error.response.data) {
      return {
        success: false,
        message: error.response.data.message || '连接失败',
        status_code: error.response.status,
        response_time: error.response.data.response_time || null
      };
    }
    // 否则返回一个默认错误对象
    return {
      success: false,
      message: error.message || '测试连接时发生错误',
      status_code: error.response?.status || null,
      response_time: null
    };
  }
}

// 获取历史分析报告
export const getHistoricalAnalysis = async (matchId: string) => {
  try {
    console.log(`开始获取历史分析报告，原始比赛ID: ${matchId}`);
    // 修正API路径，并添加结尾斜杠
    const response = await api.get(`/llm/analysis/history/${matchId}/`);
    
    // 确保时间格式保持原样，不被JavaScript自动解析为本地时区
    if (response.data && Array.isArray(response.data)) {
      console.log('获取历史分析报告成功:', response.data);
      return response.data;
    }
    
    console.log('获取历史分析报告成功:', response.data);
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.status === 404) {
      // 如果没有找到报告，返回null而不是抛出错误
      console.log(`未找到历史分析报告，matchId: ${matchId}`);
      return null;
    }
    console.error('获取历史分析报告失败:', error);
    throw error;
  }
}; 