@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@400;600&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局CSS变量 */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #333333;
  --text-secondary: #718096;
  --primary-color: #4a6cf7;
  --primary-hover: #3a57d0;
  --secondary-color: #38b2ac;
  --border-color: #e2e8f0;
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --team-logo-size: 64px;  /* 添加全局球队logo尺寸变量 */
}

/* 暗色模式变量 */
:root.dark-theme {
  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --text-primary: #e2e8f0;
  --text-secondary: #a0aec0;
  --primary-color: #5a78ff;
  --primary-hover: #6b8aff;
  --secondary-color: #4fd1c5;
  --border-color: #4a5568;
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* 应用暗色模式样式 */
:root.dark-theme body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

:root.dark-theme .card {
  background-color: var(--bg-secondary);
  box-shadow: var(--card-shadow);
}

/* 全局球队logo样式 */
.team-logo {
  width: var(--team-logo-size);
  height: var(--team-logo-size);
  display: flex;
  align-items: center;
  justify-content: center;
}

.team-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

@layer base {
  html {
    font-family: 'Open Sans', system-ui, sans-serif;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }
  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
  }
  
  /* 删除所有元素的默认外边距 */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg transition-colors duration-300;
  }
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark;
  }
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-dark;
  }
  .card {
    background-color: var(--bg-primary);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    padding: 1rem;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease-in-out;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

:root.dark-theme ::-webkit-scrollbar-thumb {
  background: #4a5568;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

:root.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* 报告可用提示样式 */
.report-available-toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(16, 185, 129, 0.95);
  color: white;
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 320px;
  cursor: pointer;
  animation: slide-in-right 0.3s ease-out forwards;
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.toast-icon {
  margin-right: 12px;
  font-size: 18px;
  background-color: white;
  color: #10b981;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0 8px;
  opacity: 0.7;
}

.toast-close:hover {
  opacity: 1;
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
