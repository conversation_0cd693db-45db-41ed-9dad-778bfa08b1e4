/* 
  注意：背景图片需要放置在以下路径：
  oddluck_dev/backend/static/images/absorb-bg.jpg
  
  如果路径不存在，请创建对应的目录结构
*/
.absorb-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: url('/static/images/absorb-bg.jpg') no-repeat center center fixed;
  background-size: cover;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.subtitle {
  font-size: 1.1rem;
  color: #64748b;
  font-style: italic;
}

.absorb-container {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.content-panel {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-width: 300px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 550px; /* 增加最小高度，原来是固定520px */
  height: auto; /* 允许自动扩展高度 */
}

.content-panel:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.panel-title {
  font-size: 1.5rem;
  color: #1e293b;
  margin-bottom: 20px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid #e0e7ff;
  position: relative;
}

.panel-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background-color: #4f46e5;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.spacer {
  flex: 1; /* 占用剩余空间 */
  min-height: 58px; /* 对应右侧轮次选择器的高度 */
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.form-select {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  color: #1e293b;
  background-color: white;
  transition: all 0.2s;
}

.form-select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
  outline: none;
}

.crawler-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto;
  padding-top: 20px;
}

.absorption-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #eef2ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.2);
}

.absorption-circle:hover {
  background: #e0e7ff;
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(79, 70, 229, 0.3);
}

.absorption-circle.running {
  animation: pulse 2s infinite;
  background: #dbeafe;
}

.absorption-icon {
  width: 50px;
  height: 50px;
  fill: #4f46e5;
  transition: all 0.3s ease;
}

.absorption-circle:hover .absorption-icon {
  fill: #4338ca;
}

.status-text {
  font-size: 1rem;
  color: #4f46e5;
  font-weight: 500;
  text-align: center;
  min-height: 3rem; /* 增加最小高度 */
  margin-top: 10px;
  margin-bottom: 10px;
  word-wrap: break-word; /* 允许长文本换行 */
  overflow-wrap: break-word; /* 确保长单词也能换行 */
  width: 100%; /* 确保文本使用全部可用宽度 */
  line-height: 1.4; /* 增加行高 */
}

.progress-container {
  margin-top: 20px;
  width: 100%;
}

.progress-bar {
  height: 10px;
  background-color: #f1f5f9;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 10px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background-color: #4f46e5;
  background-image: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
  border-radius: 5px;
  transition: width 0.3s ease;
  animation: progress-animation 1s linear infinite;
}

@keyframes progress-animation {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 1rem 0;
  }
}

.progress-text {
  font-size: 0.9rem;
  color: #6b7280;
  text-align: center;
  min-height: 40px; /* 增加最小高度 */
  word-wrap: break-word; /* 允许长文本换行 */
  overflow: hidden; /* 防止文本溢出 */
  margin-bottom: 10px; /* 增加底部间距 */
}

.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px; /* 最小宽度 */
  max-width: 350px; /* 最大宽度 */
  width: auto;      /* 宽度自动调整 */
  animation: slideIn 0.3s ease-out;
  font-size: 0.9rem;
  line-height: 1.4;
  /* display: inline-block; 移除，fixed定位通常不需要 */
  opacity: 0.95;
  transform-origin: bottom right;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 10px;
}

.notification-content span {
  flex: 1;
  word-break: break-word;
}

.notification.success {
  background-color: #ecfdf5;
  border-left: 4px solid #10b981;
  color: #047857;
}

.notification.error {
  background-color: #fef2f2;
  border-left: 4px solid #ef4444;
  color: #b91c1c;
}

.notification.info {
  background-color: #eff6ff;
  border-left: 4px solid #3b82f6;
  color: #1d4ed8;
}

.close-button {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.2rem;
  line-height: 1;
  cursor: pointer;
  padding: 0;
  margin-left: 5px;
  opacity: 0.7;
  transition: opacity 0.2s;
  align-self: flex-start;
}

.close-button:hover {
  opacity: 1;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.6);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 0.95;
  }
}

/* 添加淡入淡出过渡效果 */
.notification-enter-active,
.notification-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 0.95;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 适配移动设备 */
@media (max-width: 768px) {
  .absorb-container {
    flex-direction: column;
  }
  
  .content-panel {
    width: 100%;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .title {
    color: #f1f5f9;
  }
  
  .subtitle {
    color: #94a3b8;
  }
  
  .panel-title {
    color: #f1f5f9;
    border-bottom-color: #4b5563;
  }
  
  .panel-title::after {
    background-color: #818cf8;
  }
  
  .content-panel {
    background: rgba(31, 41, 55, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border-color: rgba(75, 85, 99, 0.3);
  }
  
  .form-group label {
    color: #94a3b8;
  }
  
  .form-select {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
  }
  
  .form-select:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
  }
  
  .absorption-circle {
    background: #312e81;
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
  }
  
  .absorption-circle:hover {
    background: #3730a3;
    box-shadow: 0 6px 12px rgba(99, 102, 241, 0.4);
  }
  
  .absorption-circle.running {
    background: #3730a3;
  }
  
  .absorption-icon {
    fill: #a5b4fc;
  }
  
  .absorption-circle:hover .absorption-icon {
    fill: #c4b5fd;
  }
  
  .status-text {
    color: #a5b4fc;
  }
  
  .progress-bar {
    background-color: #374151;
  }
  
  .progress-fill {
    background-color: #6366f1;
  }
  
  .progress-text {
    color: #94a3b8;
  }
}

/* 日志查看区域 */
.logs-container {
  margin: 20px auto 0;
  padding: 0; /* 移除内边距，由内部元素控制 */
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  max-height: 60vh; /* 限制最大高度 */
  /* overflow-y: auto; 移除，由内部滚动区处理 */
  /* position: relative; 移除 */
  display: flex; /* 改为 Flex 布局 */
  flex-direction: column; /* 纵向排列 */
}

/* 新增：日志内容滚动区域 */
.log-content-area {
  flex-grow: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 内部滚动 */
  padding: 15px; /* 将原容器的内边距移到这里 */
}

/* 关闭按钮容器 */
.close-logs {
  /* position: sticky; 移除 */
  /* bottom: 0; 移除 */
  /* left: 0; 移除 */
  /* right: 0; 移除 */
  text-align: center;
  padding: 10px 15px; /* 调整内边距 */
  background-color: #f8f9fa; /* 添加背景色以防内容重叠 */
  border-top: 1px solid #e9ecef; /* 添加顶部边框 */
  /* z-index: 10; 移除 */
  flex-shrink: 0; /* 防止按钮区域被压缩 */
}

.close-logs-btn {
  padding: 8px 16px;
  background-color: #e0e7ff;
  color: #4f46e5;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.close-logs-btn:hover {
  background-color: #c7d2fe;
  transform: translateY(-2px);
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .logs-container {
    background-color: #1f2937; /* 深色背景 */
    /* border-top-color: #374151;  移除，让sticky背景覆盖 */
  }
  .close-logs {
    background-color: #1f2937; /* 深色背景 */
    border-top-color: #374151;
  }
  
  .close-logs-btn {
    background-color: #312e81;
    color: #a5b4fc;
  }
  
  .close-logs-btn:hover {
    background-color: #3730a3;
  }
}

/* 添加当前轮次的样式 */
.form-select option.current-round {
  font-weight: bold;
  background-color: #e0e7ff;
  color: #4f46e5;
} 