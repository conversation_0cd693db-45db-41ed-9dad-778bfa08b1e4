/* Auth页面样式 */
.auth-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background-color: var(--bg-secondary);
  padding-top: 8rem;
  padding: 8rem 2rem 2rem 2rem;
}

.auth-card {
  width: 100%;
  max-width: 440px;
  background-color: var(--bg-primary);
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  animation: card-appear 0.5s ease-out;
}

@keyframes card-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.auth-logo {
  height: 100px;
}

.auth-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 0.5rem;
}

.auth-subtitle {
  font-size: 0.95rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 2rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  animation: shake 0.5s ease-in-out;
  position: relative;
  border: 1px solid rgba(185, 28, 28, 0.3);
  box-shadow: 0 2px 8px rgba(185, 28, 28, 0.1);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.close-error {
  position: absolute;
  right: 8px;
  top: 8px;
  background: none;
  border: none;
  padding: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #b91c1c;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-error:hover {
  background-color: rgba(185, 28, 28, 0.1);
}

.close-error::after {
  content: '点击关闭';
  position: absolute;
  top: 100%;
  right: 0;
  font-size: 10px;
  margin-top: 2px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s;
}

.close-error:hover::after {
  opacity: 1;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper svg {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
}

.input-wrapper input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 2.75rem;
  background-color: var(--bg-secondary);
  border: 1px solid transparent;
  border-radius: 10px;
  font-size: 0.95rem;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(74, 108, 247, 0.1);
}

.input-wrapper input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.toggle-password {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.toggle-password:hover {
  color: var(--primary-color);
}

.auth-button {
  margin-top: 0.5rem;
  padding: 0.875rem;
  border: none;
  border-radius: 10px;
  background-color: var(--primary-color);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  transition: all 0.5s ease;
}

.auth-button:hover::before {
  left: 100%;
}

.auth-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 108, 247, 0.25);
}

.auth-button:active {
  transform: translateY(0);
}

.auth-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.auth-footer {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.toggle-auth-mode {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.toggle-auth-mode:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .auth-card {
    padding: 2rem 1.5rem;
  }
}

:root.dark-theme .error-message {
  background-color: rgba(185, 28, 28, 0.1);
}

/* 成功消息样式 */
.success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #10b981;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slide-in 0.3s ease-out forwards;
}

.success-notification svg {
  stroke: white;
}

@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
} 