/* 全局状态指示器样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  color: white;
  position: relative;
}

.status-badge.running {
  background-color: #0284c7;
  box-shadow: 0 2px 8px rgba(2, 132, 199, 0.3);
  animation: pulse-status 2s infinite;
}

.status-badge.running::after {
  content: '';
  position: absolute;
  right: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: white;
  animation: blink 1.5s infinite;
}

.status-badge.completed {
  background-color: #16a34a;
  box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);
}

.status-badge.completed::after {
  content: '✓';
  margin-left: 6px;
  font-weight: bold;
}

@keyframes pulse-status {
  0% {
    box-shadow: 0 2px 8px rgba(2, 132, 199, 0.3);
  }
  50% {
    box-shadow: 0 2px 12px rgba(2, 132, 199, 0.6);
  }
  100% {
    box-shadow: 0 2px 8px rgba(2, 132, 199, 0.3);
  }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
} 