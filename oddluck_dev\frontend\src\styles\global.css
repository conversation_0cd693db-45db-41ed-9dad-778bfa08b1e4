/* CSS变量定义 */
:root {
  /* 颜色变量 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-hover: #f3f4f6;
  --bg-active: #e5e7eb;
  --bg-selected: #dbeafe;
  --bg-accent: #f0f9ff;

  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;

  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --border-accent: #d1d5db;

  --accent-color: #3b82f6;
  --accent-hover: #2563eb;
}

/* 深色主题变量 */
.dark-theme {
  --bg-primary-dark: #111827;
  --bg-secondary-dark: #1f2937;
  --bg-tertiary-dark: #374151;
  --bg-hover-dark: #374151;
  --bg-active-dark: #4b5563;
  --bg-selected-dark: #1e3a8a;
  --bg-accent-dark: #1e3a8a;

  --text-primary-dark: #f9fafb;
  --text-secondary-dark: #9ca3af;
  --text-tertiary-dark: #6b7280;

  --border-color-dark: #374151;
  --border-light-dark: #374151;
  --border-accent-dark: #4b5563;
}

/* 全局状态指示器样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  color: white;
  position: relative;
}

.status-badge.running {
  background-color: #0284c7;
  box-shadow: 0 2px 8px rgba(2, 132, 199, 0.3);
  animation: pulse-status 2s infinite;
}

.status-badge.running::after {
  content: '';
  position: absolute;
  right: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: white;
  animation: blink 1.5s infinite;
}

.status-badge.completed {
  background-color: #16a34a;
  box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);
}

.status-badge.completed::after {
  content: '✓';
  margin-left: 6px;
  font-weight: bold;
}

@keyframes pulse-status {
  0% {
    box-shadow: 0 2px 8px rgba(2, 132, 199, 0.3);
  }
  50% {
    box-shadow: 0 2px 12px rgba(2, 132, 199, 0.6);
  }
  100% {
    box-shadow: 0 2px 8px rgba(2, 132, 199, 0.3);
  }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}