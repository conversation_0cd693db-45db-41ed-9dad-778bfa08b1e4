/* 知识库页面样式 */
.knowledge-base {
  display: flex;
  min-height: calc(100vh - 64px);
  background-color: var(--bg-color, #f8fafc);
}

/* 侧边栏样式 */
.sidebar {
  width: 300px;
  background-color: var(--sidebar-bg, #ffffff);
  border-right: 1px solid var(--border-color, #e2e8f0);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #2d3748);
}

.add-category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary-color, #4a6cf7);
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-category-btn:hover {
  background-color: var(--primary-hover, #3a5bd9);
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-item:hover {
  background-color: var(--hover-bg, #f8fafc);
}

.category-item.active {
  background-color: var(--active-bg, #ebf5ff);
  border-left: 3px solid var(--primary-color, #4a6cf7);
}

.category-name {
  font-weight: 500;
  color: var(--text-color, #2d3748);
}

.file-count {
  font-size: 0.875rem;
  color: var(--text-muted, #a0aec0);
}

.empty-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--text-muted, #a0aec0);
}

.empty-category svg {
  margin-bottom: 1rem;
  color: var(--text-muted, #a0aec0);
}

.empty-category p {
  margin: 0;
}

.empty-category .hint {
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* 主内容区样式 */
.main-content {
  flex: 1;
  padding: 1.5rem;
  background-color: var(--content-bg, #ffffff);
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.content-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color, #2d3748);
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary-color, #4a6cf7);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-btn:hover {
  background-color: var(--primary-hover, #3a5bd9);
}

.no-selection, .empty-files {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-muted, #a0aec0);
}

.no-selection svg, .empty-files svg {
  margin-bottom: 1.5rem;
  color: var(--text-muted, #a0aec0);
}

.no-selection p, .empty-files p {
  margin: 0;
  font-size: 1.1rem;
}

.empty-files .hint {
  font-size: 0.95rem;
  margin-top: 0.5rem;
}

/* 文件列表样式 */
.file-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--content-bg, #ffffff);
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: var(--primary-color, #4a6cf7);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.file-icon {
  margin-right: 1rem;
  color: var(--text-muted, #a0aec0);
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color, #2d3748);
  margin-bottom: 0.25rem;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--text-muted, #a0aec0);
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 6px;
  background: none;
  cursor: pointer;
  color: var(--text-muted, #a0aec0);
  transition: all 0.2s ease;
}

.action-btn:hover {
  border-color: var(--primary-color, #4a6cf7);
  color: var(--primary-color, #4a6cf7);
}

.action-btn.delete:hover {
  border-color: #e53e3e;
  color: #e53e3e;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background-color: var(--dialog-bg, #ffffff);
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  max-width: 480px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dialog h3 {
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  color: var(--text-color, #2d3748);
}

.dialog-content {
  margin-bottom: 1.5rem;
}

.dialog-content input[type="text"] {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.dialog-content input[type="text"]:focus {
  outline: none;
  border-color: var(--primary-color, #4a6cf7);
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.1);
}

.upload-area {
  border: 2px dashed var(--border-color, #e2e8f0);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-area:hover {
  border-color: var(--primary-color, #4a6cf7);
  background-color: rgba(74, 108, 247, 0.05);
}

.upload-area svg {
  margin-bottom: 1rem;
  color: var(--text-muted, #a0aec0);
}

.upload-area p {
  margin: 0;
  color: var(--text-muted, #a0aec0);
}

.upload-area .supported-formats {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-muted, #a0aec0);
}

.selected-files {
  margin-top: 1rem;
}

.selected-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: var(--hover-bg, #f8fafc);
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

.selected-file span {
  flex: 1;
  margin-right: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  color: var(--text-muted, #a0aec0);
  transition: color 0.2s ease;
}

.remove-btn:hover {
  color: #e53e3e;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.dialog-actions button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: none;
  border: 1px solid var(--border-color, #e2e8f0);
  color: var(--text-color, #2d3748);
}

.cancel-btn:hover {
  background-color: var(--hover-bg, #f8fafc);
}

.confirm-btn {
  background-color: var(--primary-color, #4a6cf7);
  border: none;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background-color: var(--primary-hover, #3a5bd9);
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 暗色主题变量 */
:root {
  --bg-color: #f8fafc;
  --sidebar-bg: #ffffff;
  --content-bg: #ffffff;
  --dialog-bg: #ffffff;
  --text-color: #2d3748;
  --text-muted: #a0aec0;
  --primary-color: #4a6cf7;
  --primary-hover: #3a5bd9;
  --border-color: #e2e8f0;
  --hover-bg: #f8fafc;
  --active-bg: #ebf5ff;
  --info-color: #3182ce;
}

:root.dark-theme {
  --bg-color: #1a202c;
  --sidebar-bg: #2d3748;
  --content-bg: #2d3748;
  --dialog-bg: #2d3748;
  --text-color: #e2e8f0;
  --text-muted: #718096;
  --primary-color: #5a78ff;
  --primary-hover: #4a6cf7;
  --border-color: #4a5568;
  --hover-bg: #2d3748;
  --active-bg: rgba(74, 108, 247, 0.1);
  --info-color: #4299e1;
}

/* 向量化状态样式 */
.vector-status {
  display: inline-block;
  padding: 0.15rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 0.5rem;
}

.vector-status.processed {
  background-color: rgba(72, 187, 120, 0.1);
  color: #48bb78;
}

.vector-status.pending {
  background-color: rgba(160, 174, 192, 0.1);
  color: #a0aec0;
}

.vector-status.processing {
  background-color: rgba(237, 137, 54, 0.1);
  color: #ed8936;
  animation: pulse 1.5s infinite;
}

.vector-status.failed {
  background-color: rgba(229, 62, 62, 0.1);
  color: #e53e3e;
}

.vector-error-icon {
  display: inline-flex;
  margin-left: 0.5rem;
  color: #e53e3e;
  cursor: pointer;
}

.vector-error-icon:hover {
  opacity: 0.8;
}

.error-message-box {
  padding: 1rem;
  background-color: rgba(229, 62, 62, 0.1);
  border-radius: 0.5rem;
  color: #4a5568;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* 加载状态和错误提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(74, 108, 247, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color, #4a6cf7);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #fff;
  border-left: 4px solid #e53e3e;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  border-radius: 6px;
  z-index: 2000;
  max-width: 400px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.error-message p {
  margin: 0;
  color: #4a5568;
}

.error-message button {
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  margin-left: 16px;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 24px;
  height: 24px;
}

.error-message button:hover {
  color: #e53e3e;
}

.embedding-info {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--primary-color, #4a6cf7);
  font-weight: 500;
}

.category-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.delete-btn {
  background: none;
  border: none;
  color: var(--text-muted, #a0aec0);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  color: var(--error-color, #e53e3e);
  background-color: rgba(229, 62, 62, 0.1);
}

.dialog .warning {
  color: var(--error-color, #e53e3e);
  font-size: 0.9rem;
  margin-top: 8px;
}

.dialog .delete-btn {
  background-color: var(--error-color, #e53e3e);
  color: white;
}

.dialog .delete-btn:hover {
  background-color: var(--error-hover, #c53030);
} 