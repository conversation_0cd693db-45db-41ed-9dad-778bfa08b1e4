/* LLM配置页面样式 */
:root {
  --font-family: 'Source Sans Pro', 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

.llm-config-container {
  width: 100%;
  max-width: 1680px;
  margin: 0 auto;
  padding: 2rem;
  font-family: var(--font-family);
}

.llm-config-container,
.llm-config-container * {
  font-family: var(--font-family);
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  line-height: 1.3;
}

.page-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1.05rem;
  font-weight: 400;
  line-height: 1.6;
}

.config-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  height: calc(100vh - 210px);
  min-height: 400px;
}

.providers-panel {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.models-panel {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.panel-header h2 {
  margin: 0;
  font-size: 1.35rem;
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: -0.01em;
}

.panel-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  flex-grow: 1;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.925rem;
  letter-spacing: 0.01em;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
}

.btn-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
}

.providers-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
}

.provider-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.provider-item:hover {
  background-color: var(--bg-hover);
}

.provider-item.active {
  background-color: var(--bg-active);
  box-shadow: inset 0 0 0 1px var(--primary-color);
}

.provider-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 1rem;
  background-color: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.provider-logo img {
  max-width: 100%;
  max-height: 100%;
}

.provider-info h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
  letter-spacing: 0.01em;
}

.models-list {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  max-height: 700px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.models-list::-webkit-scrollbar {
  width: 8px;
}

.models-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.models-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.models-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.model-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-primary);
}

.model-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.875rem 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.model-title {
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.01em;
  max-width: none;
  line-height: 1.4;
  padding-right: 1rem;
}

.model-actions-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.15s ease, box-shadow 0.2s ease, opacity 0.2s ease;
  color: white;
  border: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.icon-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  opacity: 1;
}

.icon-btn:active {
  transform: translateY(-1px) scale(0.95);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  opacity: 0.9;
}

.icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-icon {
  background-color: #4f46e5;
}

.test-icon:hover:not(:disabled) {
  background-color: #4338ca;
}

.edit-icon {
  background-color: #0ea5e9;
}

.edit-icon:hover:not(:disabled) {
  background-color: #0284c7;
}

.delete-icon {
  background-color: #ef4444;
}

.delete-icon:hover:not(:disabled) {
  background-color: #dc2626;
}

.icon-btn i {
  position: relative;
  z-index: 2;
}

.model-body {
  padding: 1.25rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.model-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.model-field {
  margin-bottom: 1rem;
}

.model-field:last-child {
  margin-bottom: 0;
}

.model-field label {
  display: block;
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 0.35rem;
  font-weight: 500;
}

.model-field p {
  margin: 0;
  font-size: 0.925rem;
  line-height: 1.5;
  color: var(--text-primary);
  word-break: break-all;
  background-color: var(--input-bg);
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.context-menu {
  position: fixed;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
}

.context-menu-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--text-primary);
}

.context-menu-item:hover {
  background-color: var(--bg-hover);
}

.context-menu-item i {
  margin-right: 0.75rem;
  color: var(--text-secondary);
}

.context-menu-item.delete {
  color: #e53e3e;
}

.context-menu-item.delete i {
  color: #e53e3e;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-sm {
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.35rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.01em;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: var(--text-secondary);
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: 0.625rem 0.75rem;
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.form-text {
  font-size: 0.8125rem;
  margin-top: 0.375rem;
  display: block;
}

.text-muted {
  color: var(--text-secondary);
}

.text-danger {
  color: #e53e3e;
}

.input-group {
  display: flex;
  position: relative;
}

.input-group-append {
  display: flex;
}

.input-group-append .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  flex-grow: 1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.logo-upload-container {
  margin-top: 0.5rem;
}

.logo-preview {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  border: 1px dashed var(--border-color);
  position: relative;
}

.remove-logo {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.logo-upload-button {
  margin-top: 0.75rem;
}

.active-badge {
  display: inline-flex;
  align-items: center;
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-family: 'Open Sans', sans-serif;
  box-shadow: 0 2px 5px rgba(16, 185, 129, 0.2);
}

.active-badge i {
  margin-right: 0.5rem;
}

.activate-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.6rem 1.25rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(74, 108, 247, 0.25);
  font-family: 'Open Sans', sans-serif;
}

.activate-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 108, 247, 0.35);
}

.activate-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(74, 108, 247, 0.2);
}

.activate-btn i {
  margin-right: 0.5rem;
}

.text-center {
  text-align: center;
}

/* Dark Mode Adjustments for forms */
:root.dark-theme .form-control {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

:root.dark-theme .btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

:root.dark-theme .active-badge {
  background-color: rgba(16, 185, 129, 0.2);
}

/* 连接状态样式 */
.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.connection-status.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.connection-status.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.response-time {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-left: 4px;
}

/* 测试结果模态框样式 */
.test-result-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-result-header.success {
  background-color: rgba(16, 185, 129, 0.1);
  border-left: 4px solid #10b981;
}

.test-result-header.error {
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #ef4444;
}

.status-icon {
  font-size: 2rem;
  margin-right: 15px;
}

.status-icon i {
  vertical-align: middle;
}

.test-result-header.success .status-icon {
  color: #10b981;
}

.test-result-header.error .status-icon {
  color: #ef4444;
}

.status-text h4 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
}

.status-text p {
  margin: 0;
  color: #6b7280;
}

.test-details {
  margin-top: 20px;
}

.detail-item {
  margin-bottom: 15px;
}

.detail-item label {
  display: block;
  font-weight: 600;
  margin-bottom: 5px;
  color: #374151;
}

.model-response {
  background-color: #f9fafb;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #e5e7eb;
  white-space: pre-wrap;
  margin: 0;
  font-size: 0.9rem;
}

.response-data-toggle {
  display: inline-block;
  cursor: pointer;
  color: #3b82f6;
  font-size: 0.9rem;
  margin: 5px 0;
}

.response-data-toggle i {
  margin-right: 5px;
}

.response-data {
  background-color: #f9fafb;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #e5e7eb;
  white-space: pre-wrap;
  margin: 5px 0;
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.85rem;
  line-height: 1.5;
}

/* 响应式设计调整 */
@media (max-width: 1200px) {
  .models-list {
    grid-template-columns: 1fr;
  }
  .model-title {
    max-width: none;
  }
}

.icon-btn:hover {
  opacity: 0.9;
}

.icon-btn:active {
  opacity: 0.8;
}

.icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* API密钥查看按钮样式 */
.api-key-toggle {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #4b5563;
  transition: all 0.2s ease;
  border-radius: 0 4px 4px 0;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.api-key-toggle:hover {
  background-color: #e5e7eb;
  color: #1f2937;
}

.api-key-toggle i {
  font-size: 1.1rem;
}

/* 黑暗模式下的样式 */
:root.dark-theme .api-key-toggle {
  background-color: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

:root.dark-theme .api-key-toggle:hover {
  background-color: #4b5563;
  color: #f9fafb;
}

/* 添加Font Awesome图标支持 */
i.fas, i.fa {
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
}

/* 图标加载旋转动画 */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 确保Font Awesome图标正确显示 */
.icon-btn i.fas,
.api-key-toggle i.fas {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
} 