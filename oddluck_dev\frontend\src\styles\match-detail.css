/* 比赛详情页状态指示器 */
.match-status-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.match-status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.match-status.running {
  background-color: #0284c7;
  animation: status-pulse 2s infinite;
}

.match-status.running::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
  margin-right: 6px;
  animation: status-blink 1.5s infinite;
}

.match-status.completed {
  background-color: #16a34a;
}

.match-status.completed::before {
  content: '✓';
  margin-right: 6px;
  font-weight: bold;
}

@keyframes status-pulse {
  0%, 100% {
    box-shadow: 0 2px 6px rgba(2, 132, 199, 0.3);
  }
  50% {
    box-shadow: 0 2px 10px rgba(2, 132, 199, 0.6);
  }
}

@keyframes status-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.4; }
} 