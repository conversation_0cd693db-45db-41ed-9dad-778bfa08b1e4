/* 比赛表格组件样式 */
.match-table-container {
  width: 100%;
  height: 100%;
  background: var(--bg-primary, #ffffff);
  overflow-y: auto;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 48px 24px;
  text-align: center;
  color: var(--text-secondary, #6b7280);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-tertiary, #9ca3af);
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary, #111827);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 加载状态 - 骨架屏 */
.loading-state {
  padding: 16px;
}

.skeleton-table {
  width: 100%;
}

.skeleton-header {
  height: 40px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 8px;
  border-radius: 4px;
}

.skeleton-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.skeleton-cell {
  height: 32px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  flex: 1;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 内容区域 */
.matches-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-secondary, #f9fafb);
}

.content-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #111827);
}

.match-count {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  background: var(--bg-tertiary, #f3f4f6);
  padding: 4px 8px;
  border-radius: 12px;
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow-y: auto;
}

.matches-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

/* 表头 */
.matches-table thead th {
  background: var(--bg-secondary, #f9fafb);
  border-bottom: 2px solid var(--border-color, #e5e7eb);
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary, #111827);
  position: sticky;
  top: 0;
  z-index: 10;
}

.league-col { width: 15%; }
.time-col { width: 12%; }
.home-col { width: 20%; }
.score-col { width: 10%; text-align: center; }
.away-col { width: 20%; }
.odds-col { width: 12%; text-align: center; }
.report-col { width: 11%; text-align: center; }

.tooltip-icon {
  margin-left: 4px;
  color: var(--text-tertiary, #9ca3af);
  cursor: help;
}

/* 联赛分组标题 */
.league-divider {
  background: var(--bg-accent, #f0f9ff);
}

.league-title {
  padding: 8px 12px;
  font-weight: 600;
  color: var(--text-primary, #111827);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  align-items: center;
}

.league-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  object-fit: contain;
}

/* 比赛行 */
.match-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-light, #f3f4f6);
}

.match-row:hover {
  background: var(--bg-hover, #f9fafb);
}

.match-row td {
  padding: 10px 8px;
  vertical-align: middle;
}

.team-name {
  color: var(--text-primary, #111827);
  font-weight: 500;
}

.score {
  font-weight: 600;
  color: var(--accent-color, #3b82f6);
}

.vs {
  color: var(--text-secondary, #6b7280);
  font-style: italic;
}

/* 指示器 */
.indicator-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.odds-indicator {
  color: #f59e0b;
  font-size: 8px;
  animation: pulse 2s infinite;
}

.report-indicator {
  color: #10b981;
  font-size: 14px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 无比赛状态 */
.no-matches {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 48px 24px;
  text-align: center;
  color: var(--text-secondary, #6b7280);
}

.no-matches-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-tertiary, #9ca3af);
}

.no-matches h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary, #111827);
}

.no-matches p {
  margin: 0;
  font-size: 14px;
}

/* 滚动条样式 */
.table-container::-webkit-scrollbar {
  width: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: var(--bg-primary, #ffffff);
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--border-color, #e5e7eb);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary, #6b7280);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .matches-table {
    font-size: 12px;
  }
  
  .matches-table thead th,
  .match-row td {
    padding: 8px 4px;
  }
  
  .league-col { width: 18%; }
  .time-col { width: 15%; }
  .home-col { width: 22%; }
  .score-col { width: 10%; }
  .away-col { width: 22%; }
  .odds-col { width: 8%; }
  .report-col { width: 5%; }
}

/* 深色主题支持 */
.dark-theme .match-table-container {
  background: var(--bg-primary-dark, #111827);
}

.dark-theme .empty-state h3,
.dark-theme .no-matches h3 {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .content-header {
  background: var(--bg-secondary-dark, #1f2937);
  border-bottom-color: var(--border-color-dark, #374151);
}

.dark-theme .content-header h3 {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .match-count {
  background: var(--bg-tertiary-dark, #374151);
  color: var(--text-secondary-dark, #9ca3af);
}

.dark-theme .matches-table thead th {
  background: var(--bg-secondary-dark, #1f2937);
  border-bottom-color: var(--border-color-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .league-divider {
  background: var(--bg-accent-dark, #1e3a8a);
}

.dark-theme .league-title {
  color: var(--text-primary-dark, #f9fafb);
  border-bottom-color: var(--border-color-dark, #374151);
}

.dark-theme .match-row {
  border-bottom-color: var(--border-light-dark, #374151);
}

.dark-theme .match-row:hover {
  background: var(--bg-hover-dark, #374151);
}

.dark-theme .team-name {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .vs {
  color: var(--text-secondary-dark, #9ca3af);
}
