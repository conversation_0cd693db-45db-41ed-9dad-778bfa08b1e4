.calendar-page {
  padding: 20px;
  min-height: 100vh;
  background-color: #f9fafb;
}

.controls-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.date-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.date-picker-container {
  position: relative;
  display: inline-block;
}

.date-picker-button {
  width: auto;
  min-width: 120px;
  height: 35px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 12px;
}

.date-picker-button:hover {
  background-color: #f5f5f5;
}

.calendar-icon {
  font-size: 16px;
  color: #4f46e5;
  transition: color 0.3s ease;
}

.date-picker-button:hover .calendar-icon {
  color: #6366f1;
}

.selected-date {
  font-size: 14px;
  color: #374151;
}

.date-picker-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transform-origin: top;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.date-picker-content {
  padding: 16px;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 4px;
  font-size: 16px;
  transition: color 0.3s ease;
}

.close-button:hover {
  color: #333;
}

.date-tabs {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.date-tabs::-webkit-scrollbar {
  display: none;
}

.date-tab {
  padding: 8px 16px;
  white-space: nowrap;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s;
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #374151;
  cursor: pointer;
  min-width: 120px;
  text-align: center;
}

.date-tab:hover {
  background-color: #f3f4f6;
}

.date-tab.active {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .date-picker-button {
    background-color: #1f2937;
    border-color: #374151;
  }

  .calendar-icon {
    color: #818cf8;
  }

  .selected-date {
    color: #e5e7eb;
  }

  .date-tab {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
  }

  .date-tab:hover {
    background-color: #374151;
  }

  .date-tab.active {
    background-color: #4f46e5;
    color: white;
    border-color: #4f46e5;
  }
}

.view-switcher {
  display: flex;
  gap: 10px;
  align-items: center;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn:hover {
  background-color: #f3f4f6;
}

.btn.active {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.separator {
  width: 1px;
  background-color: #e5e7eb;
  margin: 0 10px;
}

.filter-box {
  position: relative;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn svg {
  color: #4f46e5;
  transition: color 0.3s ease;
}

.filter-btn:hover svg {
  color: #6366f1;
}

.arrow-down {
  transition: transform 0.3s ease;
  color: #4f46e5 !important;
}

.filter-btn:hover .arrow-down {
  color: #6366f1 !important;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 15px;
  z-index: 1000;
}

.loading-icon {
  font-size: 2rem;
  color: #4f46e5;
}

/* 错误提示 */
.error-message {
  padding: 15px;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn-retry {
  margin-left: auto;
  background-color: #dc2626;
  color: white;
  border: none;
}

.btn-retry:hover {
  background-color: #b91c1c;
}

.btn-retry svg {
  margin-right: 4px;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification.success {
  background-color: #ecfdf5;
  border: 1px solid #a7f3d0;
  color: #059669;
}

.notification.error {
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.notification.info {
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  color: #3b82f6;
}

.close-icon {
  margin-left: 10px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.close-icon:hover {
  opacity: 1;
}

/* 通知动画 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 筛选弹窗样式 */
.league-filter-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background: white;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
  margin-top: 5px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #e4e4e7;
}

.filter-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 0.85rem;
}

.league-options {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.league-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.league-option:hover {
  background-color: #f4f4f5;
}

.league-option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 1px solid #d1d5db;
  cursor: pointer;
}

.league-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.filter-footer {
  padding: 12px 15px;
  border-top: 1px solid #e4e4e7;
  display: flex;
  justify-content: flex-end;
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary:hover {
  background-color: #4338ca;
}

/* 列表视图样式 */
.match-cards-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.no-matches {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  padding: 40px;
  text-align: center;
}

.no-matches p {
  color: #6b7280;
  font-size: 1.1rem;
  margin: 0;
}

.league-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.league-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.league-header .league-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.league-header .league-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.league-name {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  flex: 1;
}

.match-list {
  padding: 10px;
}

.match-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.match-item:hover {
  background-color: #f8fafc;
}

.match-item:last-child {
  border-bottom: none;
}

.match-item-content {
  /* 可以添加 display: flex; 等 */
}

.match-time {
  font-size: 0.9rem;
  color: #64748b;
  /* text-align: center;  <-- 可以注释掉或删除这行 */
  margin-bottom: 8px;
  /* 新增 Flexbox 布局 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
  /* 可能需要微调 padding 或 margin 以适应指示器 */
}

/* 这个指示器的样式保持不变 */
.update-indicator {
  display: inline-block;
  color: #ff9800; /* 醒目的橙色 */
  margin-left: 6px;
  font-size: 0.6em; /* 图标尺寸 */
  vertical-align: middle; /* 这个在 flex 下可能不是必需的，但保留也无妨 */
  animation: pulse-indicator 1.5s infinite ease-in-out;
}

.match-teams {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.team-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.team-container.home {
  justify-content: flex-end;
}

.team-container.away {
  justify-content: flex-start;
}

.team-logo {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.team-logo img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.home-team-name {
  font-weight: 500;
  color: #334155;
  text-align: right;
}

.away-team-name {
  font-weight: 500;
  color: #334155;
  text-align: left;
}

.match-score {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ff0000;
  min-width: 80px;
  text-align: center;
}

/* vs的样式 */
.vs {
  color: #0f172a;
}

/* 动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* 移动端适配 */
@media (max-width: 767px) {
  .controls-container {
    padding: 15px;
    border-radius: 8px;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .date-controls {
    overflow-x: auto;
    padding-bottom: 5px;
  }

  .view-switcher {
    justify-content: space-between;
  }
}

/* 日历弹窗样式 */
.calendar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 60px;
  z-index: 9999;
}

.calendar-popup {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  width: 320px;
  border: 1px solid #e5e7eb;
  animation: popup 0.3s ease;
}

@keyframes popup {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.calendar-header {
  margin-bottom: 20px;
}

.calendar-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #111827;
}

.btn-text {
  padding: 8px;
  border: none;
  background: none;
  color: #6b7280;
  transition: all 0.2s;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text:hover {
  color: #111827;
  background-color: #f3f4f6;
}

.calendar-body {
  border-radius: 12px;
  overflow: hidden;
  background: #f9fafb;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.calendar-weekdays span {
  padding: 12px 0;
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  padding: 1px;
  background-color: #e5e7eb;
}

.calendar-day {
  aspect-ratio: 1;
  border: none;
  background: white;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0;
}

.calendar-day:hover {
  background-color: #f3f4f6;
  color: #111827;
  font-weight: 500;
}

.calendar-day.other-month {
  color: #9ca3af;
  background-color: #f9fafb;
}

.calendar-day.today {
  color: #4f46e5;
  font-weight: 600;
}

.calendar-day.today::after {
  content: '';
  position: absolute;
  bottom: 4px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #4f46e5;
}

.calendar-day.selected {
  background-color: #4f46e5;
  color: white;
  font-weight: 600;
}

.calendar-day.selected:hover {
  background-color: #4338ca;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .calendar-popup {
    background-color: #1f2937;
    border-color: #374151;
  }

  .calendar-title {
    color: #f3f4f6;
  }

  .btn-text {
    color: #9ca3af;
  }

  .btn-text:hover {
    color: #f3f4f6;
    background-color: #374151;
  }

  .calendar-body {
    background-color: #111827;
  }

  .calendar-weekdays {
    background-color: #111827;
    border-color: #374151;
  }

  .calendar-weekdays span {
    color: #9ca3af;
  }

  .calendar-days {
    background-color: #374151;
  }

  .calendar-day {
    background-color: #1f2937;
    color: #e5e7eb;
  }

  .calendar-day:hover {
    background-color: #374151;
    color: #f3f4f6;
  }

  .calendar-day.other-month {
    color: #6b7280;
    background-color: #111827;
  }

  .calendar-day.today {
    color: #818cf8;
  }

  .calendar-day.today::after {
    background-color: #818cf8;
  }

  .calendar-day.selected {
    background-color: #4f46e5;
    color: white;
  }

  .calendar-day.selected:hover {
    background-color: #4338ca;
  }
}

.date-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #374151;
  background-color: white;
  cursor: pointer;
}

.date-input:hover {
  border-color: #d1d5db;
}

@media (prefers-color-scheme: dark) {
  .date-input {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
  }
  
  .date-input:hover {
    border-color: #4b5563;
  }
}

/* 北冥按钮样式 */
.north-ming-button {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s;
  background-color: white;
  color: #374151;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.north-ming-button:hover {
  background-color: #f3f4f6;
}

.button-icon {
  margin-right: 6px;
  font-size: 1rem;
  color: #4f46e5;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .north-ming-button {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
  }

  .north-ming-button:hover {
    background-color: #374151;
  }

  .button-icon {
    color: #818cf8;
  }
}

/* 新增：更新指示器样式 */
.update-indicator {
  display: inline-block; 
  color: #ff9800; /* 醒目的橙色 */
  margin-left: 6px;
  font-size: 0.6em; /* 图标尺寸 */
  vertical-align: middle;
  animation: pulse-indicator 1.5s infinite ease-in-out; 
}

/* 新增：指示器动画 */
@keyframes pulse-indicator {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 新增：确认按钮样式 */
.acknowledge-button {
  position: absolute;
  top: 10px; /* 需要根据实际效果微调 */
  right: 10px;/* 需要根据实际效果微调 */
  background-color: rgba(200, 200, 200, 0.7); /* 半透明背景 */
  color: #333;
  border: none;
  border-radius: 50%; /* 圆形 */
  width: 22px;
  height: 22px;
  line-height: 22px; /* 图标垂直居中 */
  text-align: center;
  cursor: pointer;
  padding: 0;
  font-size: 10px; /* 图标大小 */
  transition: all 0.2s ease;
  z-index: 5; /* 确保在其他内容之上 */
}

.acknowledge-button:hover {
  background-color: rgba(150, 150, 150, 0.9);
  color: #fff;
  transform: scale(1.1);
} 