.navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: var(--navbar-bg, #ffffff);
  box-shadow: none;
  height: 130px;
  transition: all 0.3s ease;
  border-bottom: none;
  width: 100%;
}

.navbar-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0 2rem;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Logo样式 */
.navbar-logo {
  height: 40px;
  display: flex;
  align-items: center;
}

.navbar-logo img {
  height: 110px;
  object-fit: contain;
}

/* 导航链接样式 */
.navbar-links {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-left: 3rem;
}

.nav-link {
  position: relative;
  color: var(--text-color, #4a5568);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.5rem 0;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: var(--primary-color, #4a6cf7);
}

.nav-link.active {
  color: var(--primary-color, #4a6cf7);
  font-weight: 600;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color, #4a6cf7);
  border-radius: 1px;
  transform: scaleX(0.7);
  transition: transform 0.2s ease;
}

.nav-link.active:hover::after {
  transform: scaleX(1);
}

/* 右侧操作区域 */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 搜索框 */
/* .search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box input {
  background-color: var(--input-bg, #f8fafc);
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  width: 200px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color, #4a6cf7);
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.1);
  width: 250px;
}

.search-icon {
  position: absolute;
  right: 10px;
  color: var(--text-muted, #a0aec0);
  transition: color 0.2s ease;
}

.search-box input:focus + .search-icon {
  color: var(--primary-color, #4a6cf7);
} */

/* 主题切换按钮 */
/* .theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--text-color, #4a5568);
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.theme-toggle:hover {
  background-color: var(--hover-bg, #f8fafc);
} */

/* 用户头像/登录图标 */
.user-section {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding-bottom: 12px; /* 为下拉菜单提供额外空间 */
}

/* 默认头像样式 */
.default-avatar {
  background-color: #f0f2f5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.default-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.default-avatar:active {
  transform: scale(0.95);
}

.default-avatar::after {
  content: '未登录';
  position: absolute;
  bottom: -28px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
  z-index: 10;
}

.default-avatar:hover::after {
  opacity: 1;
  visibility: visible;
}

.login-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color, #4a5568);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--hover-bg, #f8fafc);
  transition: all 0.2s ease;
}

.login-icon:hover {
  color: var(--primary-color, #4a6cf7);
  background-color: rgba(74, 108, 247, 0.1);
}

/* 用户头像容器样式 */
.user-avatar-container {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  border-radius: 50px;
  transition: background-color 0.2s ease;
}

.user-avatar-container:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-avatar-container.menu-open {
  background-color: rgba(0, 0, 0, 0.05);
}

:root.dark-theme .user-avatar-container:hover,
:root.dark-theme .user-avatar-container.menu-open {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 下拉指示器样式 */
.dropdown-indicator {
  margin-left: 4px;
  color: var(--text-muted);
  display: flex;
  align-items: center;
}

.dropdown-indicator svg {
  transition: transform 0.3s ease;
}

.dropdown-indicator svg.rotated {
  transform: rotate(180deg);
}

/* 用户头像样式 */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.user-avatar:active {
  transform: scale(0.95);
}

.user-avatar.active {
  border-color: var(--primary-color, #4a6cf7);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color, #4a6cf7);
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
}

/* 用户下拉菜单 */
.user-dropdown {
  position: absolute;
  top: 40px;
  right: 0;
  width: 250px;
  background-color: var(--dropdown-bg, #ffffff);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: visible;
  z-index: 100;
  transform-origin: top right;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 5px;
}

/* 添加指示箭头 */
.user-dropdown::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 16px;
  width: 16px;
  height: 16px;
  background-color: var(--dropdown-bg, #ffffff);
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  transform: rotate(45deg);
  z-index: -1;
}

/* 过渡动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s cubic-bezier(0.3, 0, 0.2, 1);
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.96);
}

/* 下拉菜单内容样式 */
.dropdown-user-info {
  padding: 1.25rem;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--border-color, #e2e8f0);
  overflow: hidden;
}

/* 菜单项和分隔线 */
.dropdown-item,
.dropdown-divider {
  overflow: hidden; /* 菜单内容保持溢出隐藏 */
}

.dropdown-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 3px solid #fff;
}

.dropdown-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dropdown-avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color, #4a6cf7);
  color: white;
  font-weight: 600;
  font-size: 1.5rem;
}

.dropdown-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-color, #2d3748);
  margin-bottom: 0.25rem;
}

.dropdown-email {
  font-size: 0.85rem;
  color: var(--text-muted, #a0aec0);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color, #e2e8f0);
  margin: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.85rem 1.25rem;
  color: var(--text-color, #4a5568);
  text-decoration: none;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--hover-bg, #f8fafc);
  color: var(--primary-color, #4a6cf7);
}

.dropdown-item:active {
  background-color: rgba(74, 108, 247, 0.05);
}

.dropdown-item svg {
  color: var(--text-muted, #a0aec0);
  transition: color 0.2s ease;
}

.dropdown-item:hover svg {
  color: var(--primary-color, #4a6cf7);
}

.dropdown-item.logout {
  color: #e53e3e;
  border-top: 1px solid var(--border-color, #e2e8f0);
}

.dropdown-item.logout:hover {
  background-color: #fff5f5;
}

.dropdown-item.logout svg {
  color: #e53e3e;
}

/* 暗色主题变量 */
:root {
  --navbar-bg: #ffffff;
  --text-color: #4a5568;
  --text-muted: #a0aec0;
  --primary-color: #4a6cf7;
  --hover-bg: #f8fafc;
  --input-bg: #f8fafc;
  --border-color: #e2e8f0;
  --dropdown-bg: #ffffff;
}

:root.dark-theme {
  --navbar-bg: #1a202c;
  --text-color: #e2e8f0;
  --text-muted: #718096;
  --primary-color: #5a78ff;
  --hover-bg: #2d3748;
  --input-bg: #2d3748;
  --border-color: #4a5568;
  --dropdown-bg: #2d3748;
}

/* 暗色主题样式覆盖 */
:root.dark-theme .navbar {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

:root.dark-theme .search-box input {
  color: var(--text-color);
}

/* 动画和过渡效果 */
.theme-toggle svg {
  transition: transform 0.5s ease;
}

.theme-toggle:hover svg {
  transform: rotate(30deg);
} 