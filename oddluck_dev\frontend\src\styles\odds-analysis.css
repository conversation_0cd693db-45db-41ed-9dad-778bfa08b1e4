.analysis-container {
  width: 100%;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  animation: fade-in 0.5s ease-in-out;
  height: fit-content;
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 12px;
  padding-bottom: 10px;
}

.title-action-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.llm-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 300px;
  max-width: 450px;
}

.llm-select {
  min-width: 220px;
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background-color: white;
  font-size: 0.9rem;
  color: #374151;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
}

.llm-select:hover:not(:disabled) {
  border-color: #cbd5e1;
  background-color: #f1f5f9;
}

.llm-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.llm-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f1f5f9;
}

/* 改进选项的样式 */
.llm-select option {
  padding: 8px 12px;
  font-size: 13px;
  line-height: 1.4;
  white-space: normal;
  word-break: normal;
  background-color: #ffffff;
  color: #1e293b;
  border-bottom: 1px solid #f1f5f9;
}

.llm-select option:hover,
.llm-select option:focus {
  background-color: #f8fafc;
  color: #4f46e5;
}

.toggle-control {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 流式输出开关样式优化 */
.stream-toggle-control {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.stream-toggle-control::before {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #10b981;
  left: -14px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stream-toggle-control input:checked ~ .stream-toggle-control::before {
  opacity: 1;
}

.stream-switch {
  transform: scale(0.85);
  transform-origin: left center;
}

/* 开关激活颜色调整 */
.stream-switch input:checked + .slider {
  background-color: var(--primary-color, #4f46e5);
}

.stream-switch input:focus + .slider {
  box-shadow: 0 0 0 2px var(--primary-focus-ring-color, rgba(79, 70, 229, 0.3));
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e1;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #10b981;
}

input:focus + .slider {
  box-shadow: 0 0 1px #10b981;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/* 状态指示点 */
.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.active {
  background-color: #10b981; /* 绿色 - 活跃 */
  animation: pulse-green 2s infinite;
}

.status-dot.inactive {
  background-color: #ef4444; /* 红色 - 未活跃 */
}

.status-dot.activating {
  background-color: #f59e0b; /* 橙色 - 正在激活 */
  animation: pulse-orange 1s infinite;
}

.status-dot.loading {
  background-color: #64748b; /* 灰色 - 加载中 */
  animation: pulse-loading 1.5s infinite;
}

@keyframes pulse-green {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes pulse-orange {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(245, 158, 11, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

@keyframes pulse-loading {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.control-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  padding: 6px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  min-width: 90px;
  text-align: center;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* standard-btn样式 - 用于标准按钮 */
.standard-btn {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.standard-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.primary-btn {
  background-color: #4f46e5;
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
  z-index: 1;
}

.primary-btn:hover:not(:disabled) {
  background-color: #4338ca;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.primary-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
  z-index: -1;
}

.primary-btn:hover:not(:disabled)::before {
  left: 100%;
}

.secondary-btn {
  background-color: #f59e0b;
  color: white;
}

.secondary-btn:hover {
  background-color: #d97706;
}

.danger-btn {
  background-color: #ef4444;
  color: white;
}

.danger-btn:hover {
  background-color: #dc2626;
}

.analyzing-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
}

.analysis-status-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

.analyzing-text {
  color: #4f46e5;
  font-size: 1rem;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.error-state {
  padding: 20px;
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: 8px;
  color: #ef4444;
  margin-bottom: 16px;
  text-align: center;
}

.empty-state {
  padding: 30px;
  text-align: center;
  color: #94a3b8;
}

.empty-guide {
  max-width: 500px;
  margin: 0 auto;
  text-align: left;
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.guide-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  text-align: center;
}

.guide-steps {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: #4f46e5;
  color: white;
  font-weight: 600;
  border-radius: 50%;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content strong {
  display: block;
  margin-bottom: 4px;
  color: #1e293b;
}

.step-content p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #64748b;
}

.guide-note {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background-color: #eff6ff;
  border-radius: 8px;
  font-size: 0.85rem;
  color: #3b82f6;
}

.guide-note svg {
  margin-top: 2px;
  flex-shrink: 0;
}

.guide-note p {
  margin: 0;
  line-height: 1.4;
}

.analysis-result {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex-grow: 1;
  overflow-y: auto;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.analysis-content-section {
  padding: 8px 0;
}

.analysis-content {
  line-height: 1.6;
  color: #334155;
  white-space: pre-wrap;
  word-wrap: break-word;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 100px;
}

.knowledge-section {
  margin-top: 24px;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.knowledge-list {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.knowledge-item {
  padding: 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #edf2f7;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.knowledge-item:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border-color: #cbd5e0;
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.knowledge-title {
  font-weight: 600;
  color: #2563eb;
}

.knowledge-similarity {
  font-size: 0.8rem;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 2px 8px;
  border-radius: 12px;
}

.knowledge-preview {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  max-height: 100px;
  overflow: hidden;
  position: relative;
}

.knowledge-preview::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
}

.knowledge-usage-tip {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
  color: #0369a1;
  font-size: 0.85rem;
}

/* 在分析内容中高亮显示引用的文档 */
.analysis-content strong {
  color: #2563eb;
  font-weight: 600;
}

.analysis-content em {
  color: #0369a1;
  font-style: normal;
  text-decoration: underline;
}

/* 特别突出《引用文档》 */
.analysis-content span.reference {
  color: #2563eb;
  font-weight: 600;
  background-color: #e0e7ff;
  padding: 0.1em 0.3em;
  border-radius: 3px;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .analysis-container {
    background-color: #1f2937;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  .analysis-header {
    border-color: #374151;
  }
  
  .analysis-title-hint {
    color: #94a3b8;
  }
  
  .llm-select {
    background-color: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
  }
  
  .slider {
    background-color: #4b5563;
  }
  
  input:checked + .slider {
    background-color: #10b981;
  }
  
  .section-title {
    color: #e5e7eb;
    border-color: #374151;
  }
  
  .spinner {
    border-color: #374151;
    border-top-color: #818cf8;
  }
  
  .analyzing-text {
    color: #818cf8;
  }
  
  .error-state {
    background-color: #471a1a;
    border-color: #771d1d;
    color: #fca5a5;
  }
  
  .empty-state {
    color: #64748b;
  }
  
  .knowledge-item {
    background-color: #2d3748;
    color: #cbd5e1;
  }
  
  .knowledge-section {
    border-color: #374151;
  }
  
  .analysis-content {
    color: #d1d5db;
  }
}

.auth-error {
  padding: 20px;
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: 8px;
  color: #ef4444;
  margin-bottom: 16px;
  text-align: center;
}

.login-btn {
  background-color: #4f46e5;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.login-btn:hover {
  background-color: #4338ca;
}

/* 分析进度指示器样式 */
.analysis-progress-container {
  width: 100%;
  margin-top: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.analysis-progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-section {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.progress-title {
  font-weight: 600;
  font-size: 0.95rem;
  color: #374151;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  color: #6b7280;
}

.progress-indicator.complete .indicator-dot {
  background-color: #10b981;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #6b7280;
}

.indicator-dot.pulse {
  background-color: #3b82f6;
  animation: pulse-blue 2s infinite;
}

@keyframes pulse-blue {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.progress-content {
  padding: 16px;
  max-height: 300px;
  overflow: auto;
}

.scrollable-content {
  font-size: 0.9rem;
  line-height: 1.6;
  white-space: pre-line;
  color: #374151;
  overflow-wrap: break-word;
}

/* 知识点展示样式 */
.knowledge-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 16px 16px;
}

.knowledge-chip {
  padding: 4px 10px;
  background-color: #f3f4f6;
  border-radius: 16px;
  font-size: 0.8rem;
  color: #4b5563;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.2s ease;
}

.knowledge-chip:hover {
  background-color: #e5e7eb;
}

.knowledge-chip.more {
  background-color: #dbeafe;
  color: #2563eb;
}

.knowledge-count {
  font-size: 0.8rem;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

/* 初始化等待状态 */
.progress-initializing {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background-color: #f9fafb;
  border-radius: 10px;
  border: 1px dashed #d1d5db;
}

.initializing-animation {
  width: 50px;
  height: 50px;
  position: relative;
  margin-bottom: 20px;
}

.initializing-animation:before,
.initializing-animation:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #60a5fa;
  opacity: 0.6;
  animation: pulse-init 2s infinite ease-in-out;
}

.initializing-animation:after {
  animation-delay: -1s;
}

@keyframes pulse-init {
  0%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0;
  }
}

.progress-initializing p {
  font-size: 0.95rem;
  color: #4b5563;
  margin: 0;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .progress-section {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .progress-header {
    background-color: #111827;
    border-color: #374151;
  }
  
  .progress-title {
    color: #e5e7eb;
  }
  
  .progress-indicator {
    color: #9ca3af;
  }
  
  .scrollable-content {
    color: #d1d5db;
  }
  
  .knowledge-chip {
    background-color: #374151;
    color: #e5e7eb;
  }
  
  .knowledge-chip:hover {
    background-color: #4b5563;
  }
  
  .knowledge-chip.more {
    background-color: #1e40af;
    color: #bfdbfe;
  }
  
  .knowledge-count {
    background-color: #374151;
    color: #d1d5db;
  }
  
  .progress-initializing {
    background-color: #111827;
    border-color: #374151;
  }
  
  .progress-initializing p {
    color: #e5e7eb;
  }
}

/* 新的历史选择器行样式 */
.history-selector-row {
  padding: 8px 20px;
  display: flex;
  justify-content: flex-end;
  border-bottom: none; /* 移除分割线 */
}

.report-history-dropdown {
  width: auto; /* 宽度自适应内容 */
  max-width: 320px; 
}

.history-select {
  width: 100%;
  padding: 8px 32px 8px 12px; /* 增加右侧内边距 (原为 12px) */
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background-color: white;
  font-size: 0.9rem;
  color: #374151;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  overflow: hidden;         /* 隐藏溢出的文本 */
  text-overflow: ellipsis; /* 使用省略号表示溢出 */
  white-space: nowrap;     /* 确保文本不换行 */
}

/* 优化继续生成按钮样式 */
.continue-btn {
  margin-left: 10px;
  font-size: 13px;
  padding: 6px 16px;
  background-color: #2563eb;
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
  z-index: 1;
}

.continue-btn:hover {
  background-color: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.continue-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.continue-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
  z-index: -1;
}

.continue-btn:hover::before {
  left: 100%;
}

/* 小按钮样式 */
.small-btn {
  padding: 6px 12px;
  font-size: 13px;
}

/* 调整分析容器宽度 */
.analysis-container {
  padding: 16px;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analysis-content {
  line-height: 1.6;
  color: #1f2937;
  max-height: 600px;
  overflow-y: auto;
  padding-right: 10px;
  text-align: justify;
}

/* 调试内容样式 */
.debug-text {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  color: #1f2937;
  max-height: 600px;
  overflow-y: auto;
}

/* 错误信息样式 */
.empty-analysis-message {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 6px;
  margin-top: 12px;
}

/* 分析状态 */
.analyzing-state {
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  margin-top: 20px;
}

.progress-initializing {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
}

.initializing-animation {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(99, 102, 241, 0.2);
  border-radius: 50%;
  border-top-color: #6366f1;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 等待提示样式 */
.wait-hint {
  color: #6b7280;
  font-size: 0.9rem;
  margin-top: 12px;
  font-style: italic;
}

/* 按钮样式优化 */
.action-btn {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.secondary-btn {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.secondary-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.standard-btn {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.standard-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.danger-btn {
  background-color: #ef4444;
  color: white;
  border: none;
}

.danger-btn:hover:not(:disabled) {
  background-color: #dc2626;
}

/* 标题样式 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

/* 页面布局 */
.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-action-group {
  display: flex;
  align-items: center;
}

/* 调试内容样式 */
.debug-text {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  color: #1f2937;
  max-height: 600px;
  overflow-y: auto;
}

/* 错误信息样式 */
.empty-analysis-message {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 6px;
  margin-top: 12px;
}

/* 按钮点击波纹效果 */
@keyframes ripple {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .continue-btn {
    background-color: #3b82f6;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  }
  
  .continue-btn:hover {
    background-color: #2563eb;
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  }
  
  .primary-btn {
    background-color: #6366f1;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
  }
  
  .primary-btn:hover:not(:disabled) {
    background-color: #4f46e5;
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.4);
  }
}

/* 波纹效果样式 - 从OddsAnalysis.vue迁移过来 */
.action-btn {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}

/* 流式输出样式优化 - 更新现有样式 */
.stream-toggle-control {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

/* 添加流式输出状态文本样式 */
.stream-status {
  margin-left: 8px;
  font-size: 0.85rem;
  color: var(--text-secondary-color);
  white-space: nowrap;
  min-width: 85px;
  font-weight: 500;
}

/* 高亮当前状态 */
.stream-status:has(+ input[type="checkbox"]:checked) {
  color: var(--primary-color);
}

/* 调整开关大小以匹配按钮高度 */
.stream-switch {
  transform: scale(0.85);
  transform-origin: left center;
}

/* 标题操作组样式 */
.title-action-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 开关样式 */
.stream-switch input:checked + .slider {
  background-color: var(--primary-color, #4f46e5);
}

.stream-switch input:focus + .slider {
  box-shadow: 0 0 0 2px var(--primary-focus-ring-color, rgba(79, 70, 229, 0.3));
}

/* 确保右侧控件正确对齐 */
.right-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.llm-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 300px;
  max-width: 450px;
}

/* Styles migrated from OddsAnalysis.vue <style scoped> */
.header-loading-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 15px; /* Adjust spacing as needed */
}

.header-loading-indicator .spinner {
  width: 18px; /* Slightly larger */
  height: 18px;
  border: 2px solid rgba(99, 102, 241, 0.3); /* Indigo lighter */
  border-radius: 50%;
  border-top-color: #6366f1; /* Indigo */
  animation: spin 1s ease-in-out infinite;
}

.initial-loading-text {
   color: #888; /* Lighter color */
   font-style: italic;
   padding: 25px 15px; /* More padding */
   text-align: center;
   border: 1px dashed #e0e0e0; /* Optional border */
   border-radius: 4px;
   margin-top: 10px;
}

.analysis-content-area { /* Optional: Ensure consistent spacing */
  margin-top: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Ensure existing styles are present or adjust as needed */
.analysis-container {
  /* ... existing styles ... */
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure container takes height */
}

.analysis-header {
  /* ... existing styles ... */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.right-controls {
    display: flex;
    align-items: center;
}

.analysis-result {
  /* ... existing styles ... */
   flex-grow: 1; /* Allow result area to take available space */
   overflow-y: auto; /* Add scroll if content overflows */
}

.analysis-content-section {
    /* ... existing styles ... */
}

.analysis-content {
    /* ... existing styles ... */
    white-space: pre-wrap; /* Ensure line breaks are respected */
    word-wrap: break-word; /* Break long words */
    padding: 10px;
    background-color: #f9f9f9; /* Light background for content */
    border-radius: 4px;
    min-height: 100px; /* Ensure it has some height even when empty initially */
}

.reference { /* Style for highlighted references */
  background-color: #e0e7ff; /* Light indigo background */
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-style: italic;
}

.error-state, .empty-state, .empty-analysis-message {
    text-align: center;
    padding: 30px;
    color: #666;
}

/* ... other existing styles ... */

/* 流式输出指示器 */
.stream-indicator {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  background-color: rgba(99, 102, 241, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.stream-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #6366f1;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(99, 102, 241, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
} 