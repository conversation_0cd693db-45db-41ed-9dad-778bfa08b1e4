.odds-container {
  width: 100%;
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  padding: 20px 10px; /* 添加少量左右内边距 */
  max-width: 100%;
  margin: 0 auto;
  overflow-y: auto;
  overflow-x: hidden; /* 隐藏水平滚动条 */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  flex: 1;
}

.odds-container.with-analysis {
  flex: 0 0 45%; /* 当分析面板打开时，减少宽度至45%（原来是60%） */
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-actions {
  display: flex;
  gap: 10px;
}

.analysis-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background-color: #4f46e5;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analysis-button:hover {
  background-color: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.analysis-button.has-report {
  background-color: #10b981;
}

.analysis-button.has-report:hover {
  background-color: #059669;
}

.report-icon {
  display: flex;
  align-items: center;
}

.match-header {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
  
.league-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}
  
.league-logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
}
  
.league-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
}
  
.match-time {
  color: #64748b;
  font-size: 0.9rem;
}
  
.teams-container {
  background: white;
  border-radius: 12px;
  padding: 30px 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  position: relative; /* 添加相对定位 */
}
  
.team {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}
  
.team.home {
  text-align: right;
}
  
.team.away {
  text-align: left;
}
  
.team-logo {
  width: 66px;
  height: 66px;
  display: flex;
  align-items: center;
  justify-content: center;
}
  
.team-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
  
.team-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: #334155;
}
  
.match-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  min-width: 120px;
}
  
.match-time {
  font-size: 0.9rem;
  color: #64748b;
  text-align: center;
}
  
.score {
  font-size: 2rem;
  font-weight: 600;
  color: #ff0000;
  text-align: center;
}

/* vs样式 */
.vs {
  color: #0f172a;
}

/* 赔率表格样式 */
.odds-table-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow-x: hidden; /* 隐藏水平溢出 */
  overflow-y: visible; /* 允许垂直内容流动 */
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
}

.odds-table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
  table-layout: fixed; /* 添加固定表格布局，更好地控制列宽 */
}

.odds-table th {
  background-color: #f1f5f9;
  padding: 12px;
  font-weight: 600;
  color: #334155;
  border-bottom: 2px solid #e2e8f0;
}

.odds-table td {
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
  color: #334155;
}

/* 设置各列的宽度 */
.odds-table th:nth-child(1), .odds-table td:nth-child(1) { /* 机构列 */
  width: 20%;
}

.odds-table th:nth-child(2), .odds-table td:nth-child(2),
.odds-table th:nth-child(3), .odds-table td:nth-child(3),
.odds-table th:nth-child(4), .odds-table td:nth-child(4) { /* 主胜、平局、客胜列 */
  width: 15%;
}

.odds-table th:nth-child(5), .odds-table td:nth-child(5) { /* 更新时间列 */
  width: 25%;
}

.odds-table th:nth-child(6), .odds-table td:nth-child(6) { /* 操作列 */
  width: 10%;
}

/* 赔率变化样式 */
.odds-increased {
  color: #ef4444 !important; /* 红色 - 赔率增加 */
  font-weight: 600;
}

.odds-decreased {
  color: #10b981 !important; /* 绿色 - 赔率减少 */
  font-weight: 600;
}

/* 抽屉式展开样式 */
.latest-odds {
  background-color: #f8fafc;
}

.earliest-odds {
  background-color: #f1f5f9;
}

.separator-row {
  height: 20px;
  background-color: transparent;
}

.separator-row td {
  border-bottom: none;
}

.action-cell {
  width: 40px;
  vertical-align: middle;
}

.drawer-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  font-size: 12px;
  color: #6b7280;
  border-radius: 4px;
  transition: all 0.2s;
}

.drawer-toggle:hover {
  background-color: #e2e8f0;
  color: #334155;
}

.drawer-container {
  background-color: #f8fafc;
}

.drawer-content {
  padding: 0;
  border: none;
  overflow: visible;
}

.drawer {
  overflow: visible;
  transition: all 0.3s ease-in-out;
  animation: drawer-open 0.3s ease-in-out forwards;
  transform-origin: top;
  max-height: none;
  width: 100%;
}

@keyframes drawer-open {
  from {
    max-height: 0;
    opacity: 0;
    transform: scaleY(0);
    overflow: hidden;
  }
  to {
    max-height: none;
    opacity: 1;
    transform: scaleY(1);
    overflow: visible;
  }
}

.inner-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  table-layout: fixed;
}

.inner-table th {
  background-color: #f8fafc;
  padding: 10px;
  font-weight: 500;
  color: #475569;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.9rem;
}

.inner-table td {
  padding: 10px;
  border-bottom: 1px solid #e2e8f0;
  color: #334155;
}

.inner-table tr:last-child td {
  border-bottom: none;
}

/* 设置内部表格各列的宽度 */
.inner-table th:nth-child(1), .inner-table td:nth-child(1),
.inner-table th:nth-child(2), .inner-table td:nth-child(2),
.inner-table th:nth-child(3), .inner-table td:nth-child(3) { /* 主胜、平局、客胜列 */
  width: 20%;
}

.inner-table th:nth-child(4), .inner-table td:nth-child(4) { /* 更新时间列 */
  width: 40%;
}

.no-odds-message {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #6b7280;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 500px; /* 增加高度使动画在页面中间显示 */
  text-align: center;
  margin: 0 auto;
  width: 100%;
}

.loading-animation {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.loading-ball {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #4f46e5;
  animation: bounce 0.7s infinite alternate;
}

.loading-ball-1 {
  animation-delay: 0s;
  background-color: #4f46e5; /* 紫色 */
}

.loading-ball-2 {
  animation-delay: 0.2s;
  background-color: #3b82f6; /* 蓝色 */
}

.loading-ball-3 {
  animation-delay: 0.4s; 
  background-color: #10b981; /* 绿色 */
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-20px);
  }
}

.loading-text {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #1e293b;
}

.loading-subtext {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 0;
}

.odds {
  padding: 20px 0;
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

.actions {
  margin-top: 20px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background-color: #4338ca;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .match-header,
  .teams-container,
  .odds-table-container,
  .no-odds-message {
    background: #1f2937;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .league-name {
    color: #f3f4f6;
  }

  .match-time {
    color: #9ca3af;
  }

  .team-name {
    color: #e5e7eb;
  }

  .score {
    color: #ff0000;
  }
  
  .vs {
    color: #0f172a;
  }

  .odds-table th {
    background-color: #374151;
    color: #e5e7eb;
    border-color: #4b5563;
  }

  .odds-table td {
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .no-odds-message {
    color: #9ca3af;
  }
  
  .latest-odds {
    background-color: #1e293b;
  }

  .earliest-odds {
    background-color: #111827;
  }
  
  .drawer-container {
    background-color: #1e293b;
  }
  
  .drawer-toggle {
    color: #9ca3af;
  }
  
  .drawer-toggle:hover {
    background-color: #374151;
    color: #e5e7eb;
  }
  
  .inner-table th {
    background-color: #1e293b;
    color: #cbd5e1;
    border-color: #4b5563;
  }
  
  .inner-table td {
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .loading-text {
    color: #f1f5f9;
  }
  
  .loading-subtext {
    color: #94a3b8;
  }
}

/* 错误状态样式 */
.error-state, .no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background-color: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  margin: 40px auto;
  max-width: 500px;
}

@media (prefers-color-scheme: dark) {
  .error-state, .no-data-state {
    background-color: #1e293b;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}

.error-state .error-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ef4444;
}

.error-state .error-message {
  font-size: 18px;
  margin-bottom: 20px;
  color: #1e293b;
}

/* 无数据状态样式 */
.no-data-state .empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #6b7280;
}

.no-data-state .empty-message {
  font-size: 18px;
  margin-bottom: 20px;
  color: #1e293b;
}

/* 深色模式下的错误和无数据状态 */
@media (prefers-color-scheme: dark) {
  .error-state .error-message,
  .no-data-state .empty-message {
    color: #f1f5f9;
  }
}

/* 重试按钮样式 */
.retry-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.retry-button:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
  .retry-button {
    background-color: #4f46e5;
  }
  
  .retry-button:hover {
    background-color: #4338ca;
  }
}

.error-state {
  color: #ef4444;
}

.no-data-state {
  color: #6b7280;
} 