.odds-page {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 64px);
  background-color: #f1f5f9;
  overflow-x: hidden; /* 只隐藏水平溢出 */
  overflow-y: visible; /* 允许垂直内容正常流动 */
  align-items: center; /* 添加居中对齐 */
  width: 100%; /* 确保占满整个宽度 */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
}

.odds-container {
  display: flex;
  flex-direction: row; /* 改为横向布局 */
  width: 100%;
  min-height: calc(100vh - 64px);
  padding: 0;
  position: relative;
  overflow: auto; /* 允许滚动 */
  max-width: 1600px; /* 增加最大宽度，从1400px到1600px */
  margin: 0; /* 重置边距 */
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  gap: 20px; /* 添加两栏之间的间距 */
}

.odds-container.with-analysis {
  max-width: 1600px; /* 有分析时再增加容器宽度，从1400px到1600px */
}

.analysis-container {
  flex: 1.3; /* 增大占比，从1.2增加到1.3 */
  min-width: 480px; /* 增加最小宽度，从420px到480px */
  margin-top: 0; /* 移除上边距 */
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  transform-origin: left; /* 从左侧展开 */
  animation: slide-in-right 0.3s forwards; /* 添加从右侧滑入的动画 */
  align-self: flex-start; /* 确保顶部对齐 */
  position: sticky; /* 使分析报告固定在视口中 */
  top: 20px; /* 距离顶部20px */
  max-height: calc(100vh - 84px); /* 限制最大高度 */
  overflow-y: auto; /* 超出部分可滚动 */
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .odds-page {
    background-color: #0f172a;
  }
}

/* 流式输出活动状态 */
body.streaming-active .analysis-container {
  position: relative;
}

body.streaming-active .analysis-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #6366f1, #8b5cf6, #6366f1);
  background-size: 200% 100%;
  animation: gradient-flow 2s linear infinite;
  z-index: 10;
}

@keyframes gradient-flow {
  0% {
    background-position: 100% 0%;
  }
  100% {
    background-position: 0% 0%;
  }
}

/* 流式输出片段样式 */
.stream-chunk {
  display: inline;
  animation: fade-in 0.3s ease-in-out;
}

@keyframes fade-in {
  from { opacity: 0.5; }
  to { opacity: 1; }
} 