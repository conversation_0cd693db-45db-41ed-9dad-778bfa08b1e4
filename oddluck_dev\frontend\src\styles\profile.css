/* 用户档案页面样式 */
.profile-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.profile-header {
  margin-bottom: 2rem;
  text-align: center;
}

.profile-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color, #2d3748);
  margin-bottom: 0.5rem;
}

.profile-header p {
  color: var(--text-muted, #718096);
  font-size: 1.1rem;
}

.profile-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  padding: 2rem;
  background-color: rgba(80, 110, 255, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 2rem;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 4px solid white;
  margin-bottom: 1rem;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color, #4a6cf7);
  color: white;
  font-weight: 600;
  font-size: 3rem;
}

.change-avatar-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  border: 1px solid var(--primary-color, #4a6cf7);
  color: var(--primary-color, #4a6cf7);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.change-avatar-btn:hover {
  background-color: rgba(74, 108, 247, 0.05);
}

.profile-info {
  flex: 1;
}

.profile-info h2 {
  margin-bottom: 0.25rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color, #2d3748);
}

.user-email {
  color: var(--text-muted, #718096);
  margin-bottom: 0.5rem;
}

.join-date, .last-login, .last-updated {
  font-size: 0.85rem;
  color: var(--text-light, #a0aec0);
  margin: 0.2rem 0;
}

.profile-form {
  padding: 2rem;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color, #2d3748);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color, #2d3748);
}

.form-input-wrapper {
  position: relative;
}

.form-input-wrapper input,
.form-input-wrapper textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
  background-color: #fff;
}

.form-input-wrapper input:focus,
.form-input-wrapper textarea:focus {
  outline: none;
  border-color: var(--primary-color, #4a6cf7);
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.1);
}

.form-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e2e8f0;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color, #4a6cf7);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color, #4a6cf7);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background-color: transparent;
  border: 1px solid var(--border-color, #e2e8f0);
  color: var(--text-color, #4a5568);
}

.cancel-btn:hover {
  background-color: var(--hover-bg, #f8fafc);
}

.save-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--primary-color, #4a6cf7);
  border: none;
  color: white;
}

.save-btn:hover {
  background-color: rgba(74, 108, 247, 0.9);
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 消息提示样式 */
.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  animation: slide-in 0.3s ease-out forwards;
}

.success {
  background-color: #10b981;
  color: white;
}

.error {
  background-color: #ef4444;
  color: white;
}

.info {
  background-color: #3b82f6;
  color: white;
}

@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profile-avatar-wrapper {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }
  
  .profile-form {
    padding: 1.5rem;
  }
} 