/* 地区导航组件样式 */
.region-navigator {
  width: 100%;
  height: 100%;
  background: var(--bg-secondary, #ffffff);
  border-right: 1px solid var(--border-color, #e5e7eb);
  overflow-y: auto;
  font-size: 14px;
}

.navigator-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-primary, #f9fafb);
}

.navigator-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #111827);
}

.navigator-content {
  padding: 8px 0;
}

/* 地区组 */
.region-group {
  margin-bottom: 4px;
}

.region-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.region-header:hover {
  background: var(--bg-hover, #f3f4f6);
}

.region-header.expanded {
  background: var(--bg-active, #e5e7eb);
  font-weight: 500;
}

.expand-icon {
  width: 12px;
  height: 12px;
  margin-right: 8px;
  transition: transform 0.2s ease;
  color: var(--text-secondary, #6b7280);
}

.expand-icon.rotated {
  transform: rotate(90deg);
}

.region-name {
  flex: 1;
  font-weight: 500;
  color: var(--text-primary, #111827);
}

.country-count {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  margin-left: 4px;
}

/* 国家列表 */
.countries-list {
  background: var(--bg-tertiary, #f9fafb);
  border-left: 2px solid var(--border-accent, #d1d5db);
  margin-left: 16px;
}

.country-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.country-item:hover {
  background: var(--bg-hover, #f3f4f6);
}

.country-item.selected {
  background: var(--bg-selected, #dbeafe);
  border-left-color: var(--accent-color, #3b82f6);
  font-weight: 500;
}

.country-flag {
  width: 16px;
  height: 12px;
  margin-right: 8px;
  object-fit: cover;
  border-radius: 2px;
  border: 1px solid var(--border-color, #e5e7eb);
}

.country-name {
  color: var(--text-primary, #111827);
  font-size: 13px;
}

/* 国际赛事特殊样式 */
.international-item {
  font-style: italic;
}

.international-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: var(--accent-color, #3b82f6);
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
  color: var(--text-secondary, #6b7280);
}

.loading-container svg,
.error-container svg {
  margin-bottom: 8px;
  font-size: 20px;
}

.retry-btn {
  margin-top: 8px;
  padding: 6px 12px;
  background: var(--accent-color, #3b82f6);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: var(--accent-hover, #2563eb);
}

/* 过渡动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 500px;
  opacity: 1;
}

/* 滚动条样式 */
.region-navigator::-webkit-scrollbar {
  width: 6px;
}

.region-navigator::-webkit-scrollbar-track {
  background: var(--bg-primary, #f9fafb);
}

.region-navigator::-webkit-scrollbar-thumb {
  background: var(--border-color, #e5e7eb);
  border-radius: 3px;
}

.region-navigator::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary, #6b7280);
}

/* 深色主题支持 */
.dark-theme .region-navigator {
  background: var(--bg-secondary-dark, #1f2937);
  border-right-color: var(--border-color-dark, #374151);
}

.dark-theme .navigator-header {
  background: var(--bg-primary-dark, #111827);
  border-bottom-color: var(--border-color-dark, #374151);
}

.dark-theme .navigator-header h3 {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .region-header:hover {
  background: var(--bg-hover-dark, #374151);
}

.dark-theme .region-header.expanded {
  background: var(--bg-active-dark, #4b5563);
}

.dark-theme .region-name {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .country-count {
  color: var(--text-secondary-dark, #9ca3af);
}

.dark-theme .countries-list {
  background: var(--bg-tertiary-dark, #111827);
  border-left-color: var(--border-accent-dark, #4b5563);
}

.dark-theme .country-item:hover {
  background: var(--bg-hover-dark, #374151);
}

.dark-theme .country-item.selected {
  background: var(--bg-selected-dark, #1e3a8a);
}

.dark-theme .country-name {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .loading-container,
.dark-theme .error-container {
  color: var(--text-secondary-dark, #9ca3af);
}
