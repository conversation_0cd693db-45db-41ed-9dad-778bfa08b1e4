.spider-log-viewer {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  min-height: 500px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
}

.log-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
}

.log-status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 0.875rem;
  padding: 4px 12px;
  border-radius: 16px;
  background-color: #f8fafc;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.status-text {
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #cbd5e1;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
}

.status-running {
  background-color: #e0f2fe;
  border: 1px solid #bae6fd;
  padding: 4px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 5px rgba(2, 132, 199, 0.2);
}

.status-running .status-text {
  color: #0369a1;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.status-running .status-indicator {
  background-color: #0284c7;
  width: 10px;
  height: 10px;
  margin-right: 4px;
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 0 rgba(2, 132, 199, 0.4);
}

.status-completed {
  background-color: #dcfce7;
  border: 1px solid #86efac;
  padding: 4px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 5px rgba(22, 163, 74, 0.2);
}

.status-completed .status-text {
  color: #166534;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.status-completed .status-indicator {
  background-color: #16a34a;
  width: 10px;
  height: 10px;
  box-shadow: 0 0 0 2px rgba(22, 163, 74, 0.2);
}

.status-failed .status-text {
  color: #b91c1c;
}

.status-failed .status-indicator {
  background-color: #ef4444;
}

.status-paused .status-text {
  color: #ca8a04;
}

.status-paused .status-indicator {
  background-color: #eab308;
}

.log-info {
  display: flex;
  flex-wrap: wrap;
  padding: 12px 16px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 8px;
  font-size: 0.875rem;
}

.info-item {
  display: flex;
  gap: 4px;
  margin-right: 12px;
}

.full-width {
  width: 100%;
  margin-top: 4px;
}

.label {
  font-weight: 600;
  color: #64748b;
}

.value {
  color: #334155;
}

.progress-container {
  padding: 12px 16px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.progress-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 4px;
}

.progress-bar {
  height: 8px;
  background-color: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background-color: #3b82f6;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
  animation: progress-animation 1s linear infinite;
  transition: width 0.3s ease;
}

.log-actions {
  display: flex;
  padding: 12px 16px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 8px;
  flex-wrap: wrap;
}

.log-actions button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #e2e8f0;
  color: #334155;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.log-actions button:hover {
  background-color: #cbd5e1;
}

.refresh-btn {
  background-color: #4f46e5 !important;
  color: #c7d2fe !important;
}

.refresh-btn:hover {
  background-color: #4338ca !important;
}

.pause-btn {
  background-color: #b45309 !important;
  color: #fef3c7 !important;
}

.pause-btn:hover {
  background-color: #d97706 !important;
}

.resume-btn {
  background-color: #166534 !important;
  color: #d1fae5 !important;
}

.resume-btn:hover {
  background-color: #15803d !important;
}

.terminate-btn {
  background-color: #b91c1c !important;
  color: #fee2e2 !important;
}

.terminate-btn:hover {
  background-color: #dc2626 !important;
}

.copy-btn {
  background-color: #0c4a6e !important;
  color: #bae6fd !important;
}

.copy-btn:hover {
  background-color: #0ea5e9 !important;
}

.download-btn {
  background-color: #0c4a6e !important;
  color: #bae6fd !important;
}

.download-btn:hover {
  background-color: #0ea5e9 !important;
}

.log-content-wrapper {
  flex: 1;
  position: relative;
}

.log-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  background-color: #0f172a;
  padding: 16px;
  font-family: 'Courier New', monospace;
}

.log-content pre {
  margin: 0;
  color: #f8fafc;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(2, 132, 199, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(2, 132, 199, 0);
    transform: scale(1.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(2, 132, 199, 0);
    transform: scale(1);
  }
}

@keyframes progress-animation {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 1rem 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .spider-log-viewer {
    background-color: #1e293b;
  }
  
  .log-header {
    background-color: #0f172a;
    border-bottom-color: #334155;
  }
  
  .log-title {
    color: #f1f5f9;
  }
  
  .log-info {
    background-color: #0f172a;
    border-bottom-color: #334155;
  }
  
  .label {
    color: #94a3b8;
  }
  
  .value {
    color: #e2e8f0;
  }
  
  .progress-container {
    border-bottom-color: #334155;
  }
  
  .progress-bar {
    background-color: #334155;
  }
  
  .log-actions {
    background-color: #0f172a;
    border-bottom-color: #334155;
  }
  
  .log-actions button {
    background-color: #334155;
    color: #e2e8f0;
  }
  
  .log-actions button:hover {
    background-color: #475569;
  }
  
  .refresh-btn {
    background-color: #312e81 !important;
    color: #a5b4fc !important;
  }
  
  .refresh-btn:hover {
    background-color: #3730a3 !important;
  }
  
  .pause-btn {
    background-color: #78350f !important;
    color: #fcd34d !important;
  }
  
  .pause-btn:hover {
    background-color: #92400e !important;
  }
  
  .resume-btn {
    background-color: #14532d !important;
    color: #86efac !important;
  }
  
  .resume-btn:hover {
    background-color: #166534 !important;
  }
  
  .terminate-btn {
    background-color: #7f1d1d !important;
    color: #fca5a5 !important;
  }
  
  .terminate-btn:hover {
    background-color: #991b1b !important;
  }
  
  .copy-btn {
    background-color: #0c4a6e !important;
    color: #7dd3fc !important;
  }
  
  .copy-btn:hover {
    background-color: #075985 !important;
  }
  
  .download-btn {
    background-color: #0c4a6e !important;
    color: #7dd3fc !important;
  }
  
  .download-btn:hover {
    background-color: #075985 !important;
  }
  
  .log-content {
    background-color: #020617;
  }
  
  .status-running {
    background-color: rgba(2, 132, 199, 0.25);
    border: 1px solid rgba(2, 132, 199, 0.6);
    box-shadow: 0 2px 5px rgba(2, 132, 199, 0.3);
  }
  
  .status-running .status-text {
    color: #38bdf8;
    text-shadow: 0 0 5px rgba(56, 189, 248, 0.3);
  }
  
  .status-running .status-indicator {
    background-color: #0ea5e9;
    box-shadow: 0 0 5px #0ea5e9;
  }
  
  .status-completed {
    background-color: rgba(22, 163, 74, 0.2);
    border: 1px solid rgba(22, 163, 74, 0.5);
    box-shadow: 0 2px 5px rgba(22, 163, 74, 0.3);
  }
  
  .status-completed .status-text {
    color: #4ade80;
    text-shadow: 0 0 5px rgba(74, 222, 128, 0.3);
  }
  
  .status-completed .status-indicator {
    background-color: #22c55e;
    box-shadow: 0 0 5px #22c55e;
  }
} 