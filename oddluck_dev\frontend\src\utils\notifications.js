/**
 * 显示通知消息
 * @param {string} message 通知消息文本
 * @param {string} type 通知类型：'success', 'error', 'warning', 'info'
 * @param {number} duration 显示时长，单位毫秒
 */
export function showNotification(message, type = 'info', duration = 3000) {
  // 检查是否有现有的通知容器
  let notificationContainer = document.getElementById('notification-container');
  
  // 如果没有，创建一个
  if (!notificationContainer) {
    notificationContainer = document.createElement('div');
    notificationContainer.id = 'notification-container';
    notificationContainer.style.position = 'fixed';
    notificationContainer.style.top = '20px';
    notificationContainer.style.right = '20px';
    notificationContainer.style.zIndex = '9999';
    document.body.appendChild(notificationContainer);
  }
  
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.style.backgroundColor = getBackgroundColor(type);
  notification.style.color = getTextColor(type);
  notification.style.padding = '12px 20px';
  notification.style.borderRadius = '6px';
  notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  notification.style.marginBottom = '10px';
  notification.style.opacity = '0';
  notification.style.transform = 'translateX(20px)';
  notification.style.transition = 'all 0.3s ease';
  notification.style.maxWidth = '300px';
  notification.style.wordWrap = 'break-word';
  notification.style.display = 'flex';
  notification.style.alignItems = 'center';
  
  // 添加图标
  const icon = document.createElement('span');
  icon.className = `notification-icon icon-${type}`;
  icon.innerHTML = getIcon(type);
  icon.style.marginRight = '10px';
  notification.appendChild(icon);
  
  // 添加消息文本
  const messageElement = document.createElement('span');
  messageElement.textContent = message;
  notification.appendChild(messageElement);
  
  // 添加关闭按钮
  const closeButton = document.createElement('span');
  closeButton.innerHTML = '&times;';
  closeButton.style.marginLeft = 'auto';
  closeButton.style.cursor = 'pointer';
  closeButton.style.marginLeft = '15px';
  closeButton.style.fontWeight = 'bold';
  closeButton.onclick = () => {
    closeNotification();
  };
  notification.appendChild(closeButton);
  
  // 添加到容器
  notificationContainer.appendChild(notification);
  
  // 动画显示
  setTimeout(() => {
    notification.style.opacity = '1';
    notification.style.transform = 'translateX(0)';
  }, 10);
  
  // 自动关闭
  const timeoutId = setTimeout(() => {
    closeNotification();
  }, duration);
  
  // 关闭函数
  function closeNotification() {
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(20px)';
    
    // 等待动画完成后移除元素
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
    
    clearTimeout(timeoutId);
  }
  
  // 悬停时暂停定时器
  notification.addEventListener('mouseenter', () => {
    clearTimeout(timeoutId);
  });
  
  // 离开时恢复定时器
  notification.addEventListener('mouseleave', () => {
    const timeoutId = setTimeout(() => {
      closeNotification();
    }, duration);
  });
}

// 根据类型获取背景颜色
function getBackgroundColor(type) {
  switch (type) {
    case 'success':
      return '#10B981';
    case 'error':
      return '#EF4444';
    case 'warning':
      return '#F59E0B';
    case 'info':
    default:
      return '#3B82F6';
  }
}

// 根据类型获取文本颜色
function getTextColor() {
  return '#FFFFFF';
}

// 根据类型获取图标
function getIcon(type) {
  switch (type) {
    case 'success':
      return '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>';
    case 'error':
      return '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>';
    case 'warning':
      return '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>';
    case 'info':
    default:
      return '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>';
  }
} 