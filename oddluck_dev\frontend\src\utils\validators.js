/**
 * 验证URL是否合法
 * @param {string} url URL字符串
 * @returns {boolean} 是否为有效URL
 */
export function isValidURL(url) {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    // 确保URL使用HTTP/HTTPS协议
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch (e) {
    return false;
  }
}

/**
 * 验证字符串是否为有效的电子邮件地址
 * @param {string} email 电子邮件地址
 * @returns {boolean} 是否为有效电子邮件
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证字符串是否为空或只包含空白字符
 * @param {string} str 字符串
 * @returns {boolean} 是否为空
 */
export function isEmpty(str) {
  return !str || str.trim() === '';
}

/**
 * 验证字符串长度是否在指定范围内
 * @param {string} str 字符串
 * @param {number} min 最小长度
 * @param {number} max 最大长度
 * @returns {boolean} 是否符合长度要求
 */
export function isLengthValid(str, min, max) {
  if (!str) return false;
  const length = str.length;
  if (min !== undefined && length < min) return false;
  if (max !== undefined && length > max) return false;
  return true;
}

/**
 * 验证对象是否有所有必需的属性
 * @param {Object} obj 对象
 * @param {Array<string>} requiredProps 必需属性数组
 * @returns {boolean} 是否包含所有必需属性
 */
export function hasRequiredProps(obj, requiredProps) {
  if (!obj || typeof obj !== 'object') return false;
  
  for (const prop of requiredProps) {
    if (obj[prop] === undefined || obj[prop] === null) {
      return false;
    }
  }
  
  return true;
}

/**
 * 验证字符串是否只包含字母和数字
 * @param {string} str 字符串
 * @returns {boolean} 是否只包含字母和数字
 */
export function isAlphanumeric(str) {
  return /^[a-zA-Z0-9]+$/.test(str);
}

/**
 * 验证数值是否在指定范围内
 * @param {number} value 数值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {boolean} 是否在范围内
 */
export function isInRange(value, min, max) {
  if (typeof value !== 'number') return false;
  if (min !== undefined && value < min) return false;
  if (max !== undefined && value > max) return false;
  return true;
} 