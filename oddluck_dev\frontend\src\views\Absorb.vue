<template>
  <div class="absorb-page">
    <div class="header">
      <h1 class="title">北冥</h1>
      <p class="subtitle">吸天地之数而化为己用</p>
    </div>
    
    <div class="absorb-container">
      <!-- 左侧：联赛和球队信息爬取 -->
      <div class="content-panel">
        <h2 class="panel-title">「联赛」</h2>
        
        <div class="form-container">
          <div class="form-group">
            <label>联赛</label>
            <select v-model="leagueForm.league" class="form-select">
              <option value="全部">全部</option>
              <option value="英超">英超</option>
              <option value="意甲">意甲</option>
              <option value="西甲">西甲</option>
              <option value="德甲">德甲</option>
              <option value="法甲">法甲</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>赛季</label>
            <select v-model="leagueForm.season" class="form-select">
              <option value="2023-2024">2023-2024</option>
              <option value="2024-2025">2024-2025</option>
              <option value="2025-2026">2025-2026</option>
            </select>
          </div>
          
          <!-- 新增爬取范围下拉框 -->
          <div class="form-group">
            <label>范围</label>
            <select v-model="leagueForm.scope" class="form-select">
              <option value="all">联赛与球队</option>
              <option value="league_only">仅联赛</option>
              <option value="team_only">仅球队</option>
            </select>
          </div>
          
          <!-- 占位空间，使左右两侧表单区域高度一致 -->
          <div class="spacer"></div>
          
          <div class="crawler-control">
            <div 
              class="absorption-circle" 
              :class="{ 'running': leagueSpiderRunning }"
              @click="startLeagueSpider"
              :disabled="leagueSpiderRunning"
            >
              <svg class="absorption-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"/>
                <circle cx="12" cy="12" r="2"/>
              </svg>
            </div>
            <div class="status-text">{{ leagueSpiderStatus }}</div>
          </div>
          
          <div class="progress-container" v-if="leagueSpiderRunning">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: leagueProgress + '%' }"></div>
            </div>
            <div class="progress-text">{{ leagueProgressText }}</div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：比赛和赔率信息爬取 -->
      <div class="content-panel">
        <h2 class="panel-title">「比赛」</h2>
        
        <div class="form-container">
          <div class="form-group">
            <label>联赛</label>
            <select v-model="matchForm.league" class="form-select">
              <option value="英超">英超</option>
              <option value="意甲">意甲</option>
              <option value="西甲">西甲</option>
              <option value="德甲">德甲</option>
              <option value="法甲">法甲</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>赛季</label>
            <select v-model="matchForm.season" class="form-select">
              <option value="2023-2024">2023-2024</option>
              <option value="2024-2025">2024-2025</option>
              <option value="2025-2026">2025-2026</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>轮次</label>
            <select v-model="matchForm.round" class="form-select">
              <option v-for="r in rounds" :key="r" :value="r">
                第 {{ r }} 轮{{ String(r) === currentRound.value ? ' ★' : '' }}
              </option>
            </select>
          </div>
          
          <!-- 新增比赛下拉选框 -->
          <div class="form-group">
            <label>比赛</label>
            <select v-model="matchForm.match" class="form-select" :disabled="!roundMatches.length">
              <option value="">-- 全部比赛 --</option>
              <option v-for="match in roundMatches" :key="match.id" :value="match.id">
                {{ match.name }}
              </option>
            </select>
          </div>
          
          <div class="crawler-control">
            <div 
              class="absorption-circle" 
              :class="{ 'running': matchSpiderRunning }"
              @click="startMatchSpider"
              :disabled="matchSpiderRunning"
            >
              <svg class="absorption-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"/>
                <circle cx="12" cy="12" r="2"/>
              </svg>
            </div>
            <div class="status-text">{{ matchSpiderStatus }}</div>
          </div>
          
          <div class="progress-container" v-if="matchSpiderRunning">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: matchProgress + '%' }"></div>
            </div>
            <div class="progress-text">{{ matchProgressText }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 日志查看区域 -->
    <div class="logs-container" v-if="activeTaskId">
      <!-- 新增包裹层，用于内容滚动 -->
      <div class="log-content-area">
        <SpiderLogViewer
          :taskId="activeTaskId"
          :title="activeTaskType === 'league' ? '联赛爬虫日志' : '比赛爬虫日志'"
          :autoRefresh="true"
          :refreshInterval="3000"
          @statusChanged="handleLogStatusChanged"
        />
      </div>
      <!-- 关闭按钮容器现在是 Flex 项目 -->
      <div class="close-logs">
        <button @click="closeLogViewer" class="close-logs-btn">
          关闭日志
        </button>
      </div>
    </div>
    
    <!-- 提示弹窗 -->
    <Transition name="notification">
      <div class="notification" v-if="showNotification" :class="notification.type">
        <div class="notification-content">
          <span>{{ notification.message }}</span>
          <button @click="showNotification = false" class="close-button">&times;</button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// @ts-nocheck
import { ref, onMounted, computed, onBeforeUnmount, watch, nextTick } from 'vue'
import axios from 'axios'
import SpiderLogViewer from '../components/SpiderLogViewer.vue'
import '../styles/absorb.css' // 导入外部CSS文件

// 类型声明
interface ApiError {
  message: string;
}

// 联赛爬虫表单数据
const leagueForm = ref({
  league: '英超',
  season: '2024-2025',
  scope: 'all' // 新增爬取范围字段
})

// 比赛爬虫表单数据
const matchForm = ref({
  league: '英超',
  season: '2024-2025',
  round: '',
  match: ''
})

// 爬虫状态
const leagueSpiderRunning = ref(false)
const matchSpiderRunning = ref(false)
const leagueSpiderStatus = ref('归藏')
const matchSpiderStatus = ref('归藏')
const leagueProgress = ref(0)
const matchProgress = ref(0)
const leagueProgressText = ref('')
const matchProgressText = ref('')

// 轮次选项
const rounds = ref<string[]>([])  // 轮次选项数组
const currentRound = ref<string>('1')  // 当前轮次
const totalRounds = ref<number>(38)  // 默认最大轮次

// 比赛选项
const roundMatches = ref<Array<{id: string, name: string}>>([]) // 当前轮次的所有比赛

// 通知
const showNotification = ref(false)
const notification = ref({
  type: 'info',
  message: ''
})

// 日志查看器状态
const activeTaskId = ref('')
const activeTaskType = ref<'league' | 'match'>('league')

// API基础URL
const apiBaseUrl = 'http://localhost:8000/api'

// 记录上一次切换前的联赛和轮次
const previousLeague = ref('')
const previousSeason = ref('')
const userSelectedRound = ref('')

// 使用英文名称映射表，因为数据库中可能是英文名称
const leagueNameMap: Record<string, string> = {
  '英超': 'English Premier League',
  '意甲': 'Italian Serie A',
  '西甲': 'Spanish La Liga',
  '德甲': 'German Bundesliga',
  '法甲': 'France Ligue 1'
}

// 联赛ID映射表
const leagueIdMap: Record<string, string> = {
  '英超': '36',
  '意甲': '34',
  '西甲': '31',
  '德甲': '8',
  '法甲': '11'
}

// 显示通知
const showMessage = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  // 先清除之前的定时器（如果有）
  if (notificationTimer.value) {
    clearTimeout(notificationTimer.value)
  }
  
  // 设置通知内容和类型
  notification.value = { message, type }
  showNotification.value = true
  
  // 设置自动关闭通知的定时器（5秒后）
  notificationTimer.value = setTimeout(() => {
    showNotification.value = false
  }, 5000)
}

// 通知定时器
const notificationTimer = ref<number | null>(null)

// 获取当前轮次
const fetchCurrentRound = async () => {
  console.log('开始获取当前轮次...')
  try {
    // 调用API获取当前轮次
    const league = matchForm.value.league
    const season = matchForm.value.season
    
    console.log(`请求联赛数据: 联赛=${league}, 赛季=${season}`)
    
    // 尝试使用英文名称查询
    const englishName = leagueNameMap[league as keyof typeof leagueNameMap] || league
    console.log(`使用英文名称查询: ${englishName}`)
    
    let response = null
    let leagueData = null
    
    try {
      response = await axios.get(`${apiBaseUrl}/leagues/?league_name_en=${encodeURIComponent(englishName)}&season=${season}`)
      console.log('通过英文名查询结果:', response.data)
      
      // 检查是否有结果
      if (!response.data.results || response.data.results.length === 0) {
        console.log('通过英文名称查询没有找到结果，尝试使用中文名称')
        // 尝试使用中文名查询
        response = await axios.get(`${apiBaseUrl}/leagues/?league_name=${encodeURIComponent(league)}&season=${season}`)
        console.log('通过中文名查询结果:', response.data)
      }
    } catch (e: any) {
      console.log('查询失败:', e.message)
      // 尝试查询所有联赛
      response = await axios.get(`${apiBaseUrl}/leagues/`)
      console.log('获取所有联赛:', response.data)
    }
    
    // 检查是否获取到数据
    if (response && response.data.results && response.data.results.length > 0) {
      leagueData = response.data.results[0]
      console.log('最终获取到联赛数据:', leagueData)
      
      // 确保current_round是字符串类型
      let currentRoundNum = '1'  // 默认值
      let maxRounds = '38'       // 默认值
      
      // 如果存在total_rounds字段，则使用它
      if (leagueData.total_rounds !== null && leagueData.total_rounds !== undefined) {
        maxRounds = String(leagueData.total_rounds)
        console.log(`使用从API获取的总轮次: ${maxRounds}`)
      }
      
      // 如果存在current_round字段并且不为null，则使用它
      if (leagueData.current_round !== null && leagueData.current_round !== undefined) {
        currentRoundNum = String(leagueData.current_round)
        console.log(`使用从API获取的当前轮次: ${currentRoundNum}`)
      } else {
        // 没有current_round字段，使用与total_rounds相同的值或默认30
        if (leagueData.total_rounds) {
          // 使用总轮次的一半或30作为默认当前轮次
          const halfRounds = Math.min(30, Math.floor(parseInt(maxRounds) / 2))
          currentRoundNum = String(halfRounds)
          console.log(`API中没有current_round字段，使用总轮次一半作为默认值: ${currentRoundNum}`)
        } else {
          currentRoundNum = '30'  // 更合理的默认值
          console.log(`API中没有current_round和total_rounds字段，使用默认值: ${currentRoundNum}`)
        }
      }
      
      console.log(`该联赛当前轮次: ${currentRoundNum}, 总轮次: ${maxRounds}`)
      
      // 先更新轮次选项
      rounds.value = Array.from({length: parseInt(maxRounds)}, (_, i) => String(i + 1))
      totalRounds.value = parseInt(maxRounds)
      currentRound.value = currentRoundNum
      
      // 根据用户行为确定显示哪个轮次
      // 如果是首次加载，显示当前轮次
      if (!previousLeague.value || !previousSeason.value) {
        matchForm.value.round = currentRoundNum
        console.log('首次加载，显示当前轮次:', currentRoundNum)
      } 
      // 如果是切换联赛，显示新联赛的当前轮次
      else if (previousLeague.value !== league || previousSeason.value !== season) {
        // 获取用户选择的轮次
        const storedUserRound = userSelectedRound.value
        
        // 只有在用户没有手动选择轮次，或者联赛/赛季变化时，才显示当前轮次
        if (!storedUserRound || previousLeague.value !== league || previousSeason.value !== season) {
          matchForm.value.round = currentRoundNum
          console.log('联赛或赛季变化，显示新的当前轮次:', currentRoundNum)
        } else {
          // 如果用户之前手动选择了轮次，则尝试保留用户的选择（如果在范围内）
          if (rounds.value.includes(storedUserRound)) {
            matchForm.value.round = storedUserRound
            console.log('保留用户选择的轮次:', storedUserRound)
          } else {
            // 超出范围则使用当前轮次
            matchForm.value.round = currentRoundNum
            console.log('用户选择的轮次超出范围，显示当前轮次:', currentRoundNum)
          }
        }
      }
      
      // 更新上一次的联赛和赛季
      previousLeague.value = league
      previousSeason.value = season
      
      console.log(`最终设置轮次为: ${matchForm.value.round}，可选轮次范围: 1-${maxRounds}`)
      
      // 使用nextTick确保DOM更新
      await nextTick()
      console.log('nextTick后 - 当前选中轮次:', matchForm.value.round)
      
      // 在设置好轮次后获取比赛列表
      await fetchRoundMatches()
    } else {
      console.log('未能获取联赛数据，使用默认值')
      
      // 使用默认值
      rounds.value = Array.from({length: 38}, (_, i) => String(i + 1))
      currentRound.value = '1'
      totalRounds.value = 38
      matchForm.value.round = '1'
      
      // 显示错误通知
      showMessage(`未能获取联赛 "${league}" 赛季 "${season}" 的数据，使用默认值`, 'error')
    }
  } catch (error: any) {
    console.error('获取当前轮次出错:', error)
    
    // 使用默认值
    rounds.value = Array.from({length: 38}, (_, i) => String(i + 1))
    currentRound.value = '1'
    totalRounds.value = 38
    matchForm.value.round = '1'
    
    // 显示错误通知
    showMessage(`获取当前轮次出错: ${error.message}`, 'error')
  }
}

// 获取特定轮次的比赛列表
const fetchRoundMatches = async () => {
  // 清空之前的比赛选择
  matchForm.value.match = '';
  roundMatches.value = [];
  
  // 检查是否有足够的信息来查询
  if (!matchForm.value.league || !matchForm.value.season || !matchForm.value.round) {
    console.log('缺少查询比赛所需的联赛、赛季或轮次信息');
    return;
  }
  
  // 获取联赛ID
  const selectedLeagueName = matchForm.value.league;
  const leagueId = leagueIdMap[selectedLeagueName as keyof typeof leagueIdMap];
  
  if (!leagueId) {
    console.error(`未找到联赛 "${selectedLeagueName}" 对应的 ID`);
    showMessage(`无法获取联赛 "${selectedLeagueName}" 的比赛数据，缺少联赛ID`, 'error');
    return;
  }
  
  try {
    console.log(`请求比赛数据: 联赛ID=${leagueId}, 赛季=${matchForm.value.season}, 轮次=${matchForm.value.round}`);
    
    // 获取该轮次的所有比赛，使用 league_id 查询
    const response = await axios.get(`${apiBaseUrl}/matches/`, {
      params: {
        league_id: leagueId, // 使用联赛ID查询
        season: matchForm.value.season,
        round: matchForm.value.round
      }
    });
    
    if (response.data.results && response.data.results.length > 0) {
      console.log(`找到 ${response.data.results.length} 场比赛`);
      
      // 打印第一场比赛的结构，帮助调试
      console.log('比赛数据样例:', response.data.results[0]);
      
      // 将比赛数据转换为下拉框需要的格式
      const matches = [];
      
      for (const match of response.data.results) {
        // 尝试从不同可能的字段获取球队名称
        let homeTeamName = '主队';
        let awayTeamName = '客队';
        
        // 检查并使用可能的字段名称
        if (match.home_team_name) {
          homeTeamName = match.home_team_name;
        } else if (match.home_team && match.home_team.team_name) {
          homeTeamName = match.home_team.team_name;
        } else if (match.home_team) {
          homeTeamName = match.home_team;
        }
        
        if (match.away_team_name) {
          awayTeamName = match.away_team_name;
        } else if (match.away_team && match.away_team.team_name) {
          awayTeamName = match.away_team.team_name;
        } else if (match.away_team) {
          awayTeamName = match.away_team;
        }
        
        matches.push({
          id: match.match_id,
          name: `${homeTeamName} vs ${awayTeamName}`
        });
      }
      
      roundMatches.value = matches;
    } else {
      console.log('未找到比赛数据');
      showMessage(`未找到第 ${matchForm.value.round} 轮的比赛数据`, 'info');
    }
  } catch (error: any) {
    console.error('获取轮次比赛列表失败:', error);
    showMessage(`获取比赛列表失败: ${error.message}`, 'error');
  }
}

// 强制刷新任务状态
const refreshTaskStatus = async (taskId: string, type: 'league' | 'match'): Promise<void> => {
  if (!taskId) return;
  
  try {
    const response = await axios.get(`${apiBaseUrl}/spider-logs/${taskId}/status/`);
    const task = response.data;
    
    // 直接调用状态更新逻辑
    updateTaskStatus(task, type);
    
    // 如果任务正在运行，继续轮询
    if (task.status === 'running') {
      setTimeout(() => pollTaskStatus(taskId, type), 3000);
    }
  } catch (error) {
    console.error('刷新任务状态失败:', error);
  }
};

// 定义任务数据接口
interface SpiderTask {
  task_id: string;
  spider_type: string;
  league: string;
  season: string;
  round?: string;
  status: string;
  progress: number;
  message: string;
  log_content: string;
  started_at: string;
  updated_at: string;
  completed_at?: string;
}

// 更新任务状态的通用函数
const updateTaskStatus = (task: SpiderTask, type: 'league' | 'match'): void => {
  if (type === 'league') {
    leagueProgress.value = task.progress || 0;
    leagueProgressText.value = task.message || '处理中...';
    
    // 检查任务状态
    if (task.status === 'completed') {
      leagueSpiderRunning.value = false;
      leagueSpiderStatus.value = '归藏北冥';
      showMessage('「联赛」已归藏', 'success');
    } else if (task.status === 'failed') {
      leagueSpiderRunning.value = false;
      leagueSpiderStatus.value = '「联赛」无法归藏';
      showMessage(`「联赛」无法归藏: ${task.message}`, 'error');
    } else if (task.status === 'paused') {
      leagueSpiderRunning.value = false;
      leagueSpiderStatus.value = '暂停吸纳';
      
      // 检查实际任务完成情况
      if (task.progress >= 100 || task.log_content.includes('爬虫任务完成')) {
        leagueSpiderStatus.value = '归藏北冥';
        showMessage('「联赛」已归藏', 'success');
      } else {
        showMessage('暂停吸纳', 'info');
      }
    } else if (task.status === 'running') {
      leagueSpiderRunning.value = true;
      leagueSpiderStatus.value = '吸纳「联赛」...';
    } else {
      leagueSpiderRunning.value = false;
      leagueSpiderStatus.value = '爬虫状态未知';
    }
  } else {
    matchProgress.value = task.progress || 0;
    matchProgressText.value = task.message || '处理中...';
    
    if (task.status === 'completed') {
      matchSpiderRunning.value = false;
      matchSpiderStatus.value = '归藏北冥';
      showMessage('「比赛」已归藏', 'success');
    } else if (task.status === 'failed') {
      matchSpiderRunning.value = false;
      matchSpiderStatus.value = '「比赛」无法归藏';
      showMessage(`「比赛」无法归藏: ${task.message}`, 'error');
    } else if (task.status === 'paused') {
      matchSpiderRunning.value = false;
      matchSpiderStatus.value = '暂停吸纳';
      
      // 检查实际任务完成情况
      if (task.progress >= 100 || task.log_content.includes('爬虫任务完成')) {
        matchSpiderStatus.value = '归藏北冥';
        showMessage('「比赛」已归藏', 'success');
      } else {
        showMessage('暂停吸纳', 'info');
      }
    } else if (task.status === 'running') {
      matchSpiderRunning.value = true;
      matchSpiderStatus.value = '吸纳「比赛」...';
    } else {
      matchSpiderRunning.value = false;
      matchSpiderStatus.value = '爬虫状态未知';
    }
  }
};

// 轮询任务状态的通用函数
const pollTaskStatus = async (taskId: string, type: 'league' | 'match'): Promise<void> => {
  try {
    const response = await axios.get(`${apiBaseUrl}/spider-logs/${taskId}/status/`)
    const task = response.data
    
    // 使用通用的状态更新函数
    updateTaskStatus(task, type);
    
    // 如果任务正在运行，继续轮询
    if (task.status === 'running') {
      setTimeout(() => pollTaskStatus(taskId, type), 3000)
    }
  } catch (error) {
    console.error('轮询任务状态失败:', error)
    
    if (type === 'league') {
      leagueSpiderRunning.value = false
      leagueSpiderStatus.value = '爬虫状态未知'
    } else {
      matchSpiderRunning.value = false
      matchSpiderStatus.value = '爬虫状态未知'
    }
    
    showMessage('无法获取爬虫状态', 'error')
  }
}

// 启动联赛爬虫
const startLeagueSpider = async () => {
  if (leagueSpiderRunning.value) return
  
  try {
    leagueSpiderRunning.value = true
    leagueSpiderStatus.value = '吸纳「联赛」...'
    leagueProgress.value = 0
    leagueProgressText.value = '准备中...'
    
    const { league, season, scope } = leagueForm.value
    
    // 调用API启动爬虫
    const response = await axios.post(`${apiBaseUrl}/spider-logs/start-league-spider/`, {
      league,
      season,
      scope
    })
    
    // 获取任务ID
    const { task_id } = response.data
    
    // 设置当前任务ID用于日志查看
    activeTaskId.value = task_id
    activeTaskType.value = 'league'
    
    // 轮询任务状态
    pollTaskStatus(task_id, 'league')
    
  } catch (error) {
    console.error('无法启动联赛爬虫任务:', error)
    leagueSpiderRunning.value = false
    leagueSpiderStatus.value = '「联赛」无法归藏，请重试'
    showMessage('无法启动联赛爬虫任务', 'error')
  }
}

// 启动比赛爬虫
const startMatchSpider = async () => {
  if (matchSpiderRunning.value) return
  
  try {
    matchSpiderRunning.value = true
    matchSpiderStatus.value = '吸纳「比赛」...'
    matchProgress.value = 0
    matchProgressText.value = '准备中...'
    
    const { league, season, round, match } = matchForm.value
    
    // 构建请求参数
    const requestParams = {
      league,
      season,
      round
    }
    
    // 如果选择了特定比赛，添加target_match_id参数
    if (match) {
      console.log(`用户选择了特定比赛: ID=${match}`)
      Object.assign(requestParams, { target_match_id: match })
    } else {
      console.log('用户未选择特定比赛，将爬取整个轮次')
    }
    
    console.log('[Absorb.vue] 准备发送给 /start-match-spider/ 的参数:', JSON.stringify(requestParams)); // 添加详细日志
    
    // 调用API启动爬虫
    const response = await axios.post(`${apiBaseUrl}/spider-logs/start-match-spider/`, requestParams)
    
    // 获取任务ID
    const { task_id } = response.data
    
    // 设置当前任务ID用于日志查看
    activeTaskId.value = task_id
    activeTaskType.value = 'match'
    
    // 轮询任务状态
    pollTaskStatus(task_id, 'match')
    
  } catch (error) {
    console.error('无法启动比赛爬虫任务:', error)
    matchSpiderRunning.value = false
    matchSpiderStatus.value = '「比赛」无法归藏，请重试'
    showMessage('无法启动比赛爬虫任务', 'error')
  }
}

// 关闭日志查看器
const closeLogViewer = () => {
  activeTaskId.value = ''
}

// 定义状态变更数据接口
interface StatusChangeData {
  taskId: string;
  status: string;
  message: string;
  progress: number;
}

// 处理日志状态变更
const handleLogStatusChanged = (statusData: StatusChangeData): void => {
  console.log('日志状态变更:', statusData);
  
  // 获取状态数据
  const { taskId, status, message, progress } = statusData;
  
  // 如果没有关联的任务ID，则不处理
  if (!taskId || taskId !== activeTaskId.value) return;
  
  // 根据任务类型更新主页面状态
  if (activeTaskType.value === 'league') {
    if (status === 'running') {
      leagueSpiderRunning.value = true;
      leagueSpiderStatus.value = '吸纳「联赛」...';
    } else if (status === 'paused') {
      leagueSpiderRunning.value = false;
      leagueSpiderStatus.value = '暂停吸纳';
    } else if (status === 'completed') {
      leagueSpiderRunning.value = false;
      leagueSpiderStatus.value = '归藏北冥';
    } else if (status === 'failed') {
      leagueSpiderRunning.value = false;
      leagueSpiderStatus.value = '「联赛」无法归藏';
    }
    
    // 更新进度
    leagueProgress.value = progress;
    if (message) leagueProgressText.value = message;
  } else {
    if (status === 'running') {
      matchSpiderRunning.value = true;
      matchSpiderStatus.value = '吸纳「比赛」...';
    } else if (status === 'paused') {
      matchSpiderRunning.value = false;
      matchSpiderStatus.value = '暂停吸纳';
    } else if (status === 'completed') {
      matchSpiderRunning.value = false;
      matchSpiderStatus.value = '归藏北冥';
    } else if (status === 'failed') {
      matchSpiderRunning.value = false;
      matchSpiderStatus.value = '「比赛」无法归藏';
    }
    
    // 更新进度
    matchProgress.value = progress;
    if (message) matchProgressText.value = message;
  }
  
  // 如果需要，可以强制刷新状态
  if (status === 'running') {
    refreshTaskStatus(taskId, activeTaskType.value);
  }
};

// 添加监听器，在轮次变化时保存用户选择
watch(() => matchForm.value.round, (newValue, oldValue) => {
  if (newValue !== oldValue && newValue) {
    console.log(`用户选择了新轮次: ${newValue}`)
    userSelectedRound.value = newValue
    
    // 轮次变化时重新获取该轮次的比赛
    fetchRoundMatches()
  }
})

// 添加监听器，在联赛或赛季变化时更新轮次
watch(() => matchForm.value.league, async (newValue, oldValue) => {
  if (newValue !== oldValue) {
    console.log(`联赛变更: ${oldValue} -> ${newValue}，重新获取当前轮次`)
    
    // 清空现有选项，等待API返回新数据
    rounds.value = []
    roundMatches.value = [] // 清空比赛列表
    matchForm.value.match = '' // 清空比赛选择
    await nextTick()
    
    // 获取新联赛数据
    await fetchCurrentRound()
  }
})

watch(() => matchForm.value.season, async (newValue, oldValue) => {
  if (newValue !== oldValue) {
    console.log(`赛季变更: ${oldValue} -> ${newValue}，重新获取当前轮次`)
    
    // 清空现有选项，等待API返回新数据
    rounds.value = []
    roundMatches.value = [] // 清空比赛列表
    matchForm.value.match = '' // 清空比赛选择
    await nextTick()
    
    // 获取新赛季数据
    await fetchCurrentRound()
  }
})

onMounted(async () => {
  console.log('北冥蓄劲备发')
  console.log('组件挂载，初始轮次:', matchForm.value.round)
  // 获取当前轮次
  await fetchCurrentRound()
  console.log('组件挂载后，当前轮次:', matchForm.value.round)
})

// 组件销毁前取消所有轮询
onBeforeUnmount(() => {
  // 清除所有setTimeout
  // 这里不需要具体实现，因为setTimeout的清除已在pollTaskStatus中处理
  if (notificationTimer.value) {
    clearTimeout(notificationTimer.value)
  }
})
</script> 