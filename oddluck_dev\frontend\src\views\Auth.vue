<template>
  <div class="auth-container">
    <!-- 登录成功提示 -->
    <div v-if="loginSuccess" class="success-notification">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
        <polyline points="22 4 12 14.01 9 11.01"></polyline>
      </svg>
      <span>{{ successMessage }}</span>
    </div>

    <div class="auth-card">
      <!-- Logo区域 -->
      <div class="logo-section">
        <img src="../assets/oddluck-logo.png" alt="OddLuck" class="auth-logo" />
      </div>
      
      <!-- 标题 -->
      <!-- <h2 class="auth-title">
        {{ isLogin ? '欢迎回来' : '创建账户' }}
      </h2>
      <p class="auth-subtitle">
        {{ isLogin ? '使用您的账户继续' : '填写下面的信息开始您的旅程' }}
      </p> -->

      <!-- 错误提示 -->
      <div v-if="error" class="error-message">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
        <span>{{ error }}</span>
        <button class="close-error" @click="clearError">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <!-- 登录表单 -->
      <form v-if="isLogin" @submit.prevent="login" class="auth-form">
        <div class="form-group">
          <label for="username">昵称</label>
          <div class="input-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <input 
              id="username" 
              name="username" 
              type="text" 
              v-model="loginForm.username"
              required 
              placeholder="输入用户名"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <div class="input-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
            <input 
              id="password" 
              name="password" 
              :type="showPassword ? 'text' : 'password'"
              v-model="loginForm.password" 
              required 
              placeholder="输入密码"
            />
            <button type="button" class="toggle-password" @click="showPassword = !showPassword">
              <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
        </div>

        <button 
          type="submit" 
          :disabled="loading"
          class="auth-button"
        >
          <span v-if="loading" class="loading-spinner"></span>
          <span v-else>登录</span>
        </button>
      </form>

      <!-- 注册表单 -->
      <form v-else @submit.prevent="register" class="auth-form">
        <div class="form-group">
          <label for="reg-username">用户名</label>
          <div class="input-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <input 
              id="reg-username" 
              name="username" 
              type="text" 
              v-model="registerForm.username"
              required 
              placeholder="选择用户名"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="email">邮箱</label>
          <div class="input-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <input 
              id="email" 
              name="email" 
              type="email" 
              v-model="registerForm.email"
              required 
              placeholder="您的邮箱地址"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="reg-password">密码</label>
          <div class="input-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
            <input 
              id="reg-password" 
              name="password" 
              :type="showPassword ? 'text' : 'password'"
              v-model="registerForm.password" 
              required 
              placeholder="创建密码"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="password2">确认密码</label>
          <div class="input-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
            <input 
              id="password2" 
              name="password2" 
              :type="showPassword ? 'text' : 'password'"
              v-model="registerForm.password2" 
              required 
              placeholder="再次输入密码"
            />
            <button type="button" class="toggle-password" @click="showPassword = !showPassword">
              <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
        </div>

        <button 
          type="submit" 
          :disabled="loading"
          class="auth-button"
        >
          <span v-if="loading" class="loading-spinner"></span>
          <span v-else>注册</span>
        </button>
      </form>

      <!-- 切换登录/注册 -->
      <div class="auth-footer">
        <p>
          {{ isLogin ? '还没有账户?' : '已有账户?' }}
          <button 
            type="button" 
            @click="toggleAuthMode"
            class="toggle-auth-mode"
          >
            {{ isLogin ? '立即注册' : '立即登录' }}
          </button>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import api from '../services/api';
import '../styles/auth.css'; // 导入外部CSS文件

const router = useRouter();
const isLogin = ref(true);
const loading = ref(false);
const error = ref('');
const showPassword = ref(false);
const loginSuccess = ref(false);
const successMessage = ref('登录成功！即将为您跳转...');

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
});

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  password2: ''
});

// 切换登录/注册模式
const toggleAuthMode = () => {
  isLogin.value = !isLogin.value;
  error.value = '';
};

// 清除错误消息
const clearError = () => {
  error.value = '';
};

// 登录处理
const login = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    const response = await api.post('/auth/login/', {
      username: loginForm.username,
      password: loginForm.password
    });
    
    // 保存token到localStorage
    localStorage.setItem('token', response.data.access);
    localStorage.setItem('refreshToken', response.data.refresh);
    
    // 保存基本用户信息
    localStorage.setItem('username', response.data.username);
    localStorage.setItem('user_id', response.data.user_id);
    
    // 获取并保存用户档案信息
    let profileData = null;
    let userEmail = null;
    
    try {
      // 先获取用户基本信息
      const userInfoResponse = await api.get('/auth/user/');
      if (userInfoResponse.data && userInfoResponse.data.email) {
        userEmail = userInfoResponse.data.email;
        // 保存邮箱到localStorage
        localStorage.setItem('email', userEmail);
        
        // 保存用户加入日期
        if (userInfoResponse.data.date_joined) {
          localStorage.setItem('joinDate', userInfoResponse.data.date_joined);
        }
        
        // 保存最后登录时间
        if (userInfoResponse.data.last_login) {
          localStorage.setItem('lastLogin', userInfoResponse.data.last_login);
        }
      }
      
      // 获取用户档案详细信息
      const profileResponse = await api.get('/auth/profile/');
      if (profileResponse.data && profileResponse.data.id) {
        // 如果有头像数据，保存到localStorage
        if (profileResponse.data.avatar) {
          localStorage.setItem('avatar', profileResponse.data.avatar);
        }
        
        // 保存最后更新时间
        if (profileResponse.data.updated_at) {
          localStorage.setItem('lastUpdated', profileResponse.data.updated_at);
        }
        
        // 使用档案中的邮箱（如果有）
        if (profileResponse.data.email) {
          userEmail = profileResponse.data.email;
          localStorage.setItem('email', userEmail);
        }
        
        profileData = profileResponse.data;
      }
    } catch (profileError) {
      console.error('获取用户档案失败:', profileError);
      // 即使获取档案失败，也不影响登录成功
    }
    
    // 触发登录成功事件，通知导航栏更新用户信息
    window.dispatchEvent(new CustomEvent('login-success', {
      detail: {
        username: response.data.username,
        userId: response.data.user_id,
        avatar: profileData?.avatar || null,
        email: userEmail || response.data.username + '@oddluck.com',
        profile: profileData
      }
    }));
    
    // 显示登录成功提示
    successMessage.value = '登录成功！即将为您跳转...';
    loginSuccess.value = true;
    
    // 延迟跳转到主页
    setTimeout(() => {
      router.push('/');
    }, 1000);
    
  } catch (err) {
    console.error('登录错误:', err);
    if (err.response && err.response.data) {
      if (err.response.data.detail) {
        error.value = err.response.data.detail;
      } else {
        error.value = '登录失败，请检查用户名和密码';
      }
    } else {
      error.value = '登录时发生错误，请稍后再试';
    }
    // 错误消息将保持显示，直到用户手动关闭或切换到注册模式
  } finally {
    loading.value = false;
  }
};

// 注册处理
const register = async () => {
  // 验证两次密码是否一致
  if (registerForm.password !== registerForm.password2) {
    error.value = '两次输入的密码不一致';
    return;
  }
  
  try {
    loading.value = true;
    error.value = '';
    
    const response = await api.post('/auth/register/', {
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password,
      password2: registerForm.password2
    });
    
    // 注册成功后切换到登录表单
    isLogin.value = true;
    loginForm.username = registerForm.username;
    loginForm.password = '';
    
    // 清空注册表单
    registerForm.username = '';
    registerForm.email = '';
    registerForm.password = '';
    registerForm.password2 = '';
    
    // 替换alert为登录成功提示
    successMessage.value = '注册成功！请使用您的账号登录';
    loginSuccess.value = true;
    // 显示自定义成功消息
    setTimeout(() => {
      loginSuccess.value = false;
    }, 3000);
  } catch (err) {
    console.error('注册失败:', err);
    if (err.response && err.response.data) {
      const errData = err.response.data;
      let errMsg = '';
      
      // 处理不同类型的错误消息
      if (typeof errData === 'object') {
        for (const key in errData) {
          if (Array.isArray(errData[key])) {
            errMsg += `${key}: ${errData[key].join(', ')}\n`;
          } else {
            errMsg += `${key}: ${errData[key]}\n`;
          }
        }
      } else {
        errMsg = String(errData);
      }
      
      error.value = '注册失败: ' + errMsg;
    } else {
      error.value = '注册失败，请稍后重试';
    }
    // 错误消息将保持显示，直到用户手动关闭或切换到登录模式
  } finally {
    loading.value = false;
  }
};
</script> 