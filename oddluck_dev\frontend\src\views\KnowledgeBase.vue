<template>
  <div class="knowledge-base">
    <!-- 加载状态和错误提示 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="error = null">关闭</button>
    </div>
    
    <!-- 左侧知识库列表 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2>知识库分类</h2>
        <button class="add-category-btn" @click="showAddCategoryDialog">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          新建分类
        </button>
      </div>
      
      <div class="category-list" v-if="categories.length > 0">
        <div v-for="category in categories" 
             :key="category.id" 
             class="category-item"
             :class="{ active: selectedCategory?.id === category.id }"
             @click="selectCategory(category)">
          <span class="category-name">{{ category.name }}</span>
          <div class="category-actions">
            <span class="file-count">({{ category.file_count || 0 }})</span>
            <button class="delete-btn" @click.stop="confirmDeleteCategory(category)">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      <div v-else class="empty-category">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
        </svg>
        <p>暂无知识库分类</p>
        <p class="hint">点击上方按钮新建分类</p>
      </div>
    </div>

    <!-- 右侧文件展示区 -->
    <div class="main-content">
      <div class="content-header" v-if="selectedCategory">
        <h2>{{ selectedCategory.name }}</h2>
        <button class="upload-btn" type="button" @click.prevent="showUploadDialog">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="17 8 12 3 7 8"></polyline>
            <line x1="12" y1="3" x2="12" y2="15"></line>
          </svg>
          上传文件
        </button>
      </div>

      <div v-if="!selectedCategory" class="no-selection">
        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="12" y1="18" x2="12" y2="12"></line>
          <line x1="9" y1="15" x2="15" y2="15"></line>
        </svg>
        <p>请选择或创建一个知识库分类</p>
      </div>

      <div v-else-if="selectedCategory.files?.length" class="file-list">
        <div v-for="file in selectedCategory.files" 
             :key="file.id" 
             class="file-item">
          <div class="file-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
          </div>
          <div class="file-info">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-meta">
              <span>{{ formatFileSize(file.size) }}</span>
              <span>{{ formatDate(file.upload_time) }}</span>
              <span class="vector-status" 
                :class="{
                  'processed': file.vectorization_status === 'completed',
                  'pending': file.vectorization_status === 'pending',
                  'processing': file.vectorization_status === 'processing',
                  'failed': file.vectorization_status === 'failed'
                }">
                {{ getVectorizationStatusText(file) }}
              </span>
              <span v-if="file.vectorization_status === 'failed'" 
                    class="vector-error-icon" 
                    @click="showVectorizationError(file)">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
              </span>
            </div>
          </div>
          <div class="file-actions">
            <button class="action-btn" @click="downloadFile(file)">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
            </button>
            <button class="action-btn info" v-if="file.is_vectorized" @click="showVectorInfo(file)">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
              </svg>
            </button>
            <button class="action-btn delete" @click="deleteFile(file)">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div v-else class="empty-files">
        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="12" y1="18" x2="12" y2="12"></line>
          <line x1="9" y1="15" x2="15" y2="15"></line>
        </svg>
        <p>当前分类下暂无文件</p>
        <p class="hint">点击上方按钮上传文件</p>
      </div>
    </div>

    <!-- 新建分类对话框 -->
    <div v-if="showCategoryDialog" class="dialog-overlay" @click="closeCategoryDialog">
      <div class="dialog" @click.stop>
        <h3>新建知识库分类</h3>
        <div class="dialog-content">
          <input type="text" 
                 v-model="newCategoryName" 
                 placeholder="请输入分类名称"
                 @keyup.enter="createCategory">
        </div>
        <div class="dialog-actions">
          <button class="cancel-btn" @click="closeCategoryDialog">取消</button>
          <button class="confirm-btn" 
                  :disabled="!newCategoryName.trim()" 
                  @click="createCategory">
            确定
          </button>
        </div>
      </div>
    </div>

    <!-- 上传文件对话框 -->
    <div v-if="isUploadDialogVisible" class="dialog-overlay" @click="closeUploadDialog">
      <div class="dialog" @click.stop>
        <h3>上传文件</h3>
        <div class="dialog-content">
          <div class="upload-area" 
                @drop.prevent="handleFileDrop"
                @dragover.prevent
                @click="triggerFileInput">
            <input type="file" 
                    ref="fileInput" 
                    style="display: none"
                    @change="handleFileSelect"
                    accept=".pdf,.docx,.xlsx,.txt,.md,.html,.pptx,.epub,.doc,.xls,.ppt"
                    multiple>
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="17 8 12 3 7 8"></polyline>
              <line x1="12" y1="3" x2="12" y2="15"></line>
            </svg>
            <p>点击或拖拽文件到此处上传</p>
            <p class="supported-formats">支持 PDF、DOCX、XLSX、TXT、MD、HTML、PPTX、EPUB 等格式</p>
            <p class="embedding-info">文件将使用 BAAI/bge-large-zh-v1.5 模型进行向量化处理</p>
          </div>
          <div v-if="selectedFiles.length" class="selected-files">
            <div v-for="(file, index) in selectedFiles" 
                 :key="index" 
                 class="selected-file">
              <span>{{ file.name }}</span>
              <button class="remove-btn" @click="removeSelectedFile(index)">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div class="dialog-actions">
          <button class="cancel-btn" @click="closeUploadDialog">取消</button>
          <button class="confirm-btn" 
                  :disabled="!selectedFiles.length"
                  @click="uploadFiles">
            上传
          </button>
        </div>
      </div>
    </div>

    <!-- 添加向量化信息对话框 -->
    <div v-if="showVectorInfoDialog" class="dialog-overlay" @click="closeVectorInfoDialog">
      <div class="dialog" @click.stop>
        <h3>文件向量化信息</h3>
        <div class="dialog-content">
          <p><strong>文件名称：</strong>{{ selectedFile?.name }}</p>
          <p><strong>向量化状态：</strong>{{ getVectorizationStatusText(selectedFile) }}</p>
          <p><strong>向量化时间：</strong>{{ selectedFile?.vectorized_at ? formatDate(selectedFile.vectorized_at) : '未知' }}</p>
          <p><strong>向量模型：</strong>{{ selectedFile?.embedding_model || 'BAAI/bge-large-zh-v1.5' }}</p>
          <p><strong>模型类型：</strong>HuggingFace 本地模型</p>
          <p><strong>文本分块数：</strong>{{ selectedFile?.chunks_count || '未知' }}</p>
        </div>
        <div class="dialog-actions">
          <button class="confirm-btn" @click="closeVectorInfoDialog">确定</button>
        </div>
      </div>
    </div>

    <!-- 添加向量化错误信息对话框 -->
    <div v-if="showErrorDialog" class="dialog-overlay" @click="closeErrorDialog">
      <div class="dialog" @click.stop>
        <h3>向量化处理错误</h3>
        <div class="dialog-content">
          <p><strong>文件名称：</strong>{{ selectedFile?.name }}</p>
          <p><strong>错误信息：</strong></p>
          <div class="error-message-box">{{ selectedFile?.vectorization_error || '未知错误' }}</div>
        </div>
        <div class="dialog-actions">
          <button class="confirm-btn" @click="retryVectorization">重试</button>
          <button class="cancel-btn" @click="closeErrorDialog">关闭</button>
        </div>
      </div>
    </div>

    <!-- 删除分类确认对话框 -->
    <div v-if="showDeleteDialog" class="dialog-overlay" @click="closeDeleteDialog">
      <div class="dialog" @click.stop>
        <h3>删除分类</h3>
        <div class="dialog-content">
          <p>确定要删除分类 "{{ categoryToDelete?.name }}" 吗？</p>
          <p class="warning">注意：删除分类将同时删除该分类下的所有文件，此操作不可恢复！</p>
        </div>
        <div class="dialog-actions">
          <button class="cancel-btn" @click="closeDeleteDialog">取消</button>
          <button class="delete-btn" @click="deleteCategory">
            确定删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { api } from '../services/api';
import '../styles/knowledge-base.css'; // 导入外部CSS文件

// 类型定义
interface Category {
  id: number;
  name: string;
  file_count: number;
  files?: KnowledgeFile[];
}

interface KnowledgeFile {
  id: number;
  name: string;
  size: number;
  upload_time: string;
  file: string;
  category: number;
  is_vectorized: boolean; // 添加向量化状态字段
  vectorized_at?: string; // 添加向量化完成时间
  embedding_model?: string; // 添加使用的嵌入模型
  chunks_count?: number; // 添加文本分块数量
  vectorization_status: string; // 添加向量化状态字段
  vectorization_error?: string; // 添加向量化错误信息
}

// 状态定义
const categories = ref<Category[]>([]);
const selectedCategory = ref<Category | null>(null);
const showCategoryDialog = ref(false);
const isUploadDialogVisible = ref(false);
const newCategoryName = ref('');
const selectedFiles = ref<File[]>([]);
const fileInput = ref<HTMLInputElement | null>(null);
const isLoading = ref(false);
const error = ref<string | null>(null);

// 添加向量化信息对话框状态
const showVectorInfoDialog = ref(false);
const selectedFile = ref<KnowledgeFile | null>(null);

// 添加向量化错误对话框状态
const showErrorDialog = ref(false);

// 添加删除分类对话框状态
const showDeleteDialog = ref(false);
const categoryToDelete = ref<Category | null>(null);

// 获取所有分类
const fetchCategories = async () => {
  isLoading.value = true;
  error.value = null;
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      error.value = '请先登录';
      return;
    }

    const response = await api.get('/knowledge/categories/');
    categories.value = response.data.results || [];
  } catch (err: any) {
    console.error('获取分类失败:', err);
    error.value = err.response?.data?.error || '获取分类失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 获取分类详情（包含文件列表）
const fetchCategoryDetail = async (categoryId: number) => {
  isLoading.value = true;
  error.value = null;
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      error.value = '请先登录';
      return;
    }

    const response = await api.get(`/knowledge/categories/${categoryId}/`);
    const category = response.data;
    
    // 更新selectedCategory和categories中的对应项
    selectedCategory.value = category;
    const index = categories.value.findIndex(c => c.id === category.id);
    if (index !== -1) {
      categories.value[index] = category;
    }
  } catch (err: any) {
    console.error('获取分类详情失败:', err);
    error.value = err.response?.data?.error || '获取分类详情失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 分类相关方法
const showAddCategoryDialog = () => {
  showCategoryDialog.value = true;
  newCategoryName.value = '';
};

const closeCategoryDialog = () => {
  showCategoryDialog.value = false;
  newCategoryName.value = '';
};

const createCategory = async () => {
  if (!newCategoryName.value.trim()) return;
  
  isLoading.value = true;
  error.value = null;
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      error.value = '请先登录';
      return;
    }

    const response = await api.post(
      '/knowledge/categories/', 
      { name: newCategoryName.value.trim() }
    );
    
    // 添加新分类到列表并选中它
    const newCategory = response.data;
    categories.value.push(newCategory);
    selectCategory(newCategory);
    
    closeCategoryDialog();
  } catch (err: any) {
    console.error('创建分类失败:', err);
    error.value = err.response?.data?.error || '创建分类失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

const selectCategory = (category: Category) => {
  if (category.id !== selectedCategory.value?.id) {
    selectedCategory.value = category;
    fetchCategoryDetail(category.id);
  }
};

// 文件上传相关方法
const showUploadDialog = () => {
  if (!selectedCategory.value) {
    error.value = '请先选择一个分类';
    return;
  }
  isUploadDialogVisible.value = true;
  selectedFiles.value = [];
};

const closeUploadDialog = () => {
  isUploadDialogVisible.value = false;
  selectedFiles.value = [];
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    selectedFiles.value = [...selectedFiles.value, ...Array.from(input.files)];
  }
};

const handleFileDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files;
  if (files) {
    selectedFiles.value = [...selectedFiles.value, ...Array.from(files)];
  }
};

const removeSelectedFile = (index: number) => {
  selectedFiles.value.splice(index, 1);
};

const uploadFiles = async () => {
  if (!selectedFiles.value.length || !selectedCategory.value) return;
  
  isLoading.value = true;
  error.value = null;
  
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      error.value = '请先登录';
      return;
    }

    // 上传所有选中的文件
    const uploadPromises = selectedFiles.value.map(async (file) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('category', selectedCategory.value!.id.toString());
      formData.append('name', file.name);
      
      try {
        const response = await api.post('/knowledge/files/', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        
        // 开始检查向量化状态
        checkVectorizationStatus(response.data.id);
        
        return true;
      } catch (error) {
        console.error(`上传文件 ${file.name} 失败:`, error);
        return false;
      }
    });

    await Promise.all(uploadPromises);
    
    // 刷新当前分类详情（文件列表）
    if (selectedCategory.value) {
      fetchCategoryDetail(selectedCategory.value.id);
    }
    
    closeUploadDialog();
  } catch (err: any) {
    console.error('上传文件失败:', err);
    error.value = err.response?.data?.error || '上传文件失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 文件操作相关方法
const downloadFile = async (file: KnowledgeFile) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      error.value = '请先登录';
      return;
    }

    // 使用API获取文件下载链接
    const response = await api.get(`/knowledge/files/${file.id}/download/`, {
      responseType: 'blob'
    });
    
    // 创建下载链接并触发下载
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', file.name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (err: any) {
    console.error('下载文件失败:', err);
    error.value = err.response?.data?.error || '下载文件失败，请稍后重试';
  }
};

const deleteFile = async (file: KnowledgeFile) => {
  if (!confirm(`确定要删除文件 "${file.name}" 吗？`)) return;
  
  isLoading.value = true;
  error.value = null;
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      error.value = '请先登录';
      return;
    }

    await api.delete(`/knowledge/files/${file.id}/`);
    
    // 刷新当前分类详情（文件列表）
    if (selectedCategory.value) {
      fetchCategoryDetail(selectedCategory.value.id);
    }
  } catch (err: any) {
    console.error('删除文件失败:', err);
    error.value = err.response?.data?.error || '删除文件失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 工具方法
const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B';
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
  return (size / (1024 * 1024)).toFixed(1) + ' MB';
};

const formatDate = (date: string | number | Date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 添加向量信息相关方法
const showVectorInfo = (file: KnowledgeFile) => {
  selectedFile.value = file;
  showVectorInfoDialog.value = true;
};

const closeVectorInfoDialog = () => {
  showVectorInfoDialog.value = false;
  selectedFile.value = null;
};

// 获取向量化状态文本
const getVectorizationStatusText = (file: KnowledgeFile | null) => {
  if (!file) return '未知状态';
  
  switch (file.vectorization_status) {
    case 'completed':
      return '已向量化';
    case 'processing':
      return '向量化处理中';
    case 'pending':
      return '等待处理';
    case 'failed':
      return '处理失败';
    default:
      return '未知状态';
  }
};

// 显示向量化错误信息
const showVectorizationError = (file: KnowledgeFile) => {
  selectedFile.value = file;
  showErrorDialog.value = true;
};

// 关闭错误对话框
const closeErrorDialog = () => {
  showErrorDialog.value = false;
};

// 重试向量化处理
const retryVectorization = async () => {
  if (!selectedFile.value) return;
  
  try {
    isLoading.value = true;
    const fileId = selectedFile.value.id;
    
    // 调用API重试向量化
    await api.post(`/knowledge/files/${fileId}/retry-vectorization/`);
    
    // 更新文件状态
    selectedFile.value.vectorization_status = 'processing';
    
    // 刷新当前分类详情（文件列表）
    if (selectedCategory.value) {
      fetchCategoryDetail(selectedCategory.value.id);
    }
    
    closeErrorDialog();
    
    // 开始检查向量化状态
    checkVectorizationStatus(fileId);
  } catch (err: any) {
    console.error('重试向量化失败:', err);
    error.value = err.response?.data?.error || '重试向量化失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 在文件上传完成后轮询检查向量化状态时更新模型信息
const checkVectorizationStatus = async (fileId: number) => {
  try {
    let isComplete = false;
    let attempts = 0;
    const maxAttempts = 300; // 增加到300次，约10分钟(每2秒1次)
    let warningShown = false;
    
    while (!isComplete && attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 2000)); // 每2秒检查一次
      
      const response = await api.get(`/knowledge/files/${fileId}/`);
      const file = response.data;
      
      // 始终更新嵌入模型信息为最新值
      if (!file.embedding_model || 
          file.embedding_model === 'default' || 
          file.embedding_model === 'Pro/BAAI/bge-m3') {
        file.embedding_model = 'BAAI/bge-large-zh-v1.5';
      }
      
      if (file.vectorization_status === 'completed' || file.vectorization_status === 'failed') {
        isComplete = true;
        
        // 更新文件状态
        if (selectedCategory.value) {
          const fileIndex = selectedCategory.value.files?.findIndex(f => f.id === fileId) ?? -1;
          if (fileIndex !== -1 && selectedCategory.value.files) {
            selectedCategory.value.files[fileIndex] = file;
          }
        }
        
        // 如果处理失败，显示错误信息
        if (file.vectorization_status === 'failed') {
          console.error(`文件向量化失败: ${file.name}, 错误: ${file.vectorization_error}`);
          error.value = `文件 ${file.name} 向量化处理失败, 请查看错误详情`;
          
          // 自动显示错误对话框
          selectedFile.value = file;
          showErrorDialog.value = true;
        }
      } else if (attempts === 90 && !warningShown) { // 大约3分钟后显示处理中提示
        warningShown = true;
        console.warn(`向量化处理时间较长，文件ID: ${fileId}，仍在继续处理中...`);
        // 显示友好提示，但不是错误
        const warningMsg = `文件ID ${fileId} 向量化处理时间较长，系统仍在继续处理中...`;
        // 使用通知提示而不是错误提示
        if (typeof window.createNotification === 'function') {
          window.createNotification({
            title: '处理中',
            message: warningMsg,
            theme: 'info',
            duration: 5000
          });
        } else {
          console.log(warningMsg); // 降级到控制台日志
        }
      }
    }
    
    // 如果达到最大尝试次数但仍未完成
    if (attempts >= maxAttempts && !isComplete) {
      console.warn(`向量化处理时间超过预期(10分钟)，文件ID: ${fileId}`);
      
      // 尝试获取当前文件状态
      try {
        const response = await api.get(`/knowledge/files/${fileId}/`);
        const file = response.data;
        
        if (file.vectorization_status === 'processing') {
          error.value = `文件ID ${fileId} 向量化处理时间超过预期，但仍在处理中。请在知识库中查看处理状态，大型PDF文件可能需要更长时间`;
          
          // 更新文件状态
          if (selectedCategory.value) {
            const fileIndex = selectedCategory.value.files?.findIndex(f => f.id === fileId) ?? -1;
            if (fileIndex !== -1 && selectedCategory.value.files) {
              selectedCategory.value.files[fileIndex] = file;
            }
          }
        }
      } catch (err) {
        console.error('获取文件状态失败:', err);
        error.value = `文件ID ${fileId} 向量化处理时间超过预期，请在知识库中查看处理状态`;
      }
    }
  } catch (err) {
    console.error('检查向量化状态失败:', err);
  }
};

// 显示删除确认对话框
const confirmDeleteCategory = (category: Category) => {
  categoryToDelete.value = category;
  showDeleteDialog.value = true;
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  categoryToDelete.value = null;
};

// 删除分类
const deleteCategory = async () => {
  if (!categoryToDelete.value) return;
  
  isLoading.value = true;
  error.value = null;
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      error.value = '请先登录';
      return;
    }

    await api.delete(`/knowledge/categories/${categoryToDelete.value.id}/`);
    
    // 从列表中移除该分类
    categories.value = categories.value.filter(c => c.id !== categoryToDelete.value?.id);
    
    // 如果当前选中的是被删除的分类，清除选中状态
    if (selectedCategory.value?.id === categoryToDelete.value.id) {
      selectedCategory.value = null;
    }
    
    closeDeleteDialog();
  } catch (err: any) {
    console.error('删除分类失败:', err);
    error.value = err.response?.data?.error || '删除分类失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 页面加载时获取分类列表
onMounted(() => {
  fetchCategories();
});
</script> 