<template>
  <div class="llm-config-container">
    <div class="page-header">
      <h1>配置LLM</h1>
      <p>管理大语言模型(LLM)的连接配置</p>
    </div>
    
    <div class="config-layout">
      <!-- 左侧提供商列表 -->
      <div class="providers-panel">
        <div class="panel-header">
          <h2>LLM提供商</h2>
          <button class="btn btn-sm btn-primary" @click="showAddProviderModal">
            <i class="fa fa-plus"></i> 添加
          </button>
        </div>
        
        <div class="providers-list">
          <div 
            v-for="provider in providers" 
            :key="provider.id" 
            class="provider-item"
            :class="{ active: selectedProvider && provider.id === selectedProvider.id }"
            @click="selectProvider(provider)"
            @contextmenu.prevent="showContextMenu($event, provider)"
          >
            <div class="provider-logo" v-if="provider.logo">
              <img :src="provider.logo" :alt="provider.name">
            </div>
            <div class="provider-info">
              <h3>{{ provider.name }}</h3>
            </div>
          </div>

          <div class="empty-state" v-if="!providers.length">
            <p>暂无LLM提供商，请添加</p>
          </div>
        </div>
        
        <!-- 右键菜单 -->
        <div 
          v-if="contextMenuVisible" 
          class="context-menu"
          :style="{ top: contextMenuPosition.y + 'px', left: contextMenuPosition.x + 'px' }"
        >
          <div class="context-menu-item" @click="editContextProvider">
            <i class="fa fa-edit"></i> 编辑
          </div>
          <div class="context-menu-item delete" @click="confirmDeleteProvider">
            <i class="fa fa-trash"></i> 删除
          </div>
        </div>
      </div>

      <!-- 右侧模型配置 -->
      <div class="models-panel">
        <div class="panel-header" v-if="selectedProvider">
          <h2>{{ selectedProvider.name }}</h2>
          <button class="btn btn-sm btn-primary" @click="showAddModelModal">
            <i class="fa fa-plus"></i> 添加
          </button>
        </div>
        <div class="panel-placeholder" v-else>
          <p>请从左侧选择一名提供商</p>
        </div>

        <div class="models-list" v-if="selectedProvider">
          <div 
            v-for="model in providerModels" 
            :key="model.id" 
            class="model-card"
          >
            <div class="model-header">
              <h3 class="model-title" :title="model.model_name">{{ model.model_name }}</h3>
              <div class="model-actions-row">
                <button 
                  class="icon-btn test-icon" 
                  @click="testModelConnection(model)"
                  :disabled="testingConnection[model.id]"
                  title="测试连接"
                >
                  <i class="fas" :class="testingConnection[model.id] ? 'fa-spinner fa-spin' : 'fa-plug'"></i>
                </button>
                <button class="icon-btn edit-icon" @click="editModel(model)" title="编辑">
                  <i class="fas fa-pencil-alt"></i>
                </button>
                <button class="icon-btn delete-icon" @click="confirmDeleteModel(model)" title="删除">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </div>
            <div class="model-body">
              <div class="model-info">
                <div class="model-field">
                  <label>API端点:</label>
                  <p>{{ model.base_url }}</p>
                </div>
                <div class="model-field">
                  <label>API密钥:</label>
                  <p>{{ showApiKey ? model.api_key : maskApiKey(model.api_key) }}</p>
                </div>
                <div class="model-field" v-if="connectionStatus[model.id]">
                  <label>连接状态:</label>
                  <p class="connection-status" :class="connectionStatus[model.id].success ? 'success' : 'error'">
                    <i class="fa" :class="connectionStatus[model.id].success ? 'fa-check-circle' : 'fa-times-circle'"></i>
                    {{ connectionStatus[model.id].message }}
                    <span class="response-time" v-if="connectionStatus[model.id]?.response_time">
                      ({{ connectionStatus[model.id]?.response_time?.toFixed(2) }}s)
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="empty-state" v-if="!providerModels.length">
            <p>暂无LLM配置，请添加</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加提供商模态框 -->
    <div class="modal" v-if="showProviderModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingProvider ? '编辑提供商' : '添加提供商' }}</h3>
          <button class="close-btn" @click="closeProviderModal">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveProvider">
            <div class="form-group">
              <label for="provider_name">提供商名称</label>
              <input 
                type="text" 
                id="provider_name" 
                v-model="providerForm.name"
                class="form-control"
                placeholder="例如: OpenAI"
                required
              />
            </div>
            
            <div class="form-group">
              <label for="provider_logo">提供商Logo</label>
              <div class="logo-upload-container">
                <div 
                  class="logo-preview" 
                  v-if="logoPreview" 
                  :style="{ backgroundImage: `url(${logoPreview})` }"
                >
                  <button type="button" class="remove-logo" @click="removeLogo">
                    <i class="fa fa-times"></i>
                  </button>
                </div>
                <div class="logo-upload-button" v-if="!logoPreview">
                  <button type="button" class="btn btn-outline-secondary" @click="triggerLogoInput">
                    <i class="fa fa-upload"></i> 上传图标
                  </button>
                  <small class="form-text text-muted">支持.ico、.png、.jpg格式</small>
                </div>
                <input 
                  type="file" 
                  id="provider_logo" 
                  ref="logoInput"
                  accept=".ico,.png,.jpg,.jpeg"
                  style="display: none"
                  @change="handleLogoSelect"
                />
              </div>
            </div>
            
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="closeProviderModal">取消</button>
              <button type="submit" class="btn btn-primary" :disabled="isSaving">
                {{ isSaving ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- 添加模型模态框 -->
    <div class="modal" v-if="showModelModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingModel ? '编辑模型' : '添加模型' }}</h3>
          <button class="close-btn" @click="closeModelModal">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveModel">
            <div class="form-group">
              <label for="model_name">模型名称</label>
              <input 
                type="text" 
                id="model_name" 
                v-model="modelForm.model_name"
                class="form-control"
                placeholder="例如: gpt-4o"
                required
              />
            </div>
            
            <div class="form-group">
              <label for="base_url">API端点</label>
              <input 
                type="url" 
                id="base_url" 
                v-model="modelForm.base_url"
                class="form-control"
                placeholder="例如: https://api.openai.com/v1/chat/completions"
                required
                @input="validateApiEndpoint"
              />
              <small class="form-text" :class="{'text-muted': !apiEndpointError, 'text-danger': apiEndpointError}">
                {{ apiEndpointError || 'API端点必须是完整的URL，包含协议(https://)' }}
              </small>
            </div>
            
            <div class="form-group">
              <label for="api_key">API密钥</label>
              <div class="input-group">
                <input 
                  :type="showApiKey ? 'text' : 'password'" 
                  id="api_key" 
                  v-model="modelForm.api_key"
                  class="form-control"
                  :placeholder="editingModel ? '留空则保持现有密钥不变' : '输入API密钥'"
                  :required="!editingModel"
                />
                <div class="input-group-append">
                  <button 
                    type="button" 
                    class="api-key-toggle"
                    @click="toggleApiKeyVisibility"
                  >
                    <i class="fas" :class="showApiKey ? 'fa-eye-slash' : 'fa-eye'"></i>
                  </button>
                </div>
              </div>
              <small class="form-text text-muted" v-if="editingModel">
                留空则当前API密钥不变
              </small>
            </div>
            
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="closeModelModal">取消</button>
              <button 
                type="submit" 
                class="btn btn-primary" 
                :disabled="isSaving || (apiEndpointError !== '') || (!editingModel && !modelForm.api_key)"
              >
                {{ isSaving ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- 确认删除模态框 -->
    <div class="modal" v-if="showDeleteModal">
      <div class="modal-content modal-sm">
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="close-btn" @click="closeDeleteModal">&times;</button>
        </div>
        <div class="modal-body">
          <p>确定要删除此模型吗？此操作无法撤销。</p>
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" @click="closeDeleteModal">取消</button>
            <button type="button" class="btn btn-danger" @click="deleteModel" :disabled="isDeleting">
              {{ isDeleting ? '删除中...' : '确认删除' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 测试结果模态框 -->
    <div class="modal" v-if="showTestResultModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>连接测试结果</h3>
          <button class="close-btn" @click="closeTestResultModal">&times;</button>
        </div>
        <div class="modal-body">
          <div v-if="testResult">
            <div class="test-result-header" :class="testResult.success ? 'success' : 'error'">
              <div class="status-icon">
                <i class="fa" :class="testResult.success ? 'fa-check-circle' : 'fa-times-circle'"></i>
              </div>
              <div class="status-text">
                <h4>{{ testResult.success ? '连接成功' : '连接失败' }}</h4>
                <p>{{ testResult.message }}</p>
              </div>
            </div>
            
            <div class="test-details">
              <div class="detail-item">
                <label>状态码:</label>
                <span>{{ testResult.status_code || 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <label>响应时间:</label>
                <span>{{ testResult.response_time ? `${testResult.response_time.toFixed(2)}秒` : 'N/A' }}</span>
              </div>
              
              <div class="detail-item" v-if="testResult.model_response">
                <label>模型响应:</label>
                <pre class="model-response">{{ testResult.model_response }}</pre>
              </div>
              
              <div class="detail-item" v-if="testResult.response_data">
                <label>完整响应:</label>
                <div class="response-data-toggle" @click="toggleResponseData">
                  <i class="fa" :class="showResponseData ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                  {{ showResponseData ? '隐藏详情' : '显示详情' }}
                </div>
                <pre class="response-data" v-if="showResponseData">{{ JSON.stringify(testResult.response_data, null, 2) }}</pre>
              </div>
            </div>
          </div>
          
          <div v-else class="loading-state">
            <p>加载测试结果...</p>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-primary" @click="closeTestResultModal">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted, watch } from 'vue';
import { api } from '../services/api';
// @ts-ignore
import { showNotification } from '../utils/notifications.js';
import { getAllLLMModels, activateLLMModel, testLLMModelConnection } from '../services/llmService';
import '../styles/llm-config.css';  // 导入外部CSS文件

// 定义接口
interface Provider {
  id: number;
  name: string;
  logo?: string;
}

interface Model {
  id: number;
  model_name: string;
  base_url: string;
  api_key: string;
  provider: number;
}

interface ConnectionStatus {
  success: boolean;
  message: string;
  response_time?: number;
  timestamp?: Date;
}

interface TestResult {
  success: boolean;
  message: string;
  status_code?: number;
  response_time?: number;
  model_response?: string;
  response_data?: any;
}

// 状态变量
const loading = ref(true);
const isSaving = ref(false);
const isDeleting = ref(false);
const showApiKey = ref(false);
const logoInput = ref<HTMLInputElement | null>(null);
const logoPreview = ref('');
const logoFile = ref<File | null>(null);
const apiEndpointError = ref('');

// 数据模型
const providers = ref<Provider[]>([]);
const selectedProvider = ref<Provider | null>(null);
const providerModels = ref<Model[]>([]);

// 模态框控制
const showProviderModal = ref(false);
const showModelModal = ref(false);
const showDeleteModal = ref(false);
const editingProvider = ref<Provider | null>(null);
const editingModel = ref<Model | null>(null);
const modelToDelete = ref<Model | null>(null);

// 右键菜单相关
const contextMenuVisible = ref(false);
const contextMenuPosition = reactive({ x: 0, y: 0 });
const contextMenuProvider = ref<Provider | null>(null);

// 表单数据
const providerForm = reactive({
  name: ''
});

const modelForm = reactive({
  model_name: '',
  base_url: '',
  api_key: '',
  provider: null as number | null
});

// 模型测试连接状态
const testingConnection = reactive<Record<number, boolean>>({});
const connectionStatus = reactive<Record<number, ConnectionStatus>>({});
const showTestResultModal = ref(false);
const testResult = ref<TestResult | null>(null);
const showResponseData = ref(false);

// 获取提供商列表
async function fetchProviders() {
  try {
    loading.value = true;
    const response = await api.get('/llm/providers/');
    providers.value = response.data;
    
    if (providers.value.length > 0 && !selectedProvider.value) {
      selectProvider(providers.value[0]);
    }
  } catch (error: any) {
    console.error('获取LLM提供商失败:', error);
    showNotification('获取提供商列表失败，请稍后重试', 'error');
  } finally {
    loading.value = false;
  }
}

// 获取提供商模型
async function fetchProviderModels(providerId: number) {
  try {
    const response = await api.get(`/llm/providers/${providerId}/models/`);
    providerModels.value = response.data;
  } catch (error: any) {
    console.error('获取模型列表失败:', error);
    showNotification('获取模型列表失败，请稍后重试', 'error');
  }
}

// 选择提供商
function selectProvider(provider: Provider) {
  selectedProvider.value = provider;
  fetchProviderModels(provider.id);
}

// 添加提供商模态框
function showAddProviderModal() {
  editingProvider.value = null;
  providerForm.name = '';
  logoPreview.value = '';
  logoFile.value = null;
  showProviderModal.value = true;
}

// 编辑提供商
function editProvider(provider: Provider) {
  editingProvider.value = provider;
  providerForm.name = provider.name;
  logoPreview.value = provider.logo || '';
  logoFile.value = null;
  showProviderModal.value = true;
}

// 关闭提供商模态框
function closeProviderModal() {
  showProviderModal.value = false;
}

// 触发logo文件选择器
function triggerLogoInput() {
  if (logoInput.value) {
    logoInput.value.click();
  }
}

// 处理logo选择
function handleLogoSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    logoFile.value = file;
    logoPreview.value = URL.createObjectURL(file);
  }
}

// 移除已选择的logo
function removeLogo() {
  logoPreview.value = '';
  logoFile.value = null;
  if (logoInput.value) {
    logoInput.value.value = '';
  }
}

// 保存提供商
async function saveProvider() {
  try {
    isSaving.value = true;
    
    // 创建FormData对象用于文件上传
    const formData = new FormData();
    formData.append('name', providerForm.name);
    
    // 如果有选择新logo文件，添加到formData
    if (logoFile.value) {
      formData.append('logo', logoFile.value);
    }
    
    let response;
    
    if (editingProvider.value) {
      // 更新现有提供商
      response = await api.put(
        `/llm/providers/${editingProvider.value.id}/`, 
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' }}
      );
      // 更新提供商列表中的项
      const index = providers.value.findIndex(p => p.id === editingProvider.value?.id);
      if (index !== -1) {
        providers.value[index] = response.data;
      }
      // 如果当前选中的是被编辑的提供商，更新选中的提供商
      if (selectedProvider.value && selectedProvider.value.id === editingProvider.value.id) {
        selectedProvider.value = response.data;
      }
      showNotification('提供商更新成功', 'success');
    } else {
      // 创建新提供商
      response = await api.post(
        '/llm/providers/', 
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' }}
      );
      providers.value.push(response.data);
      showNotification('提供商添加成功', 'success');
    }
    
    closeProviderModal();
  } catch (error: any) {
    console.error('保存提供商失败:', error);
    showNotification('保存提供商失败: ' + (error.response?.data?.error || '未知错误'), 'error');
  } finally {
    isSaving.value = false;
  }
}

// 验证API端点
function validateApiEndpoint() {
  try {
    if (!modelForm.base_url) {
      apiEndpointError.value = '';
      return;
    }
    
    const url = new URL(modelForm.base_url);
    
    // 检查URL是否包含完整路径
    if (url.pathname === '/' || url.pathname === '') {
      apiEndpointError.value = 'API端点必须包含完整路径（例如：/v1/chat/completions）';
      return;
    }
    
    // 检查路径是否足够具体（至少包含2个路径段）
    const pathSegments = url.pathname.split('/').filter(segment => segment);
    if (pathSegments.length < 2) {
      apiEndpointError.value = 'API端点路径不够具体，请提供完整服务路径';
      return;
    }
    
    apiEndpointError.value = '';
  } catch (e) {
    apiEndpointError.value = '无效的URL格式，请输入完整的URL（例如：https://api.openai.com/v1/chat/completions）';
  }
}

// 添加模型模态框
function showAddModelModal() {
  if (!selectedProvider.value) return;
  
  editingModel.value = null;
  modelForm.model_name = '';
  modelForm.base_url = '';
  modelForm.api_key = '';
  modelForm.provider = selectedProvider.value.id;
  apiEndpointError.value = '';
  showModelModal.value = true;
}

// 编辑模型
function editModel(model: Model) {
  editingModel.value = model;
  modelForm.model_name = model.model_name;
  modelForm.base_url = model.base_url;
  modelForm.api_key = ''; // 不显示现有API密钥
  modelForm.provider = model.provider;
  apiEndpointError.value = '';
  showModelModal.value = true;
}

// 关闭模型模态框
function closeModelModal() {
  showModelModal.value = false;
}

// 保存模型
async function saveModel() {
  try {
    isSaving.value = true;
    
    // 验证API端点格式
    if (!validateURLFormat(modelForm.base_url)) {
      showNotification('无效的API端点URL格式', 'error');
      isSaving.value = false;
      return;
    }
    
    // 准备要提交的数据，明确复制所需字段
    const modelData: Partial<Model> & { provider?: number } = {
      provider: modelForm.provider === null ? undefined : modelForm.provider,
      model_name: modelForm.model_name,
      base_url: modelForm.base_url,
      // is_active: modelForm.is_active, // 如果需要传递 is_active
    };

    // 只有在提供了API密钥时才添加到请求中
    if (modelForm.api_key) {
      modelData.api_key = modelForm.api_key;
    }

    // 如果是更新，传递 id (后端 serializer 会处理)
    if (editingModel.value) {
      modelData.id = editingModel.value.id;
    }

    try { // 添加 try 块
      let response: any;
      if (editingModel.value && modelData.id) {
        // 更新现有模型
        // 后端 Serializer 应该处理 provider 字段，通常不允许通过 PUT 修改外键
        // 但如果 DRF 配置允许，可以传递 provider ID
        response = await api.put(`/llm/models/${modelData.id}/`, modelData);
        // 更新模型列表中的项
        const index = providerModels.value.findIndex(m => m.id === editingModel.value?.id);
        if (index !== -1) {
          // 保留API密钥的掩码显示
          const savedApiKey = providerModels.value[index].api_key;
          providerModels.value[index] = response.data;
          if (!modelForm.api_key) {
            providerModels.value[index].api_key = savedApiKey;
          }
        }
        showNotification('模型更新成功', 'success');
      } else {
        // 创建新模型
        // 检查 provider 是否已选择
        if (modelData.provider === undefined || modelData.provider === null) {
          showNotification('必须选择一个提供商', 'error');
          isSaving.value = false;
          return;
        }
        // 检查 API Key
        if (!modelData.api_key) {
          showNotification('添加新模型时必须提供API密钥', 'error');
          isSaving.value = false;
          return;
        }

        // 移除可能存在的 id 字段以防万一 (虽然上面逻辑应该避免了)
        delete modelData.id;
        
        response = await api.post('/llm/models/', modelData);
        providerModels.value.push(response.data);
        showNotification('模型添加成功', 'success');
      }
      
      // 成功后关闭模态框
      closeModelModal();
    } catch (error: any) {
      console.error('保存模型失败:', error);
      
      // 根据错误类型显示不同信息
      if (error.response?.status === 500) {
        // 尝试从错误中提取更多信息
        let errorMsg = '服务器内部错误';
        if (error.response?.data?.error) {
          errorMsg += `: ${error.response.data.error}`;
        } else if (error.response?.data?.message) {
          errorMsg += `: ${error.response.data.message}`;
        } else if (error.response?.data?.detail) {
          errorMsg += `: ${error.response.data.detail}`;
        }
        
        // 检查是否为model_id相关错误
        if (errorMsg.includes('model_id') || errorMsg.includes('模型ID')) {
          errorMsg = '保存成功，但环境变量更新失败。您的更改已保存到数据库。';
          // 如果是编辑操作，刷新模型列表
          if (editingModel.value && selectedProvider.value) {
            await fetchProviderModels(selectedProvider.value.id);
          }
          showNotification(errorMsg, 'warning');
          closeModelModal();
          return;
        }
        
        showNotification(errorMsg, 'error');
      } else if (error.response?.status === 400) {
        // 处理验证错误
        const errorData = error.response.data;
        let errorMsg = '保存失败: ';
        
        if (typeof errorData === 'object' && errorData !== null) {
          // 遍历所有错误字段并组合错误信息
          const errorFields = Object.keys(errorData).filter(key => Array.isArray(errorData[key]) || typeof errorData[key] === 'string');
          if (errorFields.length > 0) {
            errorMsg += errorFields.map(field => {
              const fieldErrors = Array.isArray(errorData[field]) ? errorData[field].join(', ') : errorData[field];
              return `${field}: ${fieldErrors}`;
            }).join('; ');
          } else {
            errorMsg += errorData.error || errorData.detail || JSON.stringify(errorData);
          }
        } else {
          errorMsg += '服务器返回了格式不正确的错误信息';
        }
        
        showNotification(errorMsg, 'error');
      } else {
        showNotification('保存模型失败: ' + (error.response?.data?.error || error.message || '未知错误'), 'error');
      }
    }
  } catch (error: any) {
    console.error('处理保存模型操作失败:', error);
    showNotification('操作失败: ' + (error.message || '未知错误'), 'error');
  } finally {
    isSaving.value = false;
  }
}

// 验证URL格式
function validateURLFormat(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

// 确认删除模型
function confirmDeleteModel(model: Model) {
  modelToDelete.value = model;
  showDeleteModal.value = true;
}

// 关闭删除确认框
function closeDeleteModal() {
  showDeleteModal.value = false;
  modelToDelete.value = null;
}

// 删除模型
async function deleteModel() {
  if (!modelToDelete.value) return;
  
  try {
    isDeleting.value = true;
    await api.delete(`/llm/models/${modelToDelete.value.id}/`);
    
    // 从列表中移除已删除的模型
    providerModels.value = providerModels.value.filter(m => m.id !== modelToDelete.value?.id);
    
    showNotification('模型已成功删除', 'success');
    closeDeleteModal();
  } catch (error: any) {
    console.error('删除模型失败:', error);
    showNotification('删除模型失败: ' + (error.response?.data?.error || '未知错误'), 'error');
  } finally {
    isDeleting.value = false;
  }
}

// 掩码显示API密钥
function maskApiKey(apiKey: string): string {
  if (!apiKey) return '不可见';
  if (apiKey.length <= 8) {
    return '*'.repeat(apiKey.length);
  }
  return apiKey.substring(0, 4) + '*'.repeat(apiKey.length - 8) + apiKey.substring(apiKey.length - 4);
}

// 切换API密钥可见性
function toggleApiKeyVisibility() {
  showApiKey.value = !showApiKey.value;
}

// 显示右键菜单
function showContextMenu(event: MouseEvent, provider: Provider) {
  // 设置菜单位置，考虑边界情况
  contextMenuPosition.x = Math.min(event.clientX, window.innerWidth - 120);
  contextMenuPosition.y = Math.min(event.clientY, window.innerHeight - 80);
  contextMenuProvider.value = provider;
  contextMenuVisible.value = true;
  
  // 添加全局点击事件监听，点击菜单外部时关闭菜单
  setTimeout(() => {
    document.addEventListener('click', hideContextMenu);
  }, 0);
}

// 编辑右键菜单选中的提供商
function editContextProvider() {
  if (contextMenuProvider.value) {
    editProvider(contextMenuProvider.value);
    closeContextMenu();
  }
}

// 确认删除右键菜单选中的提供商
function confirmDeleteProvider() {
  if (!contextMenuProvider.value) return;
  
  if (confirm(`确定要删除提供商"${contextMenuProvider.value.name}"吗？此操作将同时删除该提供商下的所有模型！`)) {
    deleteProvider(contextMenuProvider.value.id);
  }
  
  closeContextMenu();
}

// 删除提供商
async function deleteProvider(providerId: number) {
  try {
    isDeleting.value = true;
    await api.delete(`/llm/providers/${providerId}/`);
    
    // 从列表中移除已删除的提供商
    providers.value = providers.value.filter(p => p.id !== providerId);
    
    // 如果删除的是当前选中的提供商，清空选中状态
    if (selectedProvider.value && selectedProvider.value.id === providerId) {
      selectedProvider.value = null;
      providerModels.value = [];
    }
    
    showNotification('提供商已成功删除', 'success');
  } catch (error: any) {
    console.error('删除提供商失败:', error);
    showNotification('删除提供商失败: ' + (error.response?.data?.error || '未知错误'), 'error');
  } finally {
    isDeleting.value = false;
  }
}

// 隐藏右键菜单（点击外部时）
function hideContextMenu() {
  closeContextMenu();
  document.removeEventListener('click', hideContextMenu);
}

// 关闭右键菜单
function closeContextMenu() {
  contextMenuVisible.value = false;
  contextMenuProvider.value = null;
}

// 获取当前活跃的模型
async function fetchActiveModel() {
  try {
    const response = await api.get('/llm/config/');
    // 只记录活跃模型，不标记界面
    console.log('当前活跃模型:', response.data?.model?.model_name || '无');
  } catch (error: any) {
    console.error('获取当前活跃模型失败:', error);
  }
}

// 测试模型连接
async function testModelConnection(model: Model) {
  try {
    // 设置测试中状态
    testingConnection[model.id] = true;
    
    // 测试连接
    const result = await testLLMModelConnection(model.id);
    
    // 更新状态
    connectionStatus[model.id] = {
      success: result.success,
      message: result.message,
      response_time: result.response_time,
      timestamp: new Date()
    };
    
    // 显示通知
    if (result.success) {
      showNotification(`连接测试成功: ${result.message}`, 'success');
    } else {
      showNotification(`连接测试失败: ${result.message}`, 'error');
    }
    
    // 显示详细测试结果
    testResult.value = result;
    showTestResultModal.value = true;
  } catch (error: any) {
    console.error('测试连接失败:', error);
    
    // 更新状态为失败
    connectionStatus[model.id] = {
      success: false,
      message: error.message || '测试连接时发生错误',
      timestamp: new Date()
    };
    
    showNotification('测试连接失败: ' + error.message, 'error');
  } finally {
    // 清除测试中状态
    testingConnection[model.id] = false;
  }
}

// 关闭测试结果模态框
function closeTestResultModal() {
  showTestResultModal.value = false;
  showResponseData.value = false;
}

// 切换是否显示详细响应数据
function toggleResponseData() {
  showResponseData.value = !showResponseData.value;
}

// 初始化
onMounted(() => {
  fetchProviders();
  fetchActiveModel();
});

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu);
});
</script>