<template>
  <div class="odds-page">    
    <div class="odds-container" :class="{ 'with-analysis': showAnalysis }">
      <OddsDisplay
        :match="match"
        :odds="odds"
        :loading="loading"
        :error="error"
        :analyzing="analyzing"
        :hasExistingReport="hasExistingReport"
        :showAnalysis="showAnalysis"
        :expandedDrawers="expandedDrawers"
        :matchId="matchId"
        @toggle-analysis="toggleAnalysis"
        @toggle-drawer="toggleDrawer"
        @go-back="goBack"
      />
      
      <div class="analysis-container" v-if="showAnalysis">
        <OddsAnalysis
          :analyzing="analyzing"
          :analysis-result="analysisResult"
          :analysis-error="analysisError"
          :has-existing-report="!!analysisResult"
          :is-paused="isPaused"
          :match-id="matchId"
          @analyze-odds="analyzeOdds"
          @pause-analysis="pauseAnalysis"
          @stop-analysis="handleStopAnalysis"
          @update-analysis-result="updateAnalysisResult"
        />
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import OddsDisplay from '../components/OddsDisplay.vue'
import OddsAnalysis from '../components/OddsAnalysis.vue'
import { getHistoricalAnalysis } from '../services/llmService' // Assuming getHistoricalAnalysis is still needed
import '../styles/odds.css' // 导入外部CSS文件
  
const route = useRoute()
const router = useRouter()
  
// 状态管理
const matchId = ref(route.params.id as string)
const match = ref<any>(null)
const odds = ref<any[]>([])
const loading = ref(true)
const error = ref<string | undefined>(undefined)

// 分析相关
const analyzing = ref(false)
const analysisResult = ref<any>(null)
const analysisError = ref<string | undefined>(undefined)
const hasExistingReport = ref(false)
const isPaused = ref(false) // Note: Pause/Resume might not work well with standard EventSource
const showAnalysis = ref(false)

// 新增：用于存储流式接收的完整内容
let streamedAnalysisContent = '';

// 新增：用于管理流式连接
let eventSource: EventSource | null = null;

// 赔率展示相关
const expandedDrawers = ref<Record<string, boolean>>({})

// 新增：用于前端节流的变量
let accumulatedChunk = '';
let updateTimeoutId: number | null = null;
const RENDER_INTERVAL = 150; // 更新 UI 的最小间隔 (毫秒)，可以调整

// 新增：用于确保所有累积的 chunk 被渲染的辅助函数
// const flushAccumulatedChunk = () => { // <--- 移除或注释掉整个函数
//    console.log('执行刷新累积内容操作');
//    
//    if (updateTimeoutId !== null) {
//        console.log('清除正在等待的更新计时器');
//        clearTimeout(updateTimeoutId); // 清除等待中的更新
//        updateTimeoutId = null;
//    }
//    
//    // 如果有累积的 chunk，立即追加到结果中
//    if (accumulatedChunk && analysisResult.value) {
//        console.log('刷新累积内容到UI，长度:', accumulatedChunk.length);
//        
//        // 重要修改：创建新对象以触发Vue的响应式更新
//        const newAnalysis = analysisResult.value.analysis + accumulatedChunk;
//        analysisResult.value = {
//            ...analysisResult.value,
//            analysis: newAnalysis
//        };
//        
//        // 强制DOM更新后执行滚动等操作
//        setTimeout(() => {
//            const contentEl = document.querySelector('.analysis-content');
//            if (contentEl) {
//                // 滚动到底部
//                contentEl.scrollTop = contentEl.scrollHeight;
//            }
//        }, 50);
//    } else {
//        console.log('没有需要刷新的累积内容');
//    }
//    
//    accumulatedChunk = ''; // 清空累积变量
// } // <--- 移除或注释掉整个函数

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId && newId !== matchId.value) {
    matchId.value = newId as string
    handleStopAnalysis(); // 停止任何正在进行的分析或流
    loadMatchData()
  }
})

// 获取比赛和赔率数据
const loadMatchData = async () => {
  loading.value = true
  error.value = undefined
  analysisResult.value = null; // Clear previous analysis on new match load
  hasExistingReport.value = false;
  
  try {
    console.log(`正在加载比赛ID为 ${matchId.value} 的数据...`);
    const token = localStorage.getItem('token');
    const headers: HeadersInit = {}; // Linter fix: Initialize as HeadersInit
    if (token) {
        headers['Authorization'] = `Bearer ${token}`; // Linter fix: Add property if token exists
    }
    
    // 加载比赛信息
    const matchResponse = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/matches/${matchId.value}/`, { headers })
    
    if (!matchResponse.ok) {
      throw new Error(`获取比赛信息失败: ${matchResponse.status}`);
    }
    
    const matchData = await matchResponse.json();
    if (!matchData || !matchData.id) {
      throw new Error('服务器返回的比赛数据无效');
    }
    
    match.value = matchData;
    console.log('成功加载比赛数据:', matchData);
    
    // 加载赔率信息
    const oddsResponse = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/odds/?match_id=${matchId.value}`, { headers })
    
    if (!oddsResponse.ok) {
      throw new Error(`获取赔率信息失败: ${oddsResponse.status}`);
    }
    
    const oddsData = await oddsResponse.json()
    
    // 从返回的分页数据结构中提取赔率结果数组
    odds.value = oddsData.results || []
    console.log(`成功加载 ${odds.value.length} 条赔率数据`);
    
    // 检查是否已有分析报告
    await checkExistingAnalysis()
  } catch (err: any) {
    console.error('加载数据失败:', err)
    error.value = err.message || '加载数据失败'
    match.value = null;
    odds.value = [];
  } finally {
    loading.value = false
  }
}
  
// 检查是否已有分析报告
const checkExistingAnalysis = async () => {
  try {
    console.log(`检查比赛ID ${matchId.value} 是否有历史分析报告...`);
    const history = await getHistoricalAnalysis(matchId.value); // Use service

    if (history && history.length > 0) {
       console.log(`发现 ${history.length} 条历史报告，将使用最新一条`);
       // Sort history (newest first) - Assuming service returns sorted or sort here
       history.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
       analysisResult.value = history[0]; // Show the latest by default
        hasExistingReport.value = true;
        
       // If URL requests it, show the panel
        if (route.query.showReport === 'true') {
          showAnalysis.value = true;
      }
          } else {
       console.log('未找到历史分析报告');
      analysisResult.value = null;
              hasExistingReport.value = false;
    }
  } catch (err: any) {
     console.error('检查或加载历史分析报告失败:', err);
     analysisResult.value = null;
            hasExistingReport.value = false;
     // Optionally set error.value = '无法加载历史报告';
  }
}

// 修改后的分析赔率函数
const analyzeOdds = async (payload: any) => {
  handleStopAnalysis(); // Stop any previous analysis/stream
  analysisError.value = undefined;
  analyzing.value = true;
  isPaused.value = false; // Reset pause state
  showAnalysis.value = true; // Ensure panel is open

  // 如果不是继续生成，清空当前显示的结果，准备接收新内容
  if (!payload?.continue_analysis) {
    analysisResult.value = {
      match_id: matchId.value,
      analysis: '', // Start empty for streaming
      used_knowledge: [],
      // other fields will be populated by stream/response
    };
  } else {
     // For continuation, keep existing 'used_knowledge' etc. but clear 'analysis'
     if(analysisResult.value) {
         analysisResult.value.analysis = '';
     } else {
         // If somehow analysisResult is null during continuation, initialize it
         analysisResult.value = {
             match_id: matchId.value,
             analysis: '',
             used_knowledge: payload.previous_report?.used_knowledge || [],
             // ... potentially copy other relevant fields from previous_report
         };
     }
  }


  const token = localStorage.getItem('token');
  if (!token) {
    alert('请先登录');
    router.push('/auth?redirect=' + encodeURIComponent(route.fullPath));
    analyzing.value = false;
    return;
  }

  const headers: HeadersInit = {}; // Linter fix
  headers['Content-Type'] = 'application/json'; // Needed for non-stream POST
  if (token) {
      headers['Authorization'] = `Bearer ${token}`; // Linter fix
  }

  // --- 根据 payload.stream 决定调用哪个 API ---
  if (payload?.stream === true) {
    // --- 调用流式 API ---
    console.log('使用流式 API 进行分析...');
    const sessionId = Date.now().toString(); // Simple session ID for this stream request
    
    // 构建流式URL
    // 将 payload 中的参数序列化为查询字符串
    const queryParams = new URLSearchParams();
    for (const key in payload) {
      if (payload.hasOwnProperty(key) && payload[key] !== undefined && payload[key] !== null) {
        // 对 previous_report 特殊处理，只传递 id
        if (key === 'previous_report' && typeof payload[key] === 'object' && payload[key] !== null) {
          queryParams.append('base_report_id', payload[key].id);
        } else if (key !== 'previous_report' && key !== 'stream') { // 排除对象和 stream 标志
           queryParams.append(key, String(payload[key]));
        }
      }
    }
    // 添加 session_id
    queryParams.append('session_id', sessionId);
    
    const streamUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/llm/analysis/match/${matchId.value}/stream/?${queryParams.toString()}`;
    console.log('流式API URL (含参数):', streamUrl);
    
    // 设置明确的UI状态以指示流式分析
    analyzing.value = true;
    document.body.classList.add('streaming-active');

    try {
      // 关闭可能存在的旧连接
      if (eventSource) {
        console.log('关闭现有的EventSource连接');
        eventSource.close();
        eventSource = null; // 确保设为null
      }

      // **关键改动: 确保初始化时analysisResult.value是空的**
      console.log('初始化一个空的analysisResult，准备接收流式数据');
      analysisResult.value = {
        match_id: matchId.value,
        analysis: '', // 确保从空字符串开始
        used_knowledge: [],
        elapsed_time: 0,
      };
      
      // **改动: 重置错误状态**
      analysisError.value = undefined;
      
      // **改动: 创建EventSource前先设置analyzing状态**
      analyzing.value = true;
      
      // 创建新的 EventSource 连接
      console.log('创建新的EventSource连接:', streamUrl);      
      
      // 先清空flow-container内容，确保不会出现重复内容
      setTimeout(() => {
        const flowContainer = document.querySelector('.flow-container');
        if (flowContainer) {
          flowContainer.innerHTML = '';
          console.log('已清空流容器内容，准备接收新内容');
        }
      }, 0);
      
      eventSource = new EventSource(streamUrl, { 
        withCredentials: true 
      });
      console.log('EventSource已创建:', eventSource);

      // 绑定事件监听器前先定义一些标记变量
      let hasReceivedAnyEvent = false;
      let lastEventTime = Date.now();
      
      // 添加定时器检查连接健康
      const connectionCheckInterval = setInterval(() => {
        if (eventSource && analyzing.value) {
          const now = Date.now();
          console.log(`连接检查: 自上次事件已过去 ${Math.floor((now - lastEventTime)/1000)} 秒`);
          
          // 如果超过30秒没有收到任何事件，且仍处于分析状态，输出警告
          if (now - lastEventTime > 30000) {
            console.warn('警告: 超过30秒没有收到任何事件，连接可能已断开');
          }
        } else {
          // 如果不再分析，清除检查定时器
          clearInterval(connectionCheckInterval);
        }
      }, 5000); // 每5秒检查一次

      // **改动: 增加连接打开事件处理**
      eventSource.onopen = (event) => {
        console.log('EventSource连接已成功建立', event);
        hasReceivedAnyEvent = true;
        lastEventTime = Date.now();
      };

      // **改动: 增加消息事件处理（捕获未指定类型的事件）**
      eventSource.onmessage = (event) => {
        console.log('收到未指定类型的消息事件:', event.data);
        hasReceivedAnyEvent = true;
        lastEventTime = Date.now();
      };

      // Start事件处理
      eventSource.addEventListener('start', (event) => {
        try {
          console.log('收到start事件:', event.data);
          hasReceivedAnyEvent = true;
          lastEventTime = Date.now();
          
          const data = JSON.parse(event.data);
          // 在开始时处理元数据
          if(analysisResult.value) {
            if(data.match_info) analysisResult.value.match_info = data.match_info;
            if(data.used_knowledge) analysisResult.value.used_knowledge = data.used_knowledge;
            console.log('已更新分析元数据');
          }
        } catch (e) {
          console.error("解析start事件数据失败:", e, event.data);
        }
      });

      // Chunk事件处理 - 关键修改
      eventSource.addEventListener('chunk', (event) => {
        try {
          hasReceivedAnyEvent = true;
          lastEventTime = Date.now();

          console.log('收到chunk事件, 数据长度:', event.data.length);
          const data = JSON.parse(event.data);

          if (data.analysis !== undefined) {
            // 1. 只累积内容到临时变量
            streamedAnalysisContent += data.analysis;
            console.log(`累积内容，当前总长度: ${streamedAnalysisContent.length}`);

            // 2. 更新 Vue Ref (仍然需要，以便其他地方能获取到最新状态)
            //   但是这个更新不直接用于最终显示，最终显示依赖 done 事件
            if (analysisResult.value) {
               analysisResult.value = {
                  ...analysisResult.value,
                  analysis: streamedAnalysisContent // 用累积的内容更新
               };
            }

            // 3. 移除直接 DOM 操作
            // setTimeout(() => { ... contentEl.appendChild(span); ... }, 0); // <--- 移除这部分

          } else {
            console.log('收到的chunk没有analysis字段:', data);
          }
        } catch (e) {
          console.error('解析chunk数据失败:', e, event.data);
        }
      });

      // Done事件处理 - 关键修改
      eventSource.addEventListener('done', (event) => {
        console.log('收到done事件，分析完成:', event.data);
        hasReceivedAnyEvent = true;
        lastEventTime = Date.now();

        // 更新状态
        analyzing.value = false;
        document.body.classList.remove('streaming-active');

        try {
          const finalData = JSON.parse(event.data);
          console.log('分析完成，最终数据:', finalData);

          // **关键修复**: 使用 done 事件中的完整分析结果覆盖 analysisResult
          if (analysisResult.value && finalData.analysis !== undefined) {
            console.log(`设置最终分析结果，长度: ${finalData.analysis.length}`);
            analysisResult.value = {
                ...analysisResult.value, // 保留之前的元数据如 used_knowledge
                analysis: finalData.analysis, // 使用 finalData 中的完整分析
                elapsed_time: finalData.elapsed_time // 更新耗时
            };
            // 确保 DOM 更新后再滚动
            nextTick(() => {
                const contentEl = document.querySelector('.analysis-content'); // 目标容器可能需要调整
                 if (contentEl) {
                   contentEl.scrollTop = contentEl.scrollHeight;
                   console.log('已滚动到底部');
                }
            });

          } else if (!analysisResult.value) {
               console.warn("analysisResult 为 null，无法设置最终分析结果");
               // 可以考虑创建一个新的 analysisResult 对象
               analysisResult.value = {
                  match_id: matchId.value,
                   analysis: finalData.analysis || streamedAnalysisContent, // Fallback to accumulated if final is empty
                   used_knowledge: finalData.used_knowledge || [],
                   elapsed_time: finalData.elapsed_time
               };
               nextTick(() => { // 同样需要滚动
                  const contentEl = document.querySelector('.analysis-content');
                   if (contentEl) {
                       contentEl.scrollTop = contentEl.scrollHeight;
                   }
               });
          } else {
               console.warn("Done 事件数据中缺少 analysis 字段，使用累积内容");
               // 如果 done 事件没有 analysis，则使用累积的内容作为最终结果
                analysisResult.value = {
                    ...analysisResult.value,
                    analysis: streamedAnalysisContent // 使用累积内容
                };
                nextTick(() => { // 同样需要滚动
                   const contentEl = document.querySelector('.analysis-content');
                    if (contentEl) {
                        contentEl.scrollTop = contentEl.scrollHeight;
                    }
                });
          }

          // 设置报告状态
          hasExistingReport.value = true;

          // 保存结果
          if (analysisResult.value && analysisResult.value.analysis) {
            console.log('保存最终分析结果，内容长度:', analysisResult.value.analysis.length);
            sessionStorage.setItem(`analysis_${matchId.value}`, JSON.stringify(analysisResult.value));
          }
        } catch(e) {
          console.error("解析done事件数据失败:", e, event.data);
          analysisError.value = "处理分析结果时出错"; // 提供错误反馈
          // 即使解析失败，也尝试使用累积的内容
           if(analysisResult.value) {
               analysisResult.value.analysis = streamedAnalysisContent;
           }
          hasExistingReport.value = true; // 兜底
        } finally {
          // 清理资源
          streamedAnalysisContent = ''; // 清空临时变量
          // clearInterval(connectionCheckInterval); // connectionCheckInterval 定义在 stream 作用域外，不能在这里清除
          if (eventSource) {
            console.log('关闭EventSource连接');
            eventSource.close();
            eventSource = null;
          }
        }
      });

      // Error事件处理
      eventSource.addEventListener('error', (event) => {
        hasReceivedAnyEvent = true;
        lastEventTime = Date.now();
        
        console.error('收到error事件:', event);
        
        let errorMessage = '分析过程中发生错误';
        
        try {
          if ((event as any).data) {
            const errorData = JSON.parse((event as any).data);
            console.error('错误数据:', errorData);
            
            if (errorData.error) {
              errorMessage = `分析错误: ${errorData.error}`;
            }
          }
        } catch (e) {
          console.error('解析error事件数据失败:', e);
        }
        
        // 设置错误状态
        analysisError.value = errorMessage;
        analyzing.value = false;
        document.body.classList.remove('streaming-active');
        
        // 清理资源
        // clearInterval(connectionCheckInterval);
      });

      // 网络错误处理
      eventSource.onerror = (err) => {
        console.error('EventSource连接错误:', err);
        
        // 5秒后如果没有收到任何事件，判定为连接失败
        if (!hasReceivedAnyEvent) {
          setTimeout(() => {
            if (!hasReceivedAnyEvent && analyzing.value) {
              console.error('连接没有收到任何事件，判定为失败');
              analysisError.value = '无法建立与分析服务器的连接';
              analyzing.value = false;
              
              if (eventSource) {
                console.log('超时后关闭EventSource连接');
                eventSource.close();
                eventSource = null;
              }
            }
          }, 5000);
        }
        
        // 区分正常关闭和异常
        if (eventSource) {
          if (eventSource.readyState === EventSource.CLOSED) {
            console.log("EventSource已关闭");
            
            // 只有在仍处于分析状态且未收到done事件时才设置错误
            if (analyzing.value) {
              analysisError.value = '与分析服务器的连接意外断开';
              analyzing.value = false;
            }
          } else if (eventSource.readyState === EventSource.CONNECTING) {
            console.log("EventSource正在尝试重新连接...");
          } else {
            console.error("未知的EventSource错误");
            analysisError.value = '与分析服务器的连接发生错误';
            analyzing.value = false;
          }
        }
        
        // 清理资源
        // clearInterval(connectionCheckInterval);
      };

    } catch (err: any) {
      console.error('启动流式分析失败:', err);
      analysisError.value = err.message || '启动流式分析失败';
      analyzing.value = false;
      if (eventSource) eventSource.close();
      eventSource = null;
    }

  } else {
    // --- 调用非流式 API ---
    console.log('使用非流式 API 进行分析...');
    try {
      const analysisUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/llm/analysis/analyze-odds/${matchId.value}/`;
      // Prepare extra_data, excluding the 'stream' flag itself
      const nonStreamPayload = { ...payload };
      delete nonStreamPayload.stream;
      
      // --- 修复：将 nonStreamPayload 的内容正确嵌套在 extra_data 中 ---
      const extraOddsData = odds.value ? arrangeOddsByBookmaker(odds.value) : {};
      const actualExtraData = {
        bookmakers: extraOddsData,
        match_info: match.value,
        // 将 nonStreamPayload 的内容（包括 generation_type, base_report_id 等）放到这里
        ...nonStreamPayload 
      };

      console.log('非流式分析已开始，请等待结果...');
      analyzing.value = true; // 明确设置状态

      const analysisResponse = await fetch(analysisUrl, {
        method: 'POST',
        headers: headers, // Includes Content-Type and Authorization
        body: JSON.stringify({ 
            model: (await getCurrentLlmModelId(token)), // Get active model
            // 传递正确结构的 extra_data
            extra_data: actualExtraData 
        })
      });
      
      if (!analysisResponse.ok) {
        const errorData = await analysisResponse.json().catch(() => ({ error: '无法解析错误信息' }));
        throw new Error(`分析赔率失败: ${analysisResponse.status} - ${errorData.error || '未知错误'}`);
      }
      
      const result = await analysisResponse.json();
      analysisResult.value = result;
      hasExistingReport.value = true;
      if (result.analysis) {
          sessionStorage.setItem(`analysis_${matchId.value}`, JSON.stringify(result));
      }
      console.log('非流式分析完成');
    } catch (err: any) {
      console.error('启动非流式分析失败:', err);
      analysisError.value = err.message || '分析初始化失败';
    } finally {
      analyzing.value = false;
    }
  }
}

// Helper to get current active LLM model ID
async function getCurrentLlmModelId(token: string | null): Promise<string | null> {
    if (!token) return null;
     try {
         const modelResponse = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/llm/models/active/`, {
             headers: { 'Authorization': `Bearer ${token}` } // Need auth here too
         });
         if (!modelResponse.ok) {
             console.error("获取激活模型失败:", modelResponse.status);
             return null;
         }
         const modelData = await modelResponse.json();
         return modelData.model_name; // Assuming backend returns model_name
     } catch(err) {
         console.error("获取激活模型时出错:", err);
         return null;
  }
}

// 辅助函数：将赔率按照博彩公司分组
const arrangeOddsByBookmaker = (oddsList: any[]) => {
  const bookmakerNames: { [key: string]: string } = { '281': 'Bet365', '82': '立博', '115': '威廉希尔' };
  const groupedOdds: { [key: string]: any[] } = {};
  oddsList.forEach(odd => {
    const bookmakerKey = odd.bookmaker_id || 'unknown';
    if (!groupedOdds[bookmakerKey]) groupedOdds[bookmakerKey] = [];
    groupedOdds[bookmakerKey].push({
      ...odd,
      bookmaker_name: bookmakerNames[bookmakerKey] || `未知(${bookmakerKey})`
    });
  });
  Object.keys(groupedOdds).forEach(key => {
    groupedOdds[key].sort((a, b) => new Date(b.update_time).getTime() - new Date(a.update_time).getTime());
  });
  return groupedOdds;
}

// 暂停分析 (在流式模式下通常不可靠，可以尝试发送控制信号到后端，如果后端支持)
const pauseAnalysis = () => {
  if (eventSource) {
      console.log('暂停/恢复流式分析 (UI 状态切换)');
      // Real pause requires backend support via an API call
      isPaused.value = !isPaused.value;
  } else {
      console.log('暂停非流式分析 (无效操作)');
  }
};

// 修改后的停止分析处理
const handleStopAnalysis = () => {
  console.log('收到停止分析事件');
  // flushAccumulatedChunk(); // 不再需要
  streamedAnalysisContent = ''; // 清空临时变量
  analyzing.value = false;
  isPaused.value = false;

  // 关闭 EventSource 连接
  if (eventSource) {
    console.log('正在关闭 EventSource 连接...');
    eventSource.close();
    eventSource = null;
  }

  // 恢复上一次的完整报告
  const savedAnalysis = sessionStorage.getItem(`analysis_${matchId.value}`);
  if (savedAnalysis && !analyzing.value) { // Only restore if not currently analyzing something new
    try {
      analysisResult.value = JSON.parse(savedAnalysis);
      hasExistingReport.value = true;
      console.log('已恢复上次保存的分析结果');
    } catch (e) {
      console.error('解析 sessionStorage 中的分析结果失败:', e);
      analysisResult.value = null;
      hasExistingReport.value = false;
    }
  } else if (!analyzing.value) { // If not analyzing and nothing saved, clear it
    analysisResult.value = null;
    hasExistingReport.value = false; 
    console.log('未找到上次保存的分析结果，显示初始状态');
  }
  // If analyzing was true but stopped, we don't restore potentially incomplete stream results
};

// 切换分析面板
const toggleAnalysis = () => {
  showAnalysis.value = !showAnalysis.value
  // If opening and we have a report flag but no data, try loading history
  if (showAnalysis.value && hasExistingReport.value && !analysisResult.value) {
     checkExistingAnalysis();
  }
}

// 切换赔率历史抽屉
const toggleDrawer = (name: string) => {
  expandedDrawers.value[name] = !expandedDrawers.value[name]
}

// 返回上一页
const goBack = () => {
   handleStopAnalysis(); // Stop any analysis before navigating away
   // Only save if we have a result, it's considered existing, and we are NOT analyzing
   if (analysisResult.value && hasExistingReport.value && !analyzing.value) {
       try {
         sessionStorage.setItem(`analysis_${matchId.value}`, JSON.stringify(analysisResult.value));
         console.log('已将最终分析结果保存到会话存储');
       } catch (e) {
          console.error("保存到sessionStorage失败:", e);
       }
  }
  router.push('/match')
}

// 组件挂载时加载数据
onMounted(() => {
  if (matchId.value) {
    loadMatchData()
  }
  
  // 检查流式输出设置
  const streamingPreference = localStorage.getItem('useStreamingOutput')
  console.log(`当前流式输出偏好设置: ${streamingPreference !== 'false' ? '开启' : '关闭'}`)
})

// 格式化比赛日期时间，包含星期
const formatMatchDateTime = (timeString: string | undefined | null): string => {
  if (!timeString) return '暂无数据';
   try {
  const date = new Date(timeString);
        if (isNaN(date.getTime())) { // Check for invalid date
            console.warn("无效日期字符串:", timeString);
            return '无效日期格式';
        }
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const weekDay = weekDays[date.getDay()];
        // Format parts reliably
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes} ${weekDay}`;
   } catch (e) {
        console.error("日期格式化错误:", e, "原始字符串:", timeString);
        return '日期格式错误'; // Return error indicator
   }
};

const updateAnalysisResult = (result: any) => {
  if (!result) return
  console.log('更新分析结果 (通过 emit):', result) // Keep for debugging if child emits
  analysisResult.value = result
  hasExistingReport.value = !!result.analysis // Update based on received result
  if (result.match_id && result.analysis && !analyzing.value) { // Only save complete results
    sessionStorage.setItem(`analysis_${result.match_id}`, JSON.stringify(result))
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  console.log('组件卸载，清理资源');
  // flushAccumulatedChunk(); // 不再需要
  streamedAnalysisContent = ''; // 清空临时变量
  handleStopAnalysis();
});

// 添加CSS样式，提高用户体验
const css = document.createElement('style');
css.type = 'text/css';
css.innerHTML = `
  .analyzing-indicator {
    margin-top: 20px;
    text-align: center;
    color: #6366f1;
  }
  
  .analyzing-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(99, 102, 241, 0.3);
    border-radius: 50%;
    border-top-color: #6366f1;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;
document.head.appendChild(css);

// 加载分析报告的简单封装函数 - 修复引用问题
const loadExistingAnalysis = async (matchId: string) => {
  try {
        const history = await getHistoricalAnalysis(matchId); // Use service
        if (history && history.length > 0) {
            history.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
            const latestReport = history[0];
             // Add formatted match time to the loaded report if match data is available
             if (match.value && match.value.match_time && !latestReport.display_match_time) {
                 latestReport.display_match_time = formatMatchDateTime(match.value.match_time);
             }
            return latestReport;
        }
        return null;
    } catch (err) {
        console.error('加载历史分析报告失败:', err);
    return null;
  }
}
</script> 