<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1>用户档案</h1>
      <p>自定义个人信息及偏好设置</p>
    </div>

    <div class="profile-card">
      <!-- 头像和个人基本信息 -->
      <div class="profile-avatar-section">
        <div class="profile-avatar-wrapper">
          <div class="profile-avatar">
            <img v-if="userAvatar" :src="userAvatar" alt="用户头像" />
            <div v-else class="avatar-placeholder">{{ userInitials }}</div>
          </div>
          <button class="change-avatar-btn" @click="openAvatarUpload">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
              <circle cx="12" cy="13" r="4"></circle>
            </svg>
            更换头像
          </button>
          <input type="file" ref="fileInput" style="display: none" accept="image/*" @change="handleAvatarChange" />
        </div>
        <div class="profile-info">
          <h2>{{ userName }}</h2>
          <p class="user-email">{{ userEmail }}</p>
          <p class="join-date">加入时间: {{ joinDate }}</p>
          <p class="last-login" v-if="lastLogin">最后登录: {{ lastLogin }}</p>
          <p class="last-updated" v-if="lastUpdated">最后修改: {{ lastUpdated }}</p>
        </div>
      </div>

      <!-- 表单部分 -->
      <div class="profile-form">
        <h3 class="section-title">个人信息</h3>

        <div class="form-group">
          <label for="username">昵称</label>
          <div class="form-input-wrapper">
            <input
              id="username"
              v-model="userForm.username"
              type="text"
              placeholder="请填写你的昵称"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="email">邮箱</label>
          <div class="form-input-wrapper">
            <input
              id="email"
              v-model="userForm.email"
              type="email"
              placeholder="请填写你的邮箱"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="bio">说说</label>
          <div class="form-input-wrapper">
            <textarea
              id="bio"
              v-model="userForm.bio"
              rows="3"
              placeholder="请说说你想说的"
            ></textarea>
          </div>
        </div>

        <h3 class="section-title">通知设置</h3>

        <div class="form-switch">
          <label for="match-notifications">比赛开始通知</label>
          <label class="switch">
            <input type="checkbox" id="match-notifications" v-model="userForm.notifications.match">
            <span class="slider"></span>
          </label>
        </div>

        <div class="form-switch">
          <label for="odds-notifications">赔率变化通知</label>
          <label class="switch">
            <input type="checkbox" id="odds-notifications" v-model="userForm.notifications.odds">
            <span class="slider"></span>
          </label>
        </div>

        <div class="form-switch">
          <label for="analysis-notifications">新分析通知</label>
          <label class="switch">
            <input type="checkbox" id="analysis-notifications" v-model="userForm.notifications.analysis">
            <span class="slider"></span>
          </label>
        </div>

        <div class="form-actions">
          <button type="button" class="cancel-btn" @click="resetForm">取消</button>
          <button type="button" class="save-btn" @click="saveProfile" :disabled="isSaving">
            {{ isSaving ? '保存中...' : '保存更改' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 提示消息 -->
    <transition name="fade">
      <div v-if="showMessage" :class="['message', messageType]">
        {{ message }}
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { api } from '../services/api';
import '../styles/profile.css'; // 导入外部CSS文件

// 用户数据
const userName = ref('');
const userEmail = ref('');
const userAvatar = ref('');
const joinDate = ref('');
const lastLogin = ref('');
const lastUpdated = ref('');

// 表单数据
const userForm = reactive({
  username: '',
  email: '',
  bio: '',
  notifications: {
    match: true,
    odds: true,
    analysis: false
  }
});

// 状态变量
const isSaving = ref(false);
const fileInput = ref(null);
const showMessage = ref(false);
const message = ref('');
const messageType = ref('success');

// 计算用户名首字母
const userInitials = computed(() => {
  if (!userName.value) return '';
  return userName.value.charAt(0).toUpperCase();
});

// 格式化日期函数
function formatDate(dateString) {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 获取用户信息
async function fetchUserData() {
  try {
    // 获取基本用户信息
    const userResponse = await api.get('/auth/user/');
    userName.value = userResponse.data.username;
    userEmail.value = userResponse.data.email;
    userForm.username = userResponse.data.username;
    userForm.email = userResponse.data.email;

    // 格式化加入日期 - 使用用户创建时间
    joinDate.value = formatDate(userResponse.data.date_joined);

    // 格式化最后登录时间
    if (userResponse.data.last_login) {
      lastLogin.value = formatDate(userResponse.data.last_login);
    }

    // 获取用户档案信息
    const profileResponse = await api.get('/auth/profile/');
    if (profileResponse.data) {
      // 设置头像
      userAvatar.value = profileResponse.data.avatar || '';

      // 设置简介
      userForm.bio = profileResponse.data.bio || '';

      // 设置通知偏好
      userForm.notifications.match = profileResponse.data.notification_match;
      userForm.notifications.odds = profileResponse.data.notification_odds;
      userForm.notifications.analysis = profileResponse.data.notification_analysis;

      // 格式化最后修改时间
      if (profileResponse.data.updated_at) {
        lastUpdated.value = formatDate(profileResponse.data.updated_at);
      }
    }
  } catch (error) {
    console.error('获取用户数据失败:', error);
    showNotification('获取用户数据失败，请稍后重试', 'error');
  }
}

// 保存个人资料
async function saveProfile() {
  isSaving.value = true;

  try {
    // 构建提交的数据
    const profileData = {
      bio: userForm.bio,
      notification_match: userForm.notifications.match,
      notification_odds: userForm.notifications.odds,
      notification_analysis: userForm.notifications.analysis,
      email: userForm.email,
      username: userForm.username
    };

    // 发送API请求更新档案
    const response = await api.put('/auth/profile/update/', profileData);

    // 更新本地状态
    userName.value = userForm.username;
    userEmail.value = userForm.email;

    // 保存到localStorage，确保其他组件可以获取最新的值
    localStorage.setItem('email', userForm.email);
    localStorage.setItem('username', userForm.username);

    // 显示成功消息
    showNotification('个人资料已成功更新', 'success');

    // 触发自定义事件，通知其他组件用户信息已更新
    window.dispatchEvent(new CustomEvent('user-profile-updated', {
      detail: {
        username: userName.value,
        email: userEmail.value,
        avatar: userAvatar.value
      }
    }));
  } catch (error) {
    console.error('保存个人资料失败:', error);
    showNotification('保存失败，请稍后重试', 'error');
  } finally {
    isSaving.value = false;
  }
}

// 重置表单
function resetForm() {
  userForm.username = userName.value;
  userForm.email = userEmail.value;

  // 从API获取档案数据重置其他字段
  fetchUserData();

  showNotification('表单已重置', 'info');
}

// 打开头像上传对话框
function openAvatarUpload() {
  fileInput.value.click();
}

// 处理头像变更
function handleAvatarChange(event) {
  const file = event.target.files[0];
  if (!file) return;

  if (!file.type.match('image.*')) {
    showNotification('请选择图片文件', 'error');
    return;
  }

  const reader = new FileReader();
  reader.onload = async (e) => {
    const imageData = e.target.result;
    userAvatar.value = imageData;

    try {
      // 发送API请求更新头像
      await api.put('/auth/profile/update/', {
        avatar: imageData
      });

      // 触发头像更新事件
      window.dispatchEvent(new CustomEvent('user-profile-updated', {
        detail: {
          username: userName.value,
          email: userEmail.value,
          avatar: imageData
        }
      }));

      showNotification('头像已更新', 'success');
    } catch (error) {
      console.error('更新头像失败:', error);
      showNotification('更新头像失败，请稍后重试', 'error');
    }
  };
  reader.readAsDataURL(file);
}

// 显示通知
function showNotification(msg, type = 'success') {
  message.value = msg;
  messageType.value = type;
  showMessage.value = true;

  setTimeout(() => {
    showMessage.value = false;
  }, 3000);
}

// 生命周期钩子
onMounted(() => {
  fetchUserData();
});
</script>