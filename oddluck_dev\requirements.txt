# basic dependencies
Django==4.2.10
djangorestframework==3.15.0
django-cors-headers==4.3.1
django-filter==24.2
djangorestframework-simplejwt
python-dotenv==1.0.1
whitenoise==6.6.0
gunicorn==21.2.0
requests==2.31.0
pytz
django-eventstream==5.3.2
django-apscheduler==0.7.0

# system and monitoring dependencies
psutil==5.9.8

# database dependencies
psycopg2-binary==2.9.9
pgvector==0.2.5
SQLAlchemy==2.0.40

# data validation and processing dependencies
pydantic==2.6.1
python-multipart==0.0.9
pandas

# test tool dependencies
pytest==8.0.0
pytest-django==4.8.0

# LLM related dependencies
langchain==0.1.12
langchain-core==0.1.52
langchain-community==0.0.38
langchain-text-splitters==0.0.2
langchain_openai==0.0.2
openai>=1.6.1
tiktoken>=0.5.2,<0.6.0
transformers==4.38.2
torch==2.2.1 

# document processing dependencies
python-docx
PyPDF2
openpyxl
ebooklib
bs4

# vector retrieval and embedding model dependencies
sentence-transformers>=3.0.0
faiss-cpu

# server(ASGI)
Daphne==4.1.2