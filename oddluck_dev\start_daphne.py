#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ODDLUCK项目启动脚本
自动启动前端和后端开发服务器
"""

import os
import sys
import time
import signal
import subprocess
import platform
import psutil
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent

# 虚拟环境路径
VENV_PATH = os.path.join(BASE_DIR, "oddluck_venv")
if platform.system() == "Windows":
    PYTHON_PATH = os.path.join(VENV_PATH, "Scripts", "python.exe")
    ACTIVATE_PATH = os.path.join(VENV_PATH, "Scripts", "activate")
else:
    PYTHON_PATH = os.path.join(VENV_PATH, "bin", "python")
    ACTIVATE_PATH = os.path.join(VENV_PATH, "bin", "activate")

# 前端和后端目录
FRONTEND_DIR = os.path.join(BASE_DIR, "frontend")
BACKEND_DIR = os.path.join(BASE_DIR, "backend")

# 子进程列表
processes = []

def is_venv():
    """检查是否在虚拟环境中运行"""
    return (hasattr(sys, 'real_prefix') or
            (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))

def activate_venv():
    """激活虚拟环境"""
    if not is_venv():
        if platform.system() == "Windows":
            # Windows下使用批处理命令激活虚拟环境
            cmd = f'powershell -Command "& {ACTIVATE_PATH}"'
            os.system(cmd)
        else:
            # Linux/macOS下使用source命令激活虚拟环境
            cmd = f'source {ACTIVATE_PATH}'
            os.system(cmd)
        print(f"已激活虚拟环境: {VENV_PATH}")
    else:
        print("已在虚拟环境中运行")

def check_fix_ssl_settings():
    """检查并修复SSL设置"""
    print("检查SSL设置...")
    
    # 默认设置，对本地数据库禁用SSL
    env_file = os.path.join(BASE_DIR, ".env")
    if os.path.exists(env_file):
        # 直接设置环境变量
        db_host = os.environ.get('DB_HOST', 'localhost')
        if db_host == 'localhost' or db_host == '127.0.0.1':
            os.environ['SSL_MODE'] = 'disable'
            print("检测到本地数据库连接，设置SSL_MODE=disable")
        else:
            os.environ['SSL_MODE'] = 'require'
            print("检测到云数据库连接，设置SSL_MODE=require")
        print(f"SSL模式: {os.environ.get('SSL_MODE')}")
    else:
        print("未找到.env文件，使用默认SSL设置")

def start_backend():
    """启动Django后端服务器 (使用 Daphne)"""
    print("启动后端服务器 (使用 Daphne)...")
    # 不需要 check_fix_ssl_settings()

    # 构建 Daphne 命令
    # 注意：需要确保 PYTHON_PATH 指向的虚拟环境中有 daphne
    daphne_executable = ""
    if platform.system() == "Windows":
        daphne_executable = os.path.join(VENV_PATH, "Scripts", "daphne.exe")
    else:
        daphne_executable = os.path.join(VENV_PATH, "bin", "daphne")

    if not os.path.exists(daphne_executable):
        print(f"错误: 找不到 Daphne 可执行文件: {daphne_executable}")
        print("请确保已在虚拟环境中运行 'pip install daphne'")
        # 可以选择退出或回退到 runserver
        return # 或者 sys.exit(1)

    # -b 127.0.0.1
    backend_cmd_list = [daphne_executable, '-b', '127.0.0.1', 'backend.asgi:application']
    print(f"执行命令: {' '.join(backend_cmd_list)} in {BACKEND_DIR}")

    # 使用 Popen 启动 Daphne
    # cwd=BACKEND_DIR 确保 Daphne 在正确的目录下查找 asgi.py
    backend_proc = subprocess.Popen(backend_cmd_list, cwd=BACKEND_DIR)

    processes.append(backend_proc)
    print("后端服务器 (Daphne) 已启动，监听地址: 127.0.0.1:8000")

def start_frontend():
    """启动Vue.js前端开发服务器"""
    print("启动前端开发服务器...")
    if platform.system() == "Windows":
        # Windows下启动Vue.js前端
        frontend_cmd = f'cd "{FRONTEND_DIR}" && npm run dev'
        frontend_proc = subprocess.Popen(frontend_cmd, shell=True)
    else:
        # Linux/macOS下启动Vue.js前端
        frontend_cmd = ['npm', 'run', 'dev']
        frontend_proc = subprocess.Popen(frontend_cmd, cwd=FRONTEND_DIR)
    
    processes.append(frontend_proc)
    print("前端开发服务器已启动")

def kill_on_port(port):
    """终止指定端口上运行的进程"""
    try:
        # 获取所有进程
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 获取该进程的所有连接
                connections = proc.connections()
                for conn in connections:
                    if hasattr(conn, 'laddr') and conn.laddr.port == port:
                        print(f"终止端口 {port} 上的进程 (PID: {proc.pid})")
                        os.kill(proc.pid, signal.SIGTERM)
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except Exception as e:
        print(f"尝试终止端口 {port} 上的进程时出错: {str(e)}")

def cleanup():
    """清理所有子进程"""
    print("\n正在关闭服务器...")
    
    # 终止启动的所有子进程
    for proc in processes:
        try:
            if platform.system() == "Windows":
                proc.terminate()
            else:
                os.killpg(os.getpgid(proc.pid), signal.SIGTERM)
        except Exception as e:
            print(f"终止进程时出错: {str(e)}")
    
    # 尝试终止特定端口上的进程
    try:
        kill_on_port(8000)  # Django后端
        kill_on_port(5173)  # Vite开发服务器
    except Exception as e:
        print(f"终止端口进程时出错: {str(e)}")
    
    print("所有服务器已关闭")

def check_prerequisites():
    """检查运行环境依赖"""
    # 检查虚拟环境
    if not os.path.exists(VENV_PATH):
        print(f"错误: 虚拟环境不存在 ({VENV_PATH})")
        print("请先创建虚拟环境，然后再运行此脚本")
        return False
    
    # 检查前端目录
    if not os.path.exists(os.path.join(FRONTEND_DIR, "package.json")):
        print(f"错误: 前端目录不完整，缺少package.json")
        return False
    
    # 检查后端目录
    if not os.path.exists(os.path.join(BACKEND_DIR, "manage.py")):
        print(f"错误: 后端目录不完整，缺少manage.py")
        return False
    
    return True

def main():
    """主函数"""
    print("="*50)
    print("ODDLUCK项目启动工具")
    print("="*50)
    
    # 检查先决条件
    if not check_prerequisites():
        sys.exit(1)
    
    try:
        # 激活虚拟环境
        activate_venv()
        
        # 启动后端服务器
        start_backend()
        
        # 等待后端启动
        time.sleep(2)
        
        # 启动前端服务器
        start_frontend()
        
        print("\n所有服务已启动。按Ctrl+C可停止所有服务。")
        
        # 等待用户中断
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        # 用户按下Ctrl+C
        pass
    finally:
        # 清理资源
        cleanup()

if __name__ == "__main__":
    main() 