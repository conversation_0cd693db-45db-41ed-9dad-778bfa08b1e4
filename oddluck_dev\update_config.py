#!/usr/bin/env python
"""
配置同步脚本 - 将后端.env文件中的配置同步到前端
"""
import os
import re
from pathlib import Path
from dotenv import load_dotenv

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent

# 加载环境变量
env_file = os.path.join(BASE_DIR, '.env')
load_dotenv(env_file)

# 前端环境变量文件
frontend_env = os.path.join(BASE_DIR, 'frontend', '.env')

# 需要同步到前端的环境变量映射
VARS_MAPPING = {
    'LLM_MODEL_NAME': 'VITE_LLM_MODEL_NAME',
    'LLM_BASE_URL': 'VITE_LLM_BASE_URL',
    'EMBEDDING_MODEL': 'VITE_EMBEDDING_MODEL',
    'EMBEDDING_TYPE': 'VITE_EMBEDDING_TYPE',
    'EMBEDDING_DEVICE': 'VITE_EMBEDDING_DEVICE'
    # 下面这些配置已不再使用，但保留映射关系以便配置同步时可以注释掉
    # 'EMBEDDING_BASE_URL': 'VITE_EMBEDDING_BASE_URL',
    # 'EMBEDDING_API_KEY': 'VITE_EMBEDDING_API_KEY'
}

def update_frontend_env():
    """更新前端环境变量文件"""
    # 读取现有的前端.env文件
    existing_vars = {}
    if os.path.exists(frontend_env):
        with open(frontend_env, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    existing_vars[key.strip()] = value.strip()

    # 更新环境变量
    has_changes = False
    for backend_var, frontend_var in VARS_MAPPING.items():
        value = os.environ.get(backend_var)
        if value:
            if frontend_var not in existing_vars or existing_vars[frontend_var] != value:
                existing_vars[frontend_var] = value
                has_changes = True

    # 如果不存在API URL，设置默认值
    if 'VITE_API_URL' not in existing_vars:
        existing_vars['VITE_API_URL'] = 'http://localhost:8000/api'
        has_changes = True

    # 只有在有变更时才写入文件
    if has_changes:
        # 写入更新后的环境变量文件
        with open(frontend_env, 'w', encoding='utf-8') as f:
            f.write("# 自动从后端.env同步的环境变量\n")
            for key, value in existing_vars.items():
                f.write(f"{key}={value}\n")

        print(f"前端环境变量已更新: {frontend_env}")
    else:
        print("前端环境变量无变化，跳过更新")

    return has_changes

if __name__ == "__main__":
    update_frontend_env() 